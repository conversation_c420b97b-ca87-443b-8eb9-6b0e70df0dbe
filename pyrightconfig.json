{
  "include": [
    "apps/**",
    "packages/**"
  ],
  "exclude": [
    "**/__pycache__",
    "**/.pytest_cache",
    "**/node_modules",
    "**/.venv",
    "**/venv",
    "**/.git",
    "**/dist",
    "**/*.egg-info"
  ],
  "extraPaths": [
    "packages/ccontrol-common/src",
    "packages/ccontrol-ai-service/src", 
    "packages/ccontrol-test-engine/src",
    "apps/ccontrol-test-engine-service/src"
  ],
  "pythonVersion": "3.9",
  "pythonPlatform": "All",
  "typeCheckingMode": "basic",
}
