[project]
name = "ccontrol-test-engine-monorepo"
version = "0.3.28"
description = "CControl Test Engine Monorepo"
authors = [{ name = "lian<PERSON><PERSON><PERSON>", email = "lian<PERSON><PERSON><PERSON>@cvte.com" }]
requires-python = "<4,>=3.9"
readme = "README.md"
license = "MIT"
dependencies = [
  "ccontrol-common",
  "ccontrol-ai-service",
  "ccontrol-test-engine",
  "ccontrol-test-engine-service",
]

[tool.setuptools]
packages = []

[tool.uv.workspace]
members = ["apps/*", "packages/*"]

[tool.uv.sources]
ccontrol-common = { workspace = true }
ccontrol-ai-service = { workspace = true }
ccontrol-test-engine = { workspace = true }
ccontrol-test-engine-service = { workspace = true }

[tool.ruff]
line-length = 255

[tool.ruff.lint]
select = ["E", "F"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
  "pre-commit>=3.7.1,<4.0.0",
  "ruff>=0.8.2,<1.0.0",
  "build>=1.2.1,<2.0.0",
  "pytest>=8.2.1,<9.0.0",
  "pylint>=3.3.3",
  "pylint-pytest>=1.1.7",
  "tomli>=2.2.1",
  "typer[all]>=0.15.1",
  "rich>=13.9.4",
  "roboflow>=1.1.53",
]

[tool.hatch.build]
dev-mode-dirs = ["apps", "packages"]

[tool.hatch.build.targets.wheel]
only-include = ["apps", "packages"]
sources = ["apps", "packages"]

[project.scripts]
pypi = "scripts.pypi:app"


[[tool.uv.index]]
name = "cvte-snapshot"
url = "https://artifactory.gz.cvte.cn/artifactory/api/pypi/pypi-local"
publish-url = "https://artifactory.gz.cvte.cn/artifactory/api/pypi/pypi-local"
explicit = true

[[tool.uv.index]]
name = "cvte-release"
url = "https://artifactory.gz.cvte.cn/artifactory/api/pypi/pypi"
publish-url = "https://artifactory.gz.cvte.cn/artifactory/api/pypi/pypi"
explicit = true
