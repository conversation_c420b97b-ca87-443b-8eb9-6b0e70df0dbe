# CControl Test Engine Monorepo

## 项目结构

```
.
├── apps/
│   └── ccontrol-test-engine-service/  # 测试引擎服务
├── packages/
│   ├── ccontrol-common/              # 通用库
│   ├── ccontrol-test-engine/         # 测试引擎核心
│   └── ccontrol-ai-service/          # AI 服务
```

## 开发环境设置

1. 创建虚拟环境：
   ```bash
   uv venv
   source .venv/bin/activate
   ```

2. 安装依赖：
   ```bash
   uv sync --dev
   ```

## 版本管理

所有包共用根目录 pyproject.toml 中的版本号。版本号格式：
- 正式版本：`0.1.13`
- 开发版本：
  * Alpha: `0.1.13-alpha.1`
  * Beta: `0.1.13-beta.1`
  * RC: `0.1.13-rc.1`

## 发布流程

1. 配置 PyPI 源：
   ```bash
   # 复制模板并编辑
   cp .pypirc.template ~/.pypirc
   # 设置认证 token
   export SNAPSHOT_PYPI_TOKEN="your-token"
   export RELEASE_PYPI_TOKEN="your-token"
   ```

2. 发布包：
   ```bash
   # 发布所有包到 snapshot
   python scripts/publish.py publish-all snapshot
   
   # 发布单个包到 release
   python scripts/publish.py publish-single release ccontrol-common
   ```

## 开发工具

- 代码格式化：使用 ruff 进行代码格式化和 lint
- 依赖管理：使用 UV 管理依赖，自动生成 requirements.txt
- 测试：使用 pytest 进行单元测试

## 贡献指南

1. 克隆仓库
2. 创建新分支
3. 提交更改
4. 运行测试
5. 提交 PR
