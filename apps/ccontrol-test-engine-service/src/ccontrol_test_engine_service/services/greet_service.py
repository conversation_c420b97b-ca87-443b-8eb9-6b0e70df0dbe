from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class HelloReq(BaseModel):
    message: str = Field(..., description="问候语消息")
    language: str = Field(default="zh-CN", description="语言")
    type: str = Field(
        default="normal", description="问候类型", examples=["morning/afternoon/evening"]
    )


class HelloResp(BaseModel):
    id: int = Field(..., description="问候语ID")
    message: str = Field(..., description="问候语消息")
    language: str = Field(..., description="语言")
    type: str = Field(..., description="问候类型")
    status: bool = Field(default=True, description="状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    class Config:
        json_encoders = {datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S")}


# Mock数据
MOCK_GREETINGS = [
    HelloResp(
        id=1,
        message="早上好！",
        language="zh-CN",
        type="morning",
        status=True,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    ),
    HelloResp(
        id=2,
        message="Good morning!",
        language="en-US",
        type="morning",
        status=True,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    ),
    HelloResp(
        id=3,
        message="こんにちは",
        language="ja-JP",
        type="afternoon",
        status=True,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    ),
    HelloResp(
        id=4,
        message="晚上好！",
        language="zh-CN",
        type="evening",
        status=False,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    ),
]


class GreetingService:
    def __init__(self):
        self._greetings = {g.id: g for g in MOCK_GREETINGS}
        self._id_counter = len(MOCK_GREETINGS)

    def post_hello(self, dto: HelloReq) -> HelloResp:
        """创建新的问候语"""
        self._id_counter += 1
        new_greeting = HelloResp(id=self._id_counter, **dto.dict())
        self._greetings[new_greeting.id] = new_greeting
        return new_greeting

    def get_greeting_by_id(self, greeting_id: int) -> HelloResp:
        """获取指定ID的问候语"""
        if greeting_id not in self._greetings:
            raise ValueError(f"问候语ID {greeting_id} 不存在")
        return self._greetings[greeting_id]

    def list_greetings(self, page: int = 1, size: int = 10) -> List[HelloResp]:
        """获取问候语列表"""
        greetings = list(self._greetings.values())
        start = (page - 1) * size
        end = start + size
        return greetings[start:end]

    def update_greeting(self, greeting_id: int, dto: HelloReq) -> HelloResp:
        """更新问候语"""
        if greeting_id not in self._greetings:
            raise ValueError(f"问候语ID {greeting_id} 不存在")

        greeting = self._greetings[greeting_id]
        update_data = dto.dict(exclude_unset=True)
        update_data["updated_at"] = datetime.now()

        updated_greeting = greeting.copy(update=update_data)
        self._greetings[greeting_id] = updated_greeting
        return updated_greeting

    def delete_greeting(self, greeting_id: int) -> None:
        """删除问候语"""
        if greeting_id not in self._greetings:
            raise ValueError(f"问候语ID {greeting_id} 不存在")
        del self._greetings[greeting_id]

    def update_greeting_status(self, greeting_id: int, status: bool) -> None:
        """更新问候语状态"""
        if greeting_id not in self._greetings:
            raise ValueError(f"问候语ID {greeting_id} 不存在")

        greeting = self._greetings[greeting_id]
        updated_greeting = greeting.copy(
            update={"status": status, "updated_at": datetime.now()}
        )
        self._greetings[greeting_id] = updated_greeting

    def search_greetings(
        self,
        keyword: Optional[str] = None,
        language: Optional[str] = None,
        page: int = 1,
        size: int = 10,
    ) -> List[HelloResp]:
        """搜索问候语"""
        results = list(self._greetings.values())

        if keyword:
            results = [g for g in results if keyword.lower() in g.message.lower()]

        if language:
            results = [g for g in results if g.language.lower() == language.lower()]

        start = (page - 1) * size
        end = start + size
        return results[start:end]


# 创建服务实例
greet_service = GreetingService()
