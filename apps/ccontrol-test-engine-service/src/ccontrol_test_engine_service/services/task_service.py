from typing import Union, Optional

from ccontrol_common.core.log import log, LogTree
from ccontrol_common.core.errors import RequestError, ServerError
from ccontrol_test_engine import (
    get_device_info,
    GlobalVarModel,
    TaskNodeModel,
    TestSuiteNodeModel,
    TaskRunner,
)
from ccontrol_test_engine_service.model.task.bo_task_request import (
    CreateSingleDeviceTaskBo,
    CreateMultiDeviceTaskBo,
)
from ccontrol_test_engine_service.utils.task_instance_manager import task_manager


class TaskBuilder:
    """任务构建器 - 专注于构建任务相关对象"""

    def __init__(self, task_request: Union[CreateSingleDeviceTaskBo, CreateMultiDeviceTaskBo]):
        self.task_request = task_request
        self.task_id = task_request.task_tree.task_id
        self.log_root: Optional[LogTree] = None
        self.task_node: Optional[TaskNodeModel] = None
        self.test_suite: Optional[TestSuiteNodeModel] = None
        self.global_var: Optional[GlobalVarModel] = None
        self.task_runner: Optional[TaskRunner] = None
        self._is_built = False

    def build(self) -> "TaskBuilder":
        """构建完整的任务对象"""
        self._build_task_tree()
        self._build_global_var()
        self._build_task_runner()
        self._is_built = True
        return self

    def _build_task_tree(self):
        """构建任务树"""
        self.task_node = self.task_request.task_tree.to_task_node()
        self.test_suite = self.task_request.task_tree.children

    def _build_global_var(self):
        """构建全局变量"""
        if isinstance(self.task_request, CreateMultiDeviceTaskBo):
            raise RequestError("Multi device tasks are not supported yet")

        device_id = self.task_request.device.device_id
        device_info = get_device_info(device_id)
        if not device_info:
            raise RequestError(f"device: {device_id} not found")
        self.global_var = GlobalVarModel(device=device_info, agent=device_info.agent)
        # 注入用户全局变量
        req_user_global_var = self.task_request.config.user_global_var
        if isinstance(req_user_global_var, dict):
            self.global_var.user_global_var.update(req_user_global_var)

    def _build_task_runner(self):
        """构建任务执行器"""
        if self.global_var is None or self.task_node is None:
            raise RequestError("Task builder missing required components")

        self.task_runner = TaskRunner(
            global_var=self.global_var,
            task_data=self.task_node,
            on_task_complete=task_manager.complete_task,
        )

    def get_task_components(self) -> tuple[str, TaskNodeModel, LogTree, TaskRunner]:
        """获取构建好的任务组件"""
        if not self._is_built:
            raise RequestError("Must call build() before getting components")

        if not self.task_node or not self.log_root or not self.task_runner:
            raise RequestError("Task components not fully built")

        return self.task_id, self.task_node, self.log_root, self.task_runner


class TaskService:
    """任务服务"""

    def __init__(self):
        self.log = log

    def create_task(self, bo: Union[CreateSingleDeviceTaskBo, CreateMultiDeviceTaskBo]):
        """创建任务"""
        try:
            task_id = bo.task_tree.task_id

            # 首先创建任务实例
            task_manager.create_task(task_id)

            # 初始化日志
            log_handler = log.add_task_handler(task_id=task_id)
            log_root = LogTree(
                name=task_id,
                description=f"task {task_id} root log",
                task_id=task_id,
            )
            task_manager.add_log_handler(task_id, log_handler)
            task_manager.add_log_tree(task_id, log_root)
            self.log = log_root.logger

            self.log.info(f"create task {task_id}".center(60, "="))

            # 使用构建器创建任务
            builder = TaskBuilder(bo)
            builder.log_root = log_root
            builder.build()

            # 获取任务组件并注册到管理器
            task_id, task_node, log_root, task_runner = builder.get_task_components()
            task_manager.add_task_node(task_id, task_node)
            task_manager.add_task_runner(task_id, task_runner)

            self.log.info(f"task {task_id} create success".center(60, "="))

            # 启动任务
            task_manager.start_task(task_id)
            self.log.info(f"task {task_id} start".center(60, "="))

            return True

        except Exception as e:
            error_message = f"Error creating task: {str(e)}"
            self.log.exception(error_message)
            task_manager.complete_task(bo.task_tree.task_id)
            raise ServerError(error_message) from e

    def stop_task(self, task_id: str):
        """终止任务

        Args:
            task_id: 任务ID

        Raises:
            Exception: 停止任务时发生错误
        """
        try:
            self.log.info(f"Stopping task {task_id}".center(60, "="))
            task_manager.stop_task(task_id)
            self.log.info(f"Task {task_id} stopped successfully".center(60, "="))
        except Exception as e:
            self.log.exception(f"Error stopping task {task_id}: {str(e)}")
            raise


task_service = TaskService()
