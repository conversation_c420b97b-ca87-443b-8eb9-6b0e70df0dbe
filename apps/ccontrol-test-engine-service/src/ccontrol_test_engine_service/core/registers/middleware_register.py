#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
from http import HTT<PERSON>tatus

from ccontrol_common.core.log import log
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from starlette.middleware.base import BaseHTTPMiddleware

from ccontrol_test_engine_service.config.config import settings


class AccessMiddleware(BaseHTTPMiddleware):
    """
    记录请求日志
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        # log request
        if request.client:
            client_addr = f"{request.client.host}:{request.client.port}"
        else:
            client_addr = "unknown"
        http_version = (
            request.scope.get("node_type", "").upper()
            + "/"
            + request.scope.get("http_version", "")
        )
        req_method = request.method
        req_path = request.scope.get("path", "")
        if request.query_params:
            req_path += "?" + str(request.query_params)
        trace_id = request.headers.get("x-apm-traceid")
        log.bind(client_addr=client_addr, trace_id=trace_id).info(
            f'request: "{req_method} {req_path} {http_version}"'
        )

        # call function
        start_time = time.time()
        response = await call_next(request)
        end_time = time.time()
        total_time = f"{end_time - start_time:.2f}s"

        # 取消以下注释可以把response body打印出来
        # response_body = [section async for section in response.body_iterator]
        # response.body_iterator = iterate_in_threadpool(iter(response_body))
        # log.info(f"response_body={response_body[0].decode()}")

        # log response
        status_code = response.status_code
        try:
            status_phrase = HTTPStatus(status_code).phrase
        except ValueError:
            status_phrase = ""
        status_and_phrase = f"{status_code} {status_phrase}"
        log.bind(client_addr=client_addr, trace_id=trace_id).info(
            f'response: "{request.method} {req_path} {http_version}" {status_and_phrase} - {total_time}'
        )

        return response


def register_middleware(app: FastAPI) -> None:
    # cors
    if settings.MIDDLEWARE_CORS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    # gzip
    if settings.MIDDLEWARE_GZIP:
        app.add_middleware(GZipMiddleware)
    # 接口访问日志
    if settings.MIDDLEWARE_ACCESS:
        app.add_middleware(AccessMiddleware)
