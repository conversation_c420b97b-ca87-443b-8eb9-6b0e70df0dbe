from http import HTTPStatus
from fastapi import FastAPI, Request
from fastapi.exceptions import HTTPException
from pydantic import ValidationError
from starlette.responses import JSONResponse

from ccontrol_common.core.errors import BaseError
from ccontrol_test_engine_service.core.response_model import response
from ccontrol_test_engine_service.config.config import settings


def validate_http_status_code(status_code):
    """
    验证并确保返回有效的 HTTP 状态码
    如果状态码不在标准 HTTP 状态码列表中，则返回 400 Bad Request
    `python 状态码标准支持 <https://github.com/python/cpython/blob/6e3cc72afeaee2532b4327776501eb8234ac787b/Lib/http
    /__init__.py#L7>`__

    `IANA 状态码注册表 <https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml>`__

    :param status_code: HTTP 状态码
    :return: 有效的 HTTP 状态码
    """
    if status_code not in HTTPStatus:
        return 400
    return status_code


def register_exception(app: FastAPI):
    @app.exception_handler(HTTPException)
    def http_exception_handler(_: Request, exc: HTTPException):  # noqa
        return JSONResponse(
            status_code=validate_http_status_code(exc.status_code),
            content=response.error(code=exc.status_code, message=exc.detail),
            headers=exc.headers,
        )

    @app.exception_handler(Exception)
    def all_exception_handler(_: Request, exc):  # noqa
        """全局异常处理"""
        # 常规异常
        if isinstance(exc, ValidationError):
            message = ""
            for error in exc.errors():
                field = error["loc"][-1]
                _msg = error["msg"]
                message += f"{field} {_msg},"
            return JSONResponse(
                status_code=422,
                content={
                    "detail": (
                        "请求参数非法"
                        if len(message) == 0
                        else f"请求参数非法: {message[:-1]}"
                    ),
                    "errors": exc.errors() if message == "" else None,
                },
            )

        # 自定义异常
        if isinstance(exc, BaseError):
            return JSONResponse(
                status_code=validate_http_status_code(exc.error.code),
                content=response.error(
                    code=exc.error.code,
                    message=str(exc.error.message),
                    data=exc.error.data if exc.error.data else None,
                ),
            )

        return JSONResponse(
            status_code=500,
            content=(
                response.error(code=500, message=str(exc))
                if settings.UVICORN_RELOAD
                else response.error(code=500, message="Internal Server Error")
            ),
        )
