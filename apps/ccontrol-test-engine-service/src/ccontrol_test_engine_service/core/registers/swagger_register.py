from fastapi import FastAPI
from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)

from ccontrol_test_engine_service.config.config import settings


def register_swagger(app: FastAPI):
    @app.get(settings.SWAGGER_UI_OAUTH2_REDIRECT_URL, include_in_schema=False)
    async def swagger_ui_redirect():
        return get_swagger_ui_oauth2_redirect_html()

    @app.get(settings.DOCS_URL, include_in_schema=False)
    async def custom_swagger_ui_html():
        return get_swagger_ui_html(
            openapi_url=settings.OPENAPI_URL,
            title=settings.TITLE + " - Swagger UI",
            swagger_js_url="/static/swagger-ui/swagger-ui-bundle.js",
            swagger_css_url="/static/swagger-ui/swagger-ui.css",
            swagger_favicon_url="/static/swagger-ui/favicon.png",
        )

    @app.get(settings.REDOCS_URL, include_in_schema=False)
    async def redoc_html():
        return get_redoc_html(
            openapi_url=settings.OPENAPI_URL,
            title=settings.TITLE + " - ReDoc",
            redoc_js_url="/static/swagger-ui/redoc.standalone.js",
            redoc_favicon_url="/static/swagger-ui/favicon.png",
        )
