import os
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles

from ccontrol_test_engine_service.api import all_routers
from ccontrol_test_engine_service.config.config import settings
from ccontrol_test_engine_service.config.path_config import STATIC_PATH
from ccontrol_test_engine_service.config.test_engine_config import (
    DEFAULT_CHECK_CRON_TAB,
)
from ccontrol_test_engine_service.core.registers.exception_register import (
    register_exception,
)
from ccontrol_test_engine_service.core.registers.middleware_register import (
    register_middleware,
)
from ccontrol_test_engine_service.core.registers.swagger_register import (
    register_swagger,
)
from ccontrol_test_engine_service.utils.cookies_util import cookies_util
from ccontrol_test_engine_service.utils.redis_client import RedisClient
from ccontrol_test_engine_service.utils.scheduler_util import scheduler_util


def register_router(app: FastAPI):
    """API 路由"""
    for router in all_routers:
        app.include_router(router)


def register_static_file(app: FastAPI):
    """静态文件"""
    if not os.path.exists(STATIC_PATH):
        os.mkdir(STATIC_PATH)
    app.mount("/static", StaticFiles(directory=STATIC_PATH), name="static")


def register_redis_client():
    """Redis 客户端"""
    redis_listener = RedisClient()
    redis_listener.start()


def register_cookies_checker():
    """
    cookies 定期更新

    更新策略：
    - 每小时检测一次
    更新cookies的情况：
    - 获取cookies过期时间失败
    - 获取cookies过期时间成功且未超过6天
    """
    scheduler_util.add_job(
        fun_name="cookies update",
        fun=cookies_util.update_service_cookies,
        crontab=DEFAULT_CHECK_CRON_TAB,
    )


@asynccontextmanager
async def fastapi_lifespan(app: FastAPI):  # app参数必须设置
    """fastapi 生命周期"""

    try:
        # lifespan fun instead of on_event
        register_redis_client()
        register_cookies_checker()
        # code will execute before application start
        yield
        # code will execute after application finish
    finally:
        pass


def register_app():
    # FastAPI
    app = FastAPI(
        title=settings.TITLE,
        version=settings.VERSION,
        description=settings.DESCRIPTION,
        redoc_url=settings.REDOCS_URL,
        openapi_url=settings.OPENAPI_URL,
        lifespan=fastapi_lifespan,
    )

    # 中间件
    register_middleware(app)

    # 路由
    register_router(app)

    # 全局异常处理
    register_exception(app)

    # 注册静态文件
    register_static_file(app)

    # 代理 swagger ui 静态资源
    register_swagger(app)

    return app
