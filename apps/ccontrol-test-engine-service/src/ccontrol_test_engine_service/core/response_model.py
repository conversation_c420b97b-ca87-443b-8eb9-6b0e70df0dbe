#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime
from http import HTTPStatus
from typing import Annotated, Any, Generic, Optional, TypeVar

from pydantic import BaseModel, ConfigDict, Field

T = TypeVar("T")


class ResponseModel(BaseModel, Generic[T]):
    """统一API响应模型"""

    code: Annotated[int, Field(description="状态码", examples=[200])] = HTTPStatus.OK
    message: Annotated[str, Field(description="响应消息")] = HTTPStatus.OK.phrase
    data: Optional[T] = Field(default=None, description="响应数据")

    model_config = ConfigDict(
        json_encoders={datetime: lambda x: x.strftime("%Y-%m-%d %H:%M:%S")},
        arbitrary_types_allowed=True,
    )


class ApiResponse(Generic[T]):
    """API响应工具类"""

    @staticmethod
    def success(
        code: int = HTTPStatus.OK,
        message: str = HTTPStatus.OK.phrase,
        data: Optional[T] = None,
    ):
        """成功响应"""
        return ResponseModel[T](code=code, message=message, data=data)

    @staticmethod
    def error(
        code: int = HTTPStatus.BAD_REQUEST,
        message: str = HTTPStatus.BAD_REQUEST.phrase,
        data: Optional[T] = None,
    ):
        """错误响应"""
        return ResponseModel[T](code=code, message=message, data=data)

    @staticmethod
    def parameter_error(
        code: int = HTTPStatus.BAD_REQUEST,
        message: str = HTTPStatus.BAD_REQUEST.phrase,
    ):
        """参数错误响应"""
        return ApiResponse[Any].error(code=code, message=message)

    @staticmethod
    def not_found(
        code: int = HTTPStatus.NOT_FOUND,
        message: str = HTTPStatus.NOT_FOUND.phrase,
    ):
        """404响应"""
        return ApiResponse[Any].error(code=code, message=message)

    @staticmethod
    def unauthorized(
        code: int = HTTPStatus.UNAUTHORIZED,
        message: str = HTTPStatus.UNAUTHORIZED.phrase,
    ):
        """401响应"""
        return ApiResponse[Any].error(code=code, message=message)

    @staticmethod
    def forbidden(
        code: int = HTTPStatus.FORBIDDEN,
        message: str = HTTPStatus.FORBIDDEN.phrase,
    ):
        """403响应"""
        return ApiResponse[Any].error(code=code, message=message)

    @staticmethod
    def server_error(
        code: int = HTTPStatus.INTERNAL_SERVER_ERROR,
        message: str = HTTPStatus.INTERNAL_SERVER_ERROR.phrase,
        data: Optional[T] = None,
    ):
        """500响应"""
        return ApiResponse[T].error(code=code, message=message, data=data)


response = ApiResponse()


if __name__ == "__main__":
    result = response.success(data={"a": 1})
    print(f"result: {result}")

    print(f"HTTPStatus.OK: {HTTPStatus.OK.phrase}")

    print(f"200 in HTTPStatus: {900 in HTTPStatus}")
