from ccontrol_test_engine import GlobalStore, quick_init, log
from gen.agent_rpc import AgentRPC

# 必要：快速初始化
quick_init()

# 预置代码，方便编写用例
global_var = GlobalStore().get_global_var()
driver = GlobalStore().get_driver()
ai = driver.ai
agent_rpc: AgentRPC = driver.agent_rpc


# ==============================================
# =============== 测试用例编写区域 ===============
# ==============================================

# TODO: 在此处编写自动化测试用例
try:
    driver.screenshot()

    # driver.event.execute_python_event_by_method("hello_world")

    raise AssertionError("手动抛出的异常，测试用")

    print("print hello world")
    log.info("log hello world")
except Exception as e:
    # 必要：执行异常时关闭 driver
    driver.close()
    log.exception(f"测试用例执行失败: {e}")
    raise e

# ==============================================
# =============== 测试用例编写区域 ===============
# ==============================================


# 必要：关闭 driver
driver.close()
