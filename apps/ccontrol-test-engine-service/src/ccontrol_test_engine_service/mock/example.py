import time

# remove this import while using
from loguru import logger as log
from loguru._logger import Logger
from ccontrol_test_engine.service.task.model.global_model import GlobalStore

log: Logger
log.info("hello this is Event %d in Case %d in task: %s")
driver = GlobalStore().get_driver()
d = driver.adb_connect()
ai = driver.ai
agent_rpc = driver.agent_rpc
log.info(f"device info: {d.info}")
log.info(f"ai: {ai}")
log.info(f"agent_rpc: {agent_rpc}")
app_session = d.session("com.cvte.settings")
log.info(f"app_session: {app_session}")
time.sleep(1)
