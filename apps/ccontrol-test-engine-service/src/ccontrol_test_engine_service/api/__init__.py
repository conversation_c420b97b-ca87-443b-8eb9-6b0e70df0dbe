from fastapi import APIRouter

from ccontrol_test_engine_service.api.v1.common_controller import common_router
from ccontrol_test_engine_service.api.v1.greet_controller import greet_router
from ccontrol_test_engine_service.api.v1.task_controller import task_router

# 根路由
root_router = APIRouter()
root_router.include_router(common_router, tags=["通用"])

# v1路由
v1_router = APIRouter(prefix="/v1")
v1_router.include_router(greet_router, prefix="/greet", tags=["Greet"])
v1_router.include_router(task_router, prefix="/tasks", tags=["Task"])

# 导出所有路由
all_routers = [root_router, v1_router]
