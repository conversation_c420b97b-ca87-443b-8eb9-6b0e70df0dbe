#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from fastapi import APIRouter

from ccontrol_test_engine_service.core.response_model import ResponseModel, response
from ccontrol_test_engine_service.services.greet_service import greet_service
from ccontrol_test_engine_service.services.greet_service import (
    HelloReq,
    HelloResp,
)

greet_router = APIRouter()


@greet_router.get("/greetings/{greeting_id}", summary="获取指定问候语")
async def get_greeting(greeting_id: int) -> ResponseModel[HelloResp]:
    data = greet_service.get_greeting_by_id(greeting_id)
    return response.success(data=data)


@greet_router.get("/greetings", summary="获取问候语列表")
async def list_greetings(
    page: int = 1, size: int = 10
) -> ResponseModel[list[HelloResp]]:
    data = greet_service.list_greetings(page, size)
    return response.success(data=data)


@greet_router.put("/greetings/{greeting_id}", summary="更新问候语")
async def update_greeting(greeting_id: int, dto: HelloReq) -> ResponseModel[HelloResp]:
    data = greet_service.update_greeting(greeting_id, dto)
    return response.success(data=data)


@greet_router.delete("/greetings/{greeting_id}", summary="删除问候语")
async def delete_greeting(greeting_id: int) -> ResponseModel[None]:
    greet_service.delete_greeting(greeting_id)
    return response.success()


@greet_router.patch("/greetings/{greeting_id}/status", summary="修改问候语状态")
async def update_greeting_status(greeting_id: int, status: bool) -> ResponseModel[None]:
    greet_service.update_greeting_status(greeting_id, status)
    return response.success()


@greet_router.get("/greetings/search", summary="搜索问候语")
async def search_greetings(
    keyword: str, language: str, page: int = 1, size: int = 10
) -> ResponseModel[list[HelloResp]]:
    data = greet_service.search_greetings(keyword, language, page, size)
    return response.success(data=data)
