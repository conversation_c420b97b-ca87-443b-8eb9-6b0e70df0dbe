#!/user/bin/env python3
# -*- coding: utf-8 -*-
"""task controller"""

from typing import Union

from fastapi import APIRouter

from ccontrol_common.core.errors import RequestError
from ccontrol_test_engine_service.core.response_model import response
from ccontrol_test_engine_service.model.task.dto_task_request import (
    CreateSingleDeviceTaskDTO,
    CreateMultiDeviceTaskDTO,
)
from ccontrol_test_engine_service.services.task_service import task_service

task_router = APIRouter()


@task_router.post("/create", summary="新增任务")
async def execute_task(dto: Union[CreateSingleDeviceTaskDTO, CreateMultiDeviceTaskDTO]):
    """创建并执行任务

    Args:
        dto: 任务请求DTO对象

    Returns:
        成功返回success响应
        失败返回error响应
    """
    try:
        # 转换DTO到BO并创建任务
        task_bo = dto.to_task_req_bo()
        task_service.create_task(task_bo)
        return response.success()
    except RequestError as e:
        # 业务异常直接返回错误信息
        return response.error(data=str(e))
    except Exception as e:
        # 其他异常记录日志并返回通用错误
        task_service.log.exception(f"Unexpected error creating task: {str(e)}")
        return response.error(data="Internal server error")


@task_router.patch("/stop", summary="终止任务")
async def stop_task(task_id: str):
    """终止指定任务

    Args:
        task_id: 任务ID

    Returns:
        成功返回success响应
    """
    try:
        task_service.stop_task(task_id=task_id)
        return response.success(data="Task stopped successfully")
    except Exception as e:
        task_service.log.error(f"Failed to stop task {task_id}: {str(e)}")
        return response.error(data=f"Failed to stop task: {str(e)}")
