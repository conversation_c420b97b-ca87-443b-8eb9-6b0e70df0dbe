from enum import Enum
from typing import Optional

from ccontrol_test_engine_service.model.camel_model import CamelBaseModel


class UploadStatus(Enum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"


class WhereParamBo(CamelBaseModel):
    """where字段数据模型"""

    id: Optional[str] = None
    name: Optional[str] = None
    path: Optional[str] = None
    type: Optional[str] = None
    size: Optional[int] = None
    check_sum: Optional[str] = None
    upload_status: Optional[UploadStatus] = None
    expire_time: Optional[str] = None


class FileUpdateBo(CamelBaseModel):
    """文件状态更新数据模型"""

    where: WhereParamBo
    data: WhereParamBo
