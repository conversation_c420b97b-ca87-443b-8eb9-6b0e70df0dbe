"""对象存储预签名获取"""

from typing import Optional

from pydantic import Field

from ccontrol_test_engine_service.model.camel_model import CamelBaseModel


class PresignedUpReqBo(CamelBaseModel):
    """上传预签名请求模型"""

    path: Optional[str] = None
    name: str
    type: str
    size: int
    last_modified: float
    file_expires: Optional[float] = None
    sign_expires: Optional[float] = None


class PresignedUpRespBo(CamelBaseModel):
    """上传预签名响应模型"""

    upload_url: str = Field(alias="uploadURL")
    proxy_upload_url: str = Field(alias="proxyUploadURL")
    form_data: dict = Field(alias="formData")
    id: str


class PresignedDownReqBo(CamelBaseModel):
    """下载预签名请求模型"""

    id: str
    sign_expires: Optional[float] = None
    resp_headers: Optional[dict] = None
