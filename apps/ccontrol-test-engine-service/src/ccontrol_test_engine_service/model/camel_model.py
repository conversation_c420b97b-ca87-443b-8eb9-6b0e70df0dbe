"""对接前端数据模型"""

import json

from pydantic import BaseModel


class CamelBaseModel(BaseModel):
    """小驼峰形式的数据模型"""

    def dump_camel_json(self):
        """dump小驼峰形式数据"""

        def snake_2_camel(data: str) -> str:
            component = data.split("_")
            return component[0] + "".join(x.title() for x in component[1:])

        return json.loads(
            self.model_dump_json(),
            object_hook=lambda d: {
                snake_2_camel(k): v for k, v in d.items() if v is not None
            },
        )
