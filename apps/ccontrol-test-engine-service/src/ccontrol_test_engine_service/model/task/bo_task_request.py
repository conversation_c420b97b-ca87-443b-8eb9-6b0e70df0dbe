"""任务请求模型"""

from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field

from ccontrol_test_engine import TestSuiteNodeModel, TaskNodeModel
from ccontrol_test_engine.model.device import (
    DeviceStatusEnum,
    OccupancyTypeEnum,
    StatusJson,
)


class DeviceInfoBo(BaseModel):
    """设备信息模型"""

    device_id: int
    serial_number: str
    name: str
    status: Optional[DeviceStatusEnum] = None
    occupancy_type: Optional[OccupancyTypeEnum] = None
    occupied_by_task: Optional[str] = None
    occupied_by_user: Optional[str] = None
    occupied_at: Optional[datetime] = None
    occupy_expires_at: Optional[datetime] = None
    status_json: Optional[StatusJson] = None
    version: Optional[str] = None
    ram: Optional[int] = None
    rom: Optional[int] = None
    agent_id: Optional[str] = None


class TaskTypeEnum(Enum):
    """任务类型枚举"""

    TASK_NORMAL = "normal"
    TASK_XTS = "xts"


class TaskInfoBo(BaseModel):
    """任务信传输息模型"""

    task_type: TaskTypeEnum = TaskTypeEnum.TASK_NORMAL

    task_id: str
    name: str
    description: Optional[str] = None

    children: TestSuiteNodeModel

    def to_task_node(self):
        """转换为任务节点"""
        return TaskNodeModel.model_validate_json(self.model_dump_json())

    def get_test_suite(self):
        """获取测试套件"""
        return self.children


class TaskConfigBo(BaseModel):
    """任务配置传输模型"""

    notififation: dict = Field(default_factory=dict)
    user_global_var: dict = Field(default_factory=dict)


class CreateTaskBaseBo(BaseModel):
    """创建任务基础模型"""

    task_tree: TaskInfoBo
    config: TaskConfigBo


class CreateSingleDeviceTaskBo(CreateTaskBaseBo):
    """创建单设备任务"""

    device: DeviceInfoBo


class CreateMultiDeviceTaskBo(CreateTaskBaseBo):
    """创建多设备任务"""

    devices: List[DeviceInfoBo]
