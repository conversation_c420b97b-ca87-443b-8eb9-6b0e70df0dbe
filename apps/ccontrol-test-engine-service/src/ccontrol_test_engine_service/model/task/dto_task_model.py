from typing import List, Optional

from pydantic import BaseModel, Field
from pydantic.alias_generators import to_camel

from ccontrol_test_engine.service.task.model.task_model import (
    NodeTypeEnum,
    NodeStatusEnum,
)
from ccontrol_test_engine_service.model.task.dto_event_model import EventDto


class NodeBaseDto(BaseModel):
    node_type: NodeTypeEnum
    node_status: NodeStatusEnum = Field(NodeStatusEnum.PENDING, alias="nodeStatus")
    error_message: Optional[str] = Field(None, alias="errorMessage")

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
    }


class EventNodeDto(NodeBaseDto, EventDto):
    node_type: NodeTypeEnum = Field(NodeTypeEnum.EVENT, alias="nodeType")
    event_id: Optional[str] = Field(alias="eventId")
    name: str
    description: Optional[str] = None


class TestCaseNodeDto(NodeBaseDto):
    node_type: NodeTypeEnum = Field(NodeTypeEnum.TEST_CASE, alias="nodeType")
    test_case_id: str = Field(alias="testCaseId")
    name: str
    description: Optional[str] = None
    children: List[EventNodeDto] = []


class TestSuiteNodeDto(NodeBaseDto):
    node_type: NodeTypeEnum = Field(NodeTypeEnum.TEST_SUITE, alias="nodeType")
    test_suite_id: str = Field(alias="testSuiteId")
    name: str
    description: Optional[str] = None
    children: List[TestCaseNodeDto] = []


class TaskNodeDto(NodeBaseDto):
    node_type: NodeTypeEnum = Field(NodeTypeEnum.TASK, alias="nodeType")
    task_id: str = Field(alias="taskId")
    name: str
    description: Optional[str] = None
    children: Optional[TestSuiteNodeDto] = None
