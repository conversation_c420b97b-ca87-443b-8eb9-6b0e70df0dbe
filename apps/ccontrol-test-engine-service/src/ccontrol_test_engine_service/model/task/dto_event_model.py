from typing import Optional, Union, Literal

from pydantic import BaseModel, Field

from ccontrol_test_engine.service.event.model.event_model import EventTypeEnum


class EventBaseDto(BaseModel):
    function_name: str = Field(alias="function_name")
    module: str
    input_schema: Optional[dict] = Field(alias="inputSchema")
    output_schema: Optional[dict] = Field(alias="outputSchema")


class NormalEventDto(EventBaseDto):
    type: Literal[EventTypeEnum.NORMAL]


class RPCEventDto(EventBaseDto):
    type: Literal[EventTypeEnum.RPC]
    # TODO: 待实现，存在这个信息则使用 RPC 方式调用
    rpc: Optional[dict] = None


class PythonEventDto(BaseModel):
    type: Literal[EventTypeEnum.PYTHON]
    content: str


class EventDto(BaseModel):
    # TODO: 后台事件待实现
    is_background: bool = Field(False, alias="isBackground")
    backend_strategy: Optional[str] = Field(None, alias="backendStrategy")
    data: Union[NormalEventDto, RPCEventDto, PythonEventDto]
