"""任务请求模型"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, field_validator
from pydantic.alias_generators import to_camel

from ccontrol_common.core import log
from ccontrol_test_engine.model.device import (
    DeviceStatusEnum,
    OccupancyTypeEnum,
    StatusJson,
)
from ccontrol_test_engine_service.model.task.bo_task_request import (
    CreateSingleDeviceTaskBo,
    CreateMultiDeviceTaskBo,
    TaskTypeEnum,
    TaskInfoBo,
)
from ccontrol_test_engine_service.model.task.dto_task_model import TestSuiteNodeDto


# Refer: https://gitlab.gz.cvte.cn/tv-fontend/zeus/-/blob/next-boot/src/db/zmodels/ccontrol.zmodel#L184
class DeviceInfoDTO(BaseModel):
    """设备信息传输模型"""

    device_id: int = Field(alias="id")  # frontend name is id
    serial_number: str = Field(alias="serialNumber")
    name: str
    status: Optional[DeviceStatusEnum] = None
    occupancy_type: Optional[OccupancyTypeEnum] = Field(None, alias="occupancyType")
    occupied_by_task: Optional[str] = Field(None, alias="occupiedByTask")
    occupied_by_user: Optional[str] = Field(None, alias="occupiedByUser")
    occupied_at: Optional[datetime] = Field(None, alias="occupiedAt")
    occupy_expires_at: Optional[datetime] = Field(None, alias="occupyExpiresAt")
    status_json: Optional[StatusJson] = Field(None, alias="statusJson")
    version: Optional[str] = None
    ram: Optional[str] = None
    rom: Optional[str] = None
    agent_id: Optional[str] = Field(None, alias="agentId")

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
    }

    @field_validator("ram", "rom")
    @classmethod
    def double(cls, value: Optional[str]) -> int:
        """将ram/rom转换为整数"""
        try:
            return int(value) if value else 0
        except ValueError:
            log.info("invalid ram or rom, ignore it")
            return 0


class TaskInfoDTO(BaseModel):
    """任务信传输息模型"""

    task_type: TaskTypeEnum = Field(TaskTypeEnum.TASK_NORMAL, alias="taskType")
    task_id: str = Field(alias="taskId")  # frontend name is id
    name: str
    description: Optional[str] = None
    children: TestSuiteNodeDto

    def to_task_info_bo(self):
        """转换为BO模型"""
        return TaskInfoBo.model_validate_json(self.model_dump_json())

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
    }


class TaskConfigDTO(BaseModel):
    """任务配置传输模型"""

    notififation: dict = Field(default_factory=dict, alias="notification")
    user_global_var: dict = Field(default_factory=dict, alias="userGlobalVar")


class CreateTaskDTO(BaseModel):
    """任务请求传输模型"""

    task_tree: TaskInfoDTO = Field(alias="taskTree")
    config: TaskConfigDTO = Field(default_factory=TaskConfigDTO)


class CreateSingleDeviceTaskDTO(CreateTaskDTO):
    """创建单设备任务请求"""

    device: DeviceInfoDTO

    def to_task_req_bo(self):
        """转换为BO"""
        return CreateSingleDeviceTaskBo.model_validate_json(self.model_dump_json())


class CreateMultiDeviceTaskDTO(CreateTaskDTO):
    """创建多设备任务请求"""

    devices: List[DeviceInfoDTO]

    def to_task_req_bo(self):
        """转换为BO"""
        return CreateMultiDeviceTaskBo.model_validate_json(self.model_dump_json())
