"""任务运行状态模型"""

from datetime import datetime
from enum import Enum
from typing import Optional

from pydantic import Field
from pydantic.alias_generators import to_camel

from ccontrol_test_engine import TaskNodeModel
from ccontrol_test_engine_service.model.camel_model import CamelBaseModel


class TaskStatusEnum(Enum):
    """任务状态枚举"""

    PENDING = "pending"
    PROCESSING = "processing"
    FAILED = "failed"
    FINISHED = "finished"


class TaskRunningStatusBo(CamelBaseModel):
    """任务运行状态模型"""

    task_id: str
    status: TaskStatusEnum = TaskStatusEnum.PENDING
    log_path: Optional[str] = None
    error_message: Optional[str] = None
    report_json: Optional[dict] = {}
    screen_recording_url: Optional[str] = None  # 任务录屏地址

    start_time: datetime = Field(default=datetime.now(), description="任务启动时间")
    finish_time: datetime = Field(default=datetime.now(), description="任务结束时间")
    last_record_time: datetime = Field(
        default=datetime.now(), description="上一次记录时间"
    )

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
    }

    def set_status(self, target_status: TaskStatusEnum):
        """设置任务执行状态"""
        self.status = target_status

    def do_record(self, task_node: TaskNodeModel):
        """记录任务状态信息"""
        self.last_record_time = datetime.now()
        self.report_json = task_node.model_dump()
