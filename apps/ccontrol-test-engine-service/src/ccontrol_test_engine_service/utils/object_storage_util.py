"""对象存储工具类: 用于上传日志文件等"""

import mimetypes
import os
from datetime import datetime
from typing import Optional

from pathlib import Path

from ccontrol_common.config.api_conf import (
    API_PRESIGNED_UPLOAD,
    API_PRESIGNED_OVERRIDE_UPLOAD,
    API_PRESIGNED_DOWNLOAD,
    API_FILE_STATUS_UPDATE,
)
from ccontrol_common.core import SingletonMeta
from ccontrol_common.core.http_client import (
    DEFAULT_LOG_LEN,
    do_post,
    do_patch,
    response_valid,
)
from ccontrol_test_engine_service.config.config import settings
from ccontrol_common.core.log import log
from ccontrol_test_engine_service.config.test_engine_config import (
    DEFAULT_FILE_TYPE,
)
from ccontrol_test_engine_service.model.object_storage.bo_file_update import (
    FileUpdateBo,
    UploadStatus,
)
from ccontrol_test_engine_service.model.object_storage.bo_presigned_request import (
    PresignedUpReqBo,
    PresignedDownReqBo,
    PresignedUpRespBo,
)


class ObjectStorageUtil(metaclass=SingletonMeta):
    """对象存储工具类"""

    @staticmethod
    def get_file_info(
        local_file_path: str,
        remote_file_path: Optional[str] = None,
        file_expires: Optional[datetime] = None,
        sign_expires: Optional[datetime] = None,
    ):
        """获取文件信息"""
        try:
            file_type, file_encoding = mimetypes.guess_type(local_file_path)

            file_info = {
                "name": os.path.basename(local_file_path),
                "type": file_type,
                "size": os.path.getsize(local_file_path),
                "encoding": file_encoding,
                "last_modified": os.path.getmtime(local_file_path),
            }
        except Exception as e:
            log.error(f"get file info error, {e}")
            raise e

        if remote_file_path:
            file_info["path"] = remote_file_path
        if file_expires:
            file_info["file_expires"] = file_expires
        if sign_expires:
            file_info["sign_expires"] = sign_expires
        if not file_info["type"]:
            file_info["type"] = DEFAULT_FILE_TYPE

        return PresignedUpReqBo.model_validate(file_info)

    @staticmethod
    def get_upload_url(file_info: PresignedUpReqBo):
        """获取预签名上传链接"""
        request_url = settings.ZEUS_DOMAIN + (
            API_PRESIGNED_OVERRIDE_UPLOAD if file_info.path else API_PRESIGNED_UPLOAD
        )
        presigned_resp = do_post(
            request_url,
            json_data=file_info.dump_camel_json(),
            log_req_len=DEFAULT_LOG_LEN,
            log_resp_len=DEFAULT_LOG_LEN,
        )

        resp_data = response_valid(
            presigned_resp,
            err_msg="presigned update url request",
        )
        if resp_data.success:
            return resp_data.data
        return None

    @staticmethod
    def get_download_url(file_id: str):
        """获取文件下载链接"""
        down_req = PresignedDownReqBo(id=file_id)
        presigned_resp = do_post(
            settings.ZEUS_DOMAIN + API_PRESIGNED_DOWNLOAD,
            json_data=down_req.dump_camel_json(),
        )

        resp_data = response_valid(presigned_resp, err_msg="presigned download url")
        if resp_data.success:
            return resp_data.data
        return None

    @staticmethod
    def do_file_upload(url: str, upload_form_data: dict, file_path: str):
        """文件上传"""
        try:
            with open(file_path, "rb") as f:
                files = {"file": (os.path.basename(file_path), f)}
                response = do_post(
                    url,
                    files=files,
                    data=upload_form_data,
                    log_req_len=DEFAULT_LOG_LEN,
                )

                return response.is_success
        except Exception as e:
            log.exception(e)
            raise AssertionError(f"{str(e)}") from e

    @staticmethod
    def update_file_status(
        file_id: str, status: bool, expire_time: Optional[str] = None
    ):
        """更新文件状态"""
        update_status = UploadStatus.SUCCESS if status else UploadStatus.FAILED
        file_update = FileUpdateBo.model_validate(
            {
                "where": {
                    "id": file_id,
                },
                "data": {
                    "upload_status": update_status,
                    "expire_time": expire_time,
                },
            }
        )
        response = do_patch(
            settings.ZEUS_DOMAIN + API_FILE_STATUS_UPDATE,
            json=file_update.dump_camel_json(),
        )
        return response_valid(
            response, err_msg="update file status", check_json=False
        ).success

    def upload_file_2_s3(
        self,
        local_file_path: str,
        remote_file_path: Optional[str] = None,
        file_expires: Optional[datetime] = None,
        sign_expires: Optional[datetime] = None,
    ):
        """上传文件至对象存储服务"""
        # step 1: 获取文件信息
        file_info = self.get_file_info(
            local_file_path, remote_file_path, file_expires, sign_expires
        )

        # step 2: 获取上传文件预签名
        presigned_data = self.get_upload_url(file_info)
        if not presigned_data:
            return None
        presigned_bo = PresignedUpRespBo.model_validate(presigned_data)

        # step 3: 上传文件
        if presigned_bo.upload_url and presigned_bo.form_data:
            upload_res = self.do_file_upload(
                presigned_bo.upload_url, presigned_bo.form_data, local_file_path
            )
        else:
            log.error(
                "get presigned upload file url failed, please check your file or network"
            )
            return None

        # step 4: 更新文件状态
        self.update_file_status(presigned_bo.id, upload_res)

        # step 5: 返回S3服务器日志地址
        return remote_file_path


object_storage_util = ObjectStorageUtil()


if __name__ == "__main__":
    object_storage_util.upload_file_2_s3(
        local_file_path=str(
            Path.joinpath(Path(__file__).absolute().parent, "test_1.txt")
        ),
        remote_file_path="/fcl_test/test_1.log",
    )
