"""cookies工具"""

import functools
import hashlib
import time
from datetime import datetime

import pandas

from ccontrol_common.core.http_client import httpx_client
from ccontrol_common.config.api_conf import API_AUTH_CREDENTIALS, API_AUTH_SESSION
from ccontrol_common.config.conf import settings
from ccontrol_common.core import SingletonMeta, log
from ccontrol_common.core.http_client import do_get, response_valid, do_post
from ccontrol_test_engine_service.config.test_engine_config import (
    SERVICE_ID,
    AUTH_TYPE,
    DEFAULT_COOKIES_EXPIRES,
)
from ccontrol_test_engine_service.model.cookies.bo_cookies_request import CookiesReqBo
from ccontrol_test_engine_service.config.config import settings as test_engine_settings


class CookiesUtil(metaclass=SingletonMeta):
    """cookies工具类"""

    @staticmethod
    def generate_signature(service_id: str, timestamp: int) -> str:
        """生成签名"""
        attribute = f"serviceId={service_id}&serviceSecret={test_engine_settings.SERVICE_SECRET}&timestamp={timestamp}"
        hash_object = hashlib.md5()
        hash_object.update(attribute.encode())
        signature = hash_object.hexdigest()
        return signature

    @functools.lru_cache()
    def get_service_cookies(self):
        """获取cookies"""
        timestamp = int(time.time())
        signature = self.generate_signature(SERVICE_ID, timestamp)
        form_data = CookiesReqBo(
            type=AUTH_TYPE,
            service_id=SERVICE_ID,
            ts=str(timestamp),
            sign=signature,
        )
        resp = do_post(
            settings.ZEUS_DOMAIN + API_AUTH_CREDENTIALS,
            data=form_data.dump_camel_json(),
        )

        if resp.is_success or resp.is_redirect:
            return resp.cookies
        else:
            log.error(f"HTTP POST 请求失败: url={resp.url}, code: {resp.status_code}")
            return {}

    def update_service_cookies(self):
        """更新服务端cookies"""
        if not self.is_cookies_valid():
            log.info("cookies is not valid")
            self.get_service_cookies.cache_clear()

        cookies = self.get_service_cookies()
        log.info(f"set cookies: {cookies}")
        if settings.ZEUS_COOKIE_KEY in cookies:
            settings.ZEUS_COOKIE_VALUE = cookies[settings.ZEUS_COOKIE_KEY]
            # 更新 HttpClient 的 cookies
            httpx_client.update_cookies(
                {settings.ZEUS_COOKIE_KEY: settings.ZEUS_COOKIE_VALUE}
            )

    @staticmethod
    def auth_session_request() -> dict:
        """进行auth session请求"""
        resp = do_get(settings.ZEUS_DOMAIN + API_AUTH_SESSION)

        resp = response_valid(resp, err_msg="auth session", check_json=False)
        if resp.success and resp.resp_json:
            return resp.resp_json

        return {}

    def is_cookies_valid(self) -> bool:
        """检查当前cookie是否过期"""
        resp_json = self.auth_session_request()

        # 解析响应数据
        user, expires_time = (resp_json.get("user", {}), resp_json.get("expires", ""))

        # 请求失败时默认cookies失效
        if not expires_time or user["account"] != SERVICE_ID:
            return False

        try:
            date_get = pandas.to_datetime(expires_time)
            log.info(f"cookies expires time is: {date_get}")
            # cookies时间超过6天时视为过期
            if (
                datetime.now().timestamp() >= date_get.timestamp()
                or (date_get.timestamp() - datetime.now().timestamp())
                <= DEFAULT_COOKIES_EXPIRES
            ):
                log.info("cookies had expired or will expire")
                return False
        except ValueError as e:
            log.error(f"get expires time {expires_time} is invalid")
            log.exception(e)
            return False

        return True


cookies_util = CookiesUtil()

if __name__ == "__main__":
    cookies_util.update_service_cookies()
