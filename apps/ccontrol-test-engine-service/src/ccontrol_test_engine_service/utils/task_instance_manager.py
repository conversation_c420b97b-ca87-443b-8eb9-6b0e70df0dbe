import os
from typing import Dict, Optional

from ccontrol_common.config import path_conf
from ccontrol_common.config.api_conf import API_TASK_COMPLETE
from ccontrol_common.config.conf import settings
from ccontrol_common.core import SingletonMeta, log
from ccontrol_common.core.http_client import do_post, DEFAULT_LOG_LEN, response_valid
from ccontrol_common.core.log import LogTree
from ccontrol_common.core.errors import (
    TaskNotFoundError,
    TaskStateError,
    TaskRuntimeError,
)
from ccontrol_test_engine import TaskNodeModel, TaskRunner, NodeStatusEnum
from ccontrol_test_engine_service.model.task.bo_task_response import (
    TaskRunningStatusBo,
    TaskStatusEnum,
)
from ccontrol_test_engine_service.config.path_config import TASK_LOG_PATH
from ccontrol_test_engine_service.utils.object_storage_util import object_storage_util
from pydantic import BaseModel


class TaskInstance(BaseModel):
    """任务实例数据类

    该类用于存储任务实例的所有相关信息，包括：
    1. 任务ID
    2. 日志处理器
    3. 日志树
    4. 任务节点
    5. 任务运行器
    6. 运行状态
    """

    task_id: str
    log_handler: Optional[int] = None
    log_tree: Optional[LogTree] = None
    task_node: Optional[TaskNodeModel] = None
    task_runner: Optional[TaskRunner] = None
    running_status: Optional[TaskRunningStatusBo] = None

    model_config = {
        "arbitrary_types_allowed": True,  # 允许使用任意类型
    }


class TaskInstanceManager(metaclass=SingletonMeta):
    """任务实例管理器：管理所有任务实例

    该类负责管理所有任务实例的生命周期，包括：
    1. 任务的创建和销毁
    2. 任务状态的管理和转换
    3. 任务日志的管理
    4. 任务运行时的管理
    """

    def __init__(self):
        """初始化任务实例管理器"""
        self._tasks: Dict[str, TaskInstance] = {}

    def _get_task(self, task_id: str) -> TaskInstance:
        """获取任务实例，如果不存在则抛出异常

        Args:
            task_id: 任务ID

        Returns:
            TaskInstance: 任务实例

        Raises:
            TaskNotFoundError: 任务不存在时抛出
        """
        if task_id not in self._tasks:
            raise TaskNotFoundError(task_id)
        return self._tasks[task_id]

    def create_task(self, task_id: str) -> None:
        """创建新的任务实例

        Args:
            task_id: 任务ID

        Raises:
            TaskStateError: 任务已存在时抛出
        """
        if task_id in self._tasks:
            raise TaskStateError(f"Task {task_id} already exists")
        self._tasks[task_id] = TaskInstance(task_id=task_id)

    def add_log_handler(self, task_id: str, log_handler: int) -> None:
        """添加日志处理器

        Args:
            task_id: 任务ID
            log_handler: 日志处理器ID
        """
        task = self._get_task(task_id)
        task.log_handler = log_handler

    def add_log_tree(self, task_id: str, log_tree: LogTree) -> None:
        """添加日志树

        Args:
            task_id: 任务ID
            log_tree: 日志树实例
        """
        task = self._get_task(task_id)
        task.log_tree = log_tree

    def add_task_node(self, task_id: str, task_node: TaskNodeModel) -> None:
        """添加任务节点

        Args:
            task_id: 任务ID
            task_node: 任务节点模型
        """
        task = self._get_task(task_id)
        task.task_node = task_node

    def add_task_runner(self, task_id: str, task_runner: TaskRunner) -> None:
        """添加任务运行器

        Args:
            task_id: 任务ID
            task_runner: 任务运行器实例
        """
        task = self._get_task(task_id)
        task.task_runner = task_runner

    def add_task_running_status(
        self, task_id: str, status: TaskRunningStatusBo
    ) -> None:
        """添加任务运行状态

        Args:
            task_id: 任务ID
            status: 任务运行状态
        """
        task = self._get_task(task_id)
        task.running_status = status

    def _convert_node_status(self, task_id: str) -> TaskStatusEnum:
        """转换节点状态为任务状态

        Args:
            task_id: 任务ID

        Returns:
            TaskStatusEnum: 任务状态
        """
        task = self._get_task(task_id)
        if not task.task_node:
            return TaskStatusEnum.PENDING

        node_status = task.task_node.node_status
        status_map = {
            NodeStatusEnum.PROCESSING: TaskStatusEnum.PROCESSING,
            NodeStatusEnum.FINISHED: TaskStatusEnum.FINISHED,
            NodeStatusEnum.FAILED: TaskStatusEnum.FAILED,
        }
        return status_map.get(node_status, TaskStatusEnum.PENDING)

    def update_task_status(
        self, task_id: str, task_status: Optional[TaskStatusEnum] = None
    ) -> None:
        """更新任务状态

        Args:
            task_id: 任务ID
            task_status: 可选的任务状态，如果不提供则根据节点状态推导
        """
        task = self._get_task(task_id)
        if not task.task_runner or not task.running_status or not task.task_node:
            raise TaskStateError(f"Task {task_id} is not properly initialized")

        status = task_status or self._convert_node_status(task_id)
        task.running_status.set_status(status)
        task.running_status.do_record(task.task_node)

    def start_task(self, task_id: str) -> None:
        """启动任务

        Args:
            task_id: 任务ID

        Raises:
            TaskStateError: 任务未正确初始化时抛出
        """
        task = self._get_task(task_id)
        if not task.task_runner:
            raise TaskStateError(f"Task {task_id} runner not initialized")

        self.add_task_running_status(task_id, TaskRunningStatusBo(task_id=task_id))
        task.task_runner.start()
        self.update_task_status(task_id, TaskStatusEnum.PROCESSING)

    def report_task_status(self, task_id: str) -> None:
        """上报任务状态

        Args:
            task_id: 任务ID
        """
        task = self._get_task(task_id)
        if not task.running_status:
            raise TaskStateError(f"Task {task_id} status not initialized")

        try:
            # 移除日志处理器并上传日志文件
            if task.log_handler:
                log.remove(task.log_handler)
                task.log_handler = None

            log_path = object_storage_util.upload_file_2_s3(
                local_file_path=os.path.join(path_conf.LOG_PATH, f"{task_id}.log"),
                remote_file_path=TASK_LOG_PATH.format(file_name=f"{task_id}.log"),
            )
            task.running_status.log_path = log_path

            # 调用完成接口
            resp = do_post(
                settings.ZEUS_DOMAIN + API_TASK_COMPLETE,
                json_data=task.running_status.dump_camel_json(),
                log_req_len=DEFAULT_LOG_LEN,
                log_resp_len=DEFAULT_LOG_LEN,
            )

            data = response_valid(resp, err_msg="task complete request")
            if data.success:
                log.success(f"Task {task_id} report success")
            else:
                log.error(f"Task {task_id} report failed")

        except Exception as e:
            log.exception(f"Failed to report task {task_id} status: {str(e)}")
            raise TaskRuntimeError(task_id, "reporting status", data=str(e)) from e

    def clean_task(self, task_id: str) -> None:
        """清理任务资源

        Args:
            task_id: 任务ID
        """
        task = self._get_task(task_id)

        try:
            if task.log_tree:
                task.log_tree.logger.info(
                    f"Task {task_id} over, removing task instance"
                )

            if task.log_handler:
                log.remove(task.log_handler)

            self._tasks.pop(task_id)

        except Exception as e:
            log.exception(f"Error cleaning task {task_id}: {str(e)}")
            raise TaskRuntimeError(task_id, "cleaning resources", data=str(e)) from e

    def stop_task(self, task_id: str) -> None:
        """停止任务

        Args:
            task_id: 任务ID
        """
        try:
            task = self._get_task(task_id)
            if task.task_runner:
                task.task_runner.stop_task()
            else:
                log.info(f"Task {task_id} runner not found")
        except TaskNotFoundError:
            log.info(f"Task {task_id} not found")
        except Exception as e:
            log.exception(f"Error stopping task {task_id}: {str(e)}")
            raise TaskRuntimeError(task_id, "stopping task", data=str(e)) from e

    def complete_task(self, task_id: str) -> None:
        """任务结束的回调方法

        Args:
            task_id: 任务ID
        """
        try:
            self.update_task_status(task_id)
            self.report_task_status(task_id)
            self.clean_task(task_id)
        except Exception as e:
            log.exception(f"Error completing task {task_id}: {str(e)}")
            raise TaskRuntimeError(task_id, "completing task", data=str(e)) from e


task_manager = TaskInstanceManager()
