import functools
import time
from datetime import datetime
from typing import Optional, Dict

from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.schedulers.background import BackgroundScheduler


from ccontrol_common.core import SingletonMeta
from ccontrol_test_engine_service.config.test_engine_config import (
    DEFAULT_SCHEDULER_THREAD_MAX,
    DEFAULT_CRON_TABLE_LEN,
)


class SchedulerUtil(metaclass=SingletonMeta):
    """
    定时调度工具：
    - 采用apscheduler模块实现
    - BackgroundScheduler后台调度器, 任务默认后台执行
    - 任务存储于内存/可持久化
    - 默认线程数10
    """

    def __init__(self):
        super().__init__()
        self.scheduler = self._get_scheduler()
        self.scheduler.start()

    @staticmethod
    def _get_scheduler():
        """获取调度器"""
        # FIXME: 后续调用该工具类时需要考虑设置最大线程数
        executors = {
            "default": ThreadPoolExecutor(DEFAULT_SCHEDULER_THREAD_MAX),
        }
        return BackgroundScheduler(executors=executors)

    @staticmethod
    def parse_cron_tab(cron_expression: str):
        """
        解析cron表达式

        APScheduler模块无法直接解析crontab表达式
        - crontab格式：分 时 日 月 周
        - * 字段必须转换为None
        - 非 * 字段还原为字典
        """
        cron_tab = cron_expression.split()
        if len(cron_tab) != DEFAULT_CRON_TABLE_LEN:
            raise ValueError("crontab format is error")

        cron_dict: Dict[str, Optional[str]] = {
            "day_of_week": cron_tab[4],
            "month": cron_tab[3],
            "day": cron_tab[2],
            "hour": cron_tab[1],
            "minute": cron_tab[0],
        }

        for key, value in cron_dict.items():
            if value == "*":
                cron_dict[key] = None
        return cron_dict

    def add_job(
        self,
        fun_name,
        fun,
        interval: Optional[int] = None,
        crontab: Optional[str] = None,
    ):
        """
        新增定时任务

        :param fun_name: 方法名
        :param fun: 方法
        :param crontab: crontab表达式：分 时 日 月 星期
        :param interval: 推荐间隔计时
        :return: None
        """
        if interval:
            self.scheduler.add_job(
                fun,
                "interval",
                seconds=interval,
                id=fun_name,
                next_run_time=datetime.now(),
            )
        elif crontab:
            cron_dict = self.parse_cron_tab(crontab)
            self.scheduler.add_job(
                fun,
                "cron",
                id=fun_name,
                next_run_time=datetime.now(),
                day_of_week=cron_dict["day_of_week"],
                month=cron_dict["month"],
                day=cron_dict["day"],
                hour=cron_dict["hour"],
                minute=cron_dict["minute"],
            )

    def get_jobs(self):
        """获取所有任务"""
        return self.scheduler.get_jobs()

    def get_job_by_id(self, job_id: str):
        """根据任务id获取任务"""
        return self.scheduler.get_job(job_id)


scheduler_util = SchedulerUtil()


if __name__ == "__main__":

    def hello(target=None):
        if target:
            print(f"YES {target}")

    scheduler_util.add_job("fun 1", functools.partial(hello, target="LCF"), interval=5)
    scheduler_util.add_job(
        "fun 2", functools.partial(hello, target="fcl"), crontab="*/1 * * * *"
    )

    while True:
        time.sleep(1)
