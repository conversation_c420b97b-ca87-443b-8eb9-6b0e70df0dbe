import asyncio
import threading
import time
from datetime import datetime
from typing import Callable, Dict, Any

from redis.exceptions import (
    ConnectionError as RedisConnectionError,
    TimeoutError as RedisTimeoutError,
)
from pydantic import BaseModel, Field
from pydantic_core import ValidationError
from redis import Redis

from ccontrol_common.core import log
from ccontrol_test_engine_service.config.config import settings
from ccontrol_test_engine_service.config.test_engine_config import (
    REDIS_CHANNEL_STOP,
    CONNECT_TIMEOUT,
)
from ccontrol_test_engine_service.utils.task_instance_manager import task_manager


class RedisSignal(BaseModel):
    """Redis信号"""

    task_id: str = Field(alias="taskId")
    timestamp: datetime


class RedisClient(threading.Thread):
    """Redis 监听客户端"""

    _channel_callback: Dict[str, Callable[[Any], None]]

    def __init__(self):
        super().__init__()
        self.redis_client: Redis
        self.loop: asyncio.AbstractEventLoop
        self._channel_callback = {}

    def run(self):
        """注册回调 and 启动监听"""
        self.register_fun()
        self.start_listen()

    def register_fun(self):
        """注册回调函数"""
        self._channel_callback[REDIS_CHANNEL_STOP] = self.handle_stop_msg

    def is_connected(self):
        """判断redis是否连接"""
        if hasattr(self, "redis_client") and self.redis_client:
            try:
                return self.redis_client.ping()
            except RedisConnectionError:
                log.info("redis check failed while ping server")
        return False

    def connect_redis(self):
        """连接到redis服务端"""
        start_time = time.time()
        while not self.is_connected():
            try:
                self.redis_client = Redis.from_url(settings.REDIS_SERVER)
            except RedisConnectionError:
                log.info("redis connect error")
            except RedisTimeoutError:
                log.info("redis connect timeout")
            if time.time() - start_time > CONNECT_TIMEOUT:
                return False
            time.sleep(0.1)
        return True

    def start_listen(self):
        """redis 设置订阅"""
        log.info("[Redis Listener] start redis channel listen")
        if not self.connect_redis():
            log.error("connect redis failed, please check")
            return

        with self.redis_client.pubsub() as pubsub:
            for channel, callback in self._channel_callback.items():
                pubsub.subscribe(**{channel: callback})
            for message in pubsub.listen():
                log.info(f"redis client receive message: {message}")

    def handle_stop_msg(self, msg: dict):
        """redis message handler"""
        if msg is not None:
            target_channel = str(msg["channel"], encoding="utf8")
            if target_channel not in self._channel_callback:
                return
            try:
                signal = RedisSignal.model_validate_json(msg["data"])
            except ValidationError as e:
                log.info(f"redis signal is error: {e}")
                return

            task_manager.stop_task(signal.task_id)
