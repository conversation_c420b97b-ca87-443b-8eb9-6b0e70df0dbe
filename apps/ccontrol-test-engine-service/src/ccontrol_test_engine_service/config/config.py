from functools import lru_cache

from ccontrol_common.config.conf import Settings


class ServiceSettings(Settings):
    """配置类"""

    # FastAPI
    TITLE: str = "CControl Test Engine Service"
    VERSION: str = "v0.1"
    DESCRIPTION: str = """
[redocs](/v1/redocs)
    """
    DOCS_URL: str = "/v1/docs"
    REDOCS_URL: str = "/v1/redocs"
    OPENAPI_URL: str = "/v1/openapi"
    SWAGGER_UI_OAUTH2_REDIRECT_URL: str = "/docs/oauth2-redirect"

    # Uvicorn
    UVICORN_HOST: str = "0.0.0.0"
    UVICORN_PORT: int = 30001
    UVICORN_RELOAD: bool = True
    # 如果此处为True，在 @ccontrol_test_engine.on_event("startup") 时发生异常，则程序不会终止，详情：https://github.com/encode/starlette/issues/486
    UVICORN_WORKERS: int = 1

    # Middleware
    MIDDLEWARE_CORS: bool = True
    MIDDLEWARE_GZIP: bool = True
    MIDDLEWARE_ACCESS: bool = True

    # Redis Config
    REDIS_SERVER: str = "redis://localhost"

    # Zeus Server
    SERVICE_SECRET: str = ""


@lru_cache()
def get_settings():
    """读取配置优化写法"""
    return ServiceSettings()


settings = get_settings()

if __name__ == "__main__":
    print(settings.model_dump_json())
