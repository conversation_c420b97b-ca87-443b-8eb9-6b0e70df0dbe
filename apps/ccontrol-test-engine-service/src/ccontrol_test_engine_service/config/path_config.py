from pathlib import Path


# 模块源代码路径
PACKAGE_SOURCE_PATH = Path(__file__).resolve().parent.parent

# 测试文件路径
TEST_FOLDER_PATH = Path.joinpath(PACKAGE_SOURCE_PATH, "tests")

# 静态文件路径
STATIC_PATH = Path.joinpath(PACKAGE_SOURCE_PATH, "static")

# 远端S3对象存储路径
REMOTE_S3_PREFIX = "test_engine_service"
TASK_LOG_PATH = REMOTE_S3_PREFIX + "/task_logs/{file_name}"


if __name__ == "__main__":
    print(f"PACKAGE_PATH: {PACKAGE_SOURCE_PATH}")
    print(f"TEST_FOLDER_PATH: {TEST_FOLDER_PATH}")
    print(f"STATIC_PATH: {STATIC_PATH}")
