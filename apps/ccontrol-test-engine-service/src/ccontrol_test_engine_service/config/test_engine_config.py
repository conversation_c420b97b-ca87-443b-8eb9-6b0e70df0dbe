"""测试引擎服务常量"""

# cookies认证
AUTH_TYPE = "service"  # 认证类型
SERVICE_ID = "test-engine-service"  # 服务名
DEFAULT_COOKIES_EXPIRES = 24 * 60 * 60  # 默认cookies过期时间
DEFAULT_MAX_RETRY_TIMES = 3  # http请求默认最大尝试次数
DEFAULT_CHECK_CRON_TAB = "* */1 * * *"  # 默认cookies刷新crontab表达式

# 默认上传到S3对象存储的文件类型
DEFAULT_FILE_TYPE = "text/plain"
S3_DOMAIN_DEV = "https://acc.gz.cvte.cn/s3-ccontrol-dev/"

# redis服务
CONNECT_TIMEOUT = 10
REDIS_CHANNEL_STOP = "task:terminate"

# apscheduler服务
DEFAULT_SCHEDULER_THREAD_MAX = 20
DEFAULT_CRON_TABLE_LEN = 5
