[project]
name = "ccontrol-test-engine-service"
description = "CControl Test Engine Service"
authors = [{ name = "l<PERSON><PERSON><PERSON><PERSON>", email = "lian<PERSON><PERSON><PERSON>@cvte.com" }]
dynamic = ["version"]
dependencies = [
  "fastapi<1.0.0,>=0.111.0",
  "uvicorn<1.0.0,>=0.29.0",
  "ccontrol-common",
  "ccontrol-ai-service",
  "redis>=5.2.1",
  "numpy==2.0.2",
  "pandas==2.2.3",
  "apscheduler>=3.9.0"
]
requires-python = "<4,>=3.9"
readme = "README.md"
license = "MIT"

[tool.setuptools]
package-dir = {"" = "src"}
packages = ["ccontrol_test_engine_service"]

[tool.setuptools.dynamic]
version = {attr = "ccontrol_test_engine_service.__version__"}

[tool.ruff]
line-length = 255

[tool.ruff.lint]
select = ["E", "F"]

[build-system]
requires = ["setuptools>=78.1.0"]
build-backend = "setuptools.build_meta"

[dependency-groups]
dev = [
    "pytest>=8.3.4",
]
