import time
from pathlib import Path

import pytest

from ccontrol_test_engine import TestSuiteNodeModel, TestCaseNodeModel, EventNodeModel
from ccontrol_test_engine.service.event.model.event_model import (
    PythonEventModel,
    EventTypeEnum,
)
from ccontrol_test_engine_service.model.task.bo_task_request import (
    TaskInfoBo,
    DeviceInfoBo,
    CreateSingleDeviceTaskBo,
)
from ccontrol_test_engine_service.services.task_service import task_service

app_path = Path(__file__).resolve().parent.parent.parent
example_path = Path.joinpath(app_path, "src/ccontrol_test_engine_service/mock/hello_world.py")
with open(example_path, encoding="utf-8", mode="r") as f:
    PYTHON_CONTENT = f.read()

task_request = CreateSingleDeviceTaskBo(
    task_tree=TaskInfoBo(
        task_id="fcl_task",
        name="fcl_task",
        children=TestSuiteNodeModel(
            test_suite_id="fcl_suite",
            name="fcl_suite",
            children=[],
        ),
    ),
    device=DeviceInfoBo(device_id=80, serial_number="123456", name="test device"),
)

for i in range(2):
    test_case = TestCaseNodeModel(
        test_case_id=f"fcl_case_{i}",
        name=f"fcl_case_{i}",
        children=[],
    )
    for j in range(3):
        test_event = EventNodeModel(
            event_id=f"fcl_event_{i}",
            name=f"fcl_event_{i}",
            data=PythonEventModel(
                type=EventTypeEnum.PYTHON,
                content=PYTHON_CONTENT,
            ),
        )
        test_case.children.append(test_event)
    task_request.task_tree.children.children.append(test_case)


def test_task_create():
    """创建任务测试"""
    task_service.create_task(bo=task_request)
    time.sleep(15)


def test_print_task_request():
    """打印任务请求"""
    task_json = task_request.model_dump_json(indent=2)
    print(task_json)


def test_task_stop():
    """终止任务测试"""
    task_service.create_task(bo=task_request)
    time.sleep(10)
    task_service.stop_task("fcl_task")
    time.sleep(5)


def test_global_vars_isolation():
    """测试不同任务的全局变量隔离"""
    from ccontrol_test_engine.service.task.model.global_model import GlobalStore

    # 创建两个任务，分别设置不同的全局变量
    task1_vars = {"key1": "value1"}
    task1 = CreateSingleDeviceTaskBo(
        task_tree=TaskInfoBo(task_id="task1", name="task1", children=TestSuiteNodeModel(test_suite_id="suite1", name="suite1", children=[])), device=DeviceInfoBo(device_id=80, serial_number="123456", name="test device"), config=task1_vars
    )

    task2_vars = {"key2": "value2"}
    task2 = CreateSingleDeviceTaskBo(
        task_tree=TaskInfoBo(task_id="task2", name="task2", children=TestSuiteNodeModel(test_suite_id="suite2", name="suite2", children=[])), device=DeviceInfoBo(device_id=80, serial_number="123456", name="test device"), config=task2_vars
    )

    # 创建并验证第一个任务
    task_service.create_task(bo=task1)
    global_var1 = GlobalStore().get_global_var()
    assert global_var1.user_global_var == task1_vars

    # 清理第一个任务
    task_service.stop_task("task1")

    # 创建并验证第二个任务
    task_service.create_task(bo=task2)
    global_var2 = GlobalStore().get_global_var()
    assert global_var2.user_global_var == task2_vars

    # 验证变量隔离
    assert "key1" not in global_var2
    assert "key2" in global_var2

    # 清理第二个任务
    task_service.stop_task("task2")

    # 创建带有全局变量的任务请求
    test_vars = {"key1": "value1", "key2": 123}
    test_request = CreateSingleDeviceTaskBo(
        task_tree=TaskInfoBo(task_id="global_var_test", name="global_var_test", children=TestSuiteNodeModel(test_suite_id="global_suite", name="global_suite", children=[])),
        device=DeviceInfoBo(device_id=80, serial_number="123456", name="test device"),
        config=test_vars,
    )

    # 创建任务
    task_service.create_task(bo=test_request)

    # 验证全局变量注入
    global_var = GlobalStore().get_global_var()
    assert global_var is not None
    assert global_var.user_global_var == test_vars
    assert global_var["key1"] == "value1"
    assert global_var["key2"] == 123

    # 清理任务
    task_service.stop_task("global_var_test")


if __name__ == "__main__":
    pytest.main(["-s", "-v"])
