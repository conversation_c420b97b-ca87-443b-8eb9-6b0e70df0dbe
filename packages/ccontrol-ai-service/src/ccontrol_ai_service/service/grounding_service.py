import re
import time
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from dataclasses import dataclass
from typing import Generic, List, Literal, Optional, Tuple, TypeVar, Union

from PIL import ImageDraw
from PIL.Image import Image
from openai import OpenAI
from rapidfuzz import fuzz, process
from rapidfuzz.utils import default_process

from ccontrol_ai_service.common.constans import (
    BoxLabelColorEnum,
    VQAModelEnum,
    API_VQA,
    generate_grounding_prompt,
)
from ccontrol_ai_service.interface.device import DeviceInterface
from ccontrol_ai_service.service.image_compare_service import ImageCompareServiceImpl
from ccontrol_ai_service.service.roboflow_service import (
    RoboflowImage,
    RoboflowServiceImpl,
)
from ccontrol_ai_service.service.yolo_service import YOLOServiceImpl
from ccontrol_common.config.conf import settings
from ccontrol_common.core.http_client import httpx_client
from ccontrol_common.core.log import log
from ccontrol_common.utils.image import ImageUtil

T_Device = TypeVar("T_Device", bound=DeviceInterface)


@dataclass
class ImageMatch:
    """图像匹配结果"""

    confidence: float
    image: Optional[RoboflowImage]

    @property
    def is_valid(self) -> bool:
        """判断是否为有效匹配"""
        return self.confidence >= 0.85 and self.image is not None


@dataclass
class DetectionMatch:
    """检测框匹配结果"""

    box: List[int]
    confidence: float


@dataclass
class ComparisonTask:
    """图像比对任务"""

    cropped_base64: str  # 裁剪后的图像base64
    target_image: RoboflowImage  # 目标图像信息
    box: List[int]  # 检测框坐标 [x1, y1, x2, y2]


class GroundingServiceImpl(Generic[T_Device]):
    """Grounding 服务实现"""

    device: T_Device
    roboflow_service: RoboflowServiceImpl
    yolo_service: YOLOServiceImpl
    image_compare_service: ImageCompareServiceImpl

    def __init__(
        self,
        device: T_Device,
        roboflow_service: Optional[RoboflowServiceImpl] = None,
        yolo_service: Optional[YOLOServiceImpl] = None,
        image_compare_service: Optional[ImageCompareServiceImpl] = None,
    ):
        self.device = device
        self.roboflow_service = roboflow_service or RoboflowServiceImpl()
        self.yolo_service = yolo_service or YOLOServiceImpl()
        self.image_compare_service = image_compare_service or ImageCompareServiceImpl()

    def _convert_coordinate(self, image: Image, box: list):
        """
        将Grounding模型返回的归一化坐标转换为绝对像素坐标

        Args:
            image: PIL Image对象
            box: 包含归一化坐标的列表 [x1, y1, x2, y2], 取值范围0-1

        Returns:
            list: 包含转换后绝对坐标的列表 [x1, y1, x2, y2]
        """
        current_width, current_height = image.size
        for i, coord in enumerate(box):
            if coord < 1:
                if i % 2 == 0:
                    box[i] = int(coord * current_width)
                else:
                    box[i] = int(coord * current_height)
        return box

    def _draw_boxes(self, image: Image, boxes: list, box_color: Union[BoxLabelColorEnum, str]):
        """
        在图像上绘制边界框

        Args:
            image: PIL Image对象
            boxes: 包含多个边界框坐标的列表,每个边界框格式为[x1, y1, x2, y2]
            box_color: 边界框颜色,可以是BoxLabelColorEnum枚举值或颜色字符串

        Returns:
            Image: 绘制了边界框的图像对象
        """
        if isinstance(box_color, BoxLabelColorEnum):
            box_color = box_color.value
        box_width = 5
        draw = ImageDraw.Draw(image)
        for box in boxes:
            x1, y1, x2, y2 = box
            draw.rectangle(
                [x1, y1, x2, y2],
                outline=box_color,
                width=box_width,
            )
        return image

    def _find_matching_classes(self, target: str, all_classes: List[str]) -> List[str]:
        """
        查找匹配的类名,采用多级匹配策略

        匹配策略按优先级:
        1. 精确匹配(不区分大小写)
        2. 基于 token_set_ratio 的模糊匹配(处理词序、分词不同的情况)

        Args:
            target: 目标名称,如 'YouTube 图标'
            all_classes: 所有可用的类名列表

        Returns:
            List[str]: 匹配到的类名列表,按匹配度排序
        """
        if not target or not all_classes:
            return []

        matched_classes = []
        target_lower = target.lower()

        # 1. 精确匹配 (不区分大小写)
        for class_name in all_classes:
            if target_lower == class_name.lower():
                matched_classes.append(class_name)
        if matched_classes:
            return matched_classes

        # 2. 使用 token_set_ratio 进行模糊匹配
        matches = process.extract(
            query=target,
            choices=all_classes,
            scorer=fuzz.ratio,
            score_cutoff=80,
            processor=default_process,
        )
        if matches:
            # 按相似度评分从高到低排序
            return [match[0] for match in sorted(matches, key=lambda x: x[1], reverse=True)]

        return []

    def _try_roboflow_detection(self, target: str, image: Image) -> Optional[List[List[int]]]:
        """
        尝试使用 Roboflow 前置检测

        Args:
            target: 目标名称
            image: 待检测图像

        Returns:
            Optional[List[List[int]]]: 检测到目标时返回边界框列表，否则返回None。
            边界框格式为 [[x1, y1, x2, y2], ...]，按置信度从高到低排序。
        """
        # 步骤1: 获取并匹配目标类别
        start_time = time.time()
        all_classes = self.roboflow_service.get_all_classes()
        class_time = time.time() - start_time
        log.debug(f"[Roboflow] 获取类别列表耗时: {class_time:.3f}s，共 {len(all_classes)} 个类别")

        matched_classes = self._find_matching_classes(target, all_classes)
        if not matched_classes:
            log.info(f"[Roboflow] 未在类别库中找到目标: {target}")
            return None

        matched_class = matched_classes[0]
        log.debug(f"[Roboflow] 类别匹配结果: {matched_class}")

        # 步骤2: 获取目标对应的图像
        start_time = time.time()
        roboflow_images = self.roboflow_service.get_images(classes=matched_class)
        images_time = time.time() - start_time
        log.debug(f"[Roboflow] 获取参考图像耗时: {images_time:.3f}s，共 {len(roboflow_images)} 张图像")

        if not roboflow_images:
            log.info(f"[Roboflow] 未找到类别 '{matched_class}' 的参考图像")
            return None

        # 步骤3: 使用YOLO检测可交互元素
        image_url = ImageUtil.convert_to_base64(image)
        start_time = time.time()
        detect_result = self.yolo_service.detect_interactive_elements(image_url)
        detect_time = time.time() - start_time

        detection_count = len(detect_result.detections) if detect_result.detections else 0
        log.debug(f"[Roboflow] YOLO检测耗时: {detect_time:.3f}s，检测到 {detection_count} 个可交互元素")

        if not detect_result.detections:
            log.info("[Roboflow] 未检测到可交互元素")
            return None

        # 步骤4: 准备图像对比任务
        comparison_tasks = self._prepare_comparison_tasks(image, detect_result.detections, roboflow_images)
        if not comparison_tasks:
            log.info("[Roboflow] 未能准备有效的对比任务")
            return None

        # 步骤5: 并行执行所有图像对比任务
        start_time = time.time()
        matching_detections = self._execute_comparison_tasks(comparison_tasks, target)
        compare_time = time.time() - start_time
        log.debug(f"[Roboflow] 图像比对耗时: {compare_time:.3f}s，找到 {len(matching_detections)} 个匹配结果")

        # 步骤6: 处理匹配结果
        if matching_detections:
            # 按置信度从高到低排序
            matching_detections.sort(key=lambda x: x.confidence, reverse=True)
            log.info(f"[Roboflow] 检测完成 - 目标: {target}, 匹配类别: {matched_class}, 找到 {len(matching_detections)} 个匹配区域")
            return [match.box for match in matching_detections]

        return None

    def _prepare_comparison_tasks(self, image: Image, detections: list, reference_images: List[RoboflowImage]) -> List[ComparisonTask]:
        """
        准备图像对比任务

        Args:
            image: 原始图像
            detections: YOLO检测结果
            reference_images: 参考图像列表

        Returns:
            List[ComparisonTask]: 比对任务列表
        """
        tasks = []
        start_time = time.time()

        for detection in detections:
            bbox = detection.get_bounding_box()
            box = [
                int(bbox.x_min),
                int(bbox.y_min),
                int(bbox.x_max),
                int(bbox.y_max),
            ]

            try:
                cropped_image = image.crop((bbox.x_min, bbox.y_min, bbox.x_max, bbox.y_max))
                cropped_base64 = ImageUtil.convert_to_base64(cropped_image)

                # 为每个检测框和每个目标图像创建比对任务
                for target_image in reference_images:
                    tasks.append(
                        ComparisonTask(
                            cropped_base64=cropped_base64,
                            target_image=target_image,
                            box=box,
                        )
                    )
            except Exception as e:
                log.debug(f"[Roboflow] 区域处理异常: {str(e)}")
                continue

        prep_time = time.time() - start_time
        log.debug(f"[Roboflow] 准备对比任务耗时: {prep_time:.3f}s，共 {len(tasks)} 个任务")
        return tasks

    def _execute_comparison_tasks(self, tasks: List[ComparisonTask], target: str) -> List[DetectionMatch]:
        """执行图像比对任务，使用批量图像比对接口提高效率"""
        matching_detections = []
        box_best_matches = {}

        if not tasks:
            return []

        start_time = time.time()

        try:
            image_pairs = []
            task_mapping = {}

            for i, task in enumerate(tasks):
                image_pairs.append((task.cropped_base64, task.target_image.base64))
                task_mapping[i] = task

            batch_results = self.image_compare_service.compare_images_batch(image_pairs)

            for i, result in enumerate(batch_results):
                task = task_mapping[i]
                match = ImageMatch(confidence=result.confidence, image=task.target_image)
                box_tuple = tuple(task.box)

                if box_tuple not in box_best_matches or match.confidence > box_best_matches[box_tuple].confidence:
                    box_best_matches[box_tuple] = match

            batch_time = time.time() - start_time
            log.debug(f"批量图像比对完成: {len(image_pairs)} 个任务, 耗时: {batch_time:.3f}s")

        except Exception as e:
            log.error(f"批量图像比对异常: {str(e)}")
            log.info("回退到单张图像比对模式")

            def __compare_single_task(task: ComparisonTask) -> Tuple[ImageMatch, List[int]]:
                try:
                    result = self.image_compare_service.compare_images(task.cropped_base64, task.target_image.base64)
                    return (
                        ImageMatch(confidence=result.confidence, image=task.target_image),
                        task.box,
                    )
                except Exception as e:
                    log.debug(f"图像比对异常: {str(e)}")
                    return ImageMatch(confidence=0.0, image=None), task.box

            with ThreadPoolExecutor(max_workers=20) as executor:
                futures = []
                for task in tasks:
                    futures.append(executor.submit(__compare_single_task, task))

                for future in as_completed(futures):
                    try:
                        match, box = future.result()
                        box_tuple = tuple(box)

                        if box_tuple not in box_best_matches or match.confidence > box_best_matches[box_tuple].confidence:
                            box_best_matches[box_tuple] = match
                    except Exception as e:
                        log.debug(f"处理比对结果异常: {str(e)}")
                        continue

            fallback_time = time.time() - start_time
            log.debug(f"单张图像比对回退完成: {len(tasks)} 个任务, 耗时: {fallback_time:.3f}s")

        for box_tuple, match in box_best_matches.items():
            if match.is_valid:
                box = list(box_tuple)
                detection_match = DetectionMatch(box=box, confidence=match.confidence)
                matching_detections.append(detection_match)

                if match.image:
                    log_info = match.image.model_dump(exclude={"base64"})
                    log.info(f"[Roboflow] 目标匹配成功 - 目标: {target}, 置信度：{match.confidence:.2f}, 目标位置：{box}, 匹配信息: {log_info}")

        return matching_detections

    def _parse_vlm_response(self, response_content: str, image_size: Tuple[int, int]) -> List[List[int]]:
        """
        解析VLM模型的响应内容，提取边界框坐标

        Args:
            response_content (str): VLM模型的原始响应内容
            image_size (Tuple[int, int]): 图像的宽高尺寸 (width, height)

        Returns:
            List[List[int]]: 解析得到的边界框坐标列表，格式为 [[x1, y1, x2, y2], ...]
        """
        width, height = image_size
        boxes = []

        # 尝试匹配不同格式的坐标表示
        # 1. 匹配形如 [x1, y1, x2, y2] 或 (x1, y1, x2, y2) 的格式
        pattern1 = r"[\[\(](\d+)[,\s]+(\d+)[,\s]+(\d+)[,\s]+(\d+)[\]\)]"
        # 2. 匹配形如 x1,y1,x2,y2 的格式
        pattern2 = r"(\d+),(\d+),(\d+),(\d+)"

        # 首先尝试使用方括号或圆括号的格式匹配
        matches = re.findall(pattern1, response_content)

        # 如果没有找到匹配，尝试使用逗号分隔的格式
        if not matches:
            matches = re.findall(pattern2, response_content)

        for match in matches:
            try:
                # 将字符串转换为整数
                coords = [int(coord) for coord in match]

                # 过滤掉全零坐标
                if all(coord == 0 for coord in coords):
                    continue

                # 判断是否为相对坐标(0-1范围内)或归一化坐标(0-1000范围内)
                if all(0 <= coord <= 1 for coord in coords):
                    # 相对坐标，转换为绝对像素坐标
                    x1, y1, x2, y2 = coords
                    x1, x2 = int(x1 * width), int(x2 * width)
                    y1, y2 = int(y1 * height), int(y2 * height)
                    boxes.append([x1, y1, x2, y2])
                elif all(0 <= coord <= 1000 for coord in coords):
                    # 归一化坐标(0-1000)，转换为绝对像素坐标
                    x1, y1, x2, y2 = [int(coord / 1000 * (width if i % 2 == 0 else height)) for i, coord in enumerate(coords)]
                    boxes.append([x1, y1, x2, y2])
                else:
                    # 假设已经是绝对像素坐标
                    boxes.append(coords)
            except Exception as e:
                log.debug(f"解析坐标失败: {str(e)}, 匹配结果: {match}")
                continue

        return boxes

    def _vlm_grounding(
        self,
        target: str,
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        model: Optional[Union[VQAModelEnum, str]] = None,
        system_prompt: Optional[str] = None,
    ) -> List[List[int]]:
        """
        使用视觉语言模型(VLM)进行目标定位并返回解析后的边界框坐标

        通过向视觉语言模型提供图像和文本描述，定位图像中的特定目标对象，
        返回目标的边界框信息。

        Args:
            target (str): 需要定位的目标描述
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            model (Optional[Union[VQAModelEnum, str]]): 使用的VLM模型，可以是VQAModelEnum枚举或字符串，默认为None时使用CVTE_LATEST
            system_prompt (Optional[str]): 系统提示词，用于引导模型生成特定格式的边界框信息

        Returns:
            List[List[int]]: 解析得到的边界框坐标列表，格式为 [[x1, y1, x2, y2], ...]

        Raises:
            AssertionError: 当目标定位失败时抛出
        """
        prompt = generate_grounding_prompt(target)

        # 获取截图并转换为 base64
        if not image:
            image = self.device.screenshot()

        # 确保图像转换为 PIL.Image 对象，以便后续处理
        pil_image = ImageUtil.convert_to_image(image)
        image_base64 = ImageUtil.convert_to_base64(pil_image)

        # 若未传入模型，则自动选择模型
        if not model:
            model = VQAModelEnum.CVTE_LATEST

        openai_client = OpenAI(
            base_url=settings.ZEUS_DOMAIN + "/v1",
            api_key="sk-******",
            http_client=httpx_client.get_client(),
        )

        # 调用 VLM 模型进行目标定位
        messages = [
            {"role": "system", "content": system_prompt if system_prompt else ""},
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"{image_base64}"},
                    },
                    {"type": "text", "text": prompt},
                ],
            },
        ]
        messages = [item for item in messages if item]

        start_time = time.time()
        response = openai_client.chat.completions.create(
            messages=messages,
            model=model.value if isinstance(model, VQAModelEnum) else model,
        )
        response_time = time.time() - start_time
        log.debug(f"[VLM Grounding] API调用耗时: {response_time:.3f}s")

        response_content = response.choices[0].message.content

        if not response_content:
            raise AssertionError(f"目标定位失败, response: {response.model_dump_json()}")

        # 调用新封装的函数解析响应内容
        boxes = self._parse_vlm_response(response_content, pil_image.size)

        log.debug(f"[VLM Grounding] 解析到 {len(boxes)} 个目标边界框")
        return boxes

    def grounding(
        self,
        target: str,
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        box_color: Optional[Union[BoxLabelColorEnum, str]] = None,
        is_crop: bool = False,
    ) -> Union[List[List[int]], Image]:
        """
        执行目标检测并返回目标边界框坐标或处理后的图像

        Args:
            target (str): 要检测的目标名称或描述
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            box_color (Optional[Union[BoxLabelColorEnum, str]]): 边界框颜色，指定时返回带标注框的图像
            is_crop (bool): 是否裁剪图像，为True时返回裁剪后的第一个目标区域

        Returns:
            Union[List[List[int]], Image]:
                - 默认返回边界框坐标列表，每个边界框格式为[x1, y1, x2, y2]
                - 指定box_color时返回带标注的原始图像
                - 指定is_crop时返回裁剪后的第一个目标区域图像

        Raises:
            AssertionError: 目标检测请求失败时抛出
            TypeError: 图像格式转换失败时抛出
        """
        # 如果未指定图像，则默认使用设备截图
        if not image:
            image = self.device.screenshot()

        image = ImageUtil.convert_to_image(image)

        # 记录整体检测开始时间
        total_start_time = time.time()

        # 尝试使用 Roboflow 前置检测
        detected_boxes: Optional[List[List[int]]] = self._try_roboflow_detection(target, image)

        roboflow_time = time.time() - total_start_time
        log.info(f"[Grounding] Roboflow前置检测总耗时: {roboflow_time:.3f}s, 检测结果: {'成功' if detected_boxes else '失败'}")

        if detected_boxes:
            if box_color:
                return self._draw_boxes(image, detected_boxes, box_color)
            if is_crop and detected_boxes:
                x1, y1, x2, y2 = detected_boxes[0]  # type: ignore
                return image.crop((x1, y1, x2, y2))
            return detected_boxes

        # 如果 Roboflow 检测未找到目标，使用 VLM Grounding 进行定位
        log.info("[Grounding] Roboflow检测未找到目标，切换到VLM Grounding模型")
        grounding_start_time = time.time()

        # 直接调用内部的 VLM Grounding 方法
        try:
            boxes = self._vlm_grounding(target, image=image)
        except Exception as e:
            log.error(f"[Grounding] VLM Grounding 调用异常: {str(e)}")
            raise AssertionError(f"VLM Grounding 目标检测失败: {str(e)}")

        api_time = time.time() - grounding_start_time
        log.info(f"[Grounding] VLM Grounding调用耗时: {api_time:.3f}s")

        total_time = time.time() - total_start_time
        log.info(f"[Grounding] 目标检测总耗时: {total_time:.3f}s, 检测到 {len(boxes)} 个目标")

        # 当指定了框选颜色时，返回带有框选的截图
        if box_color:
            return self._draw_boxes(image, boxes, box_color)

        # 当指定了裁剪参数时，返回裁剪后的图片
        if is_crop and boxes:
            x1, y1, x2, y2 = boxes[0]
            crop_img = image.crop((x1, y1, x2, y2))
            return crop_img

        return boxes

    def grounding_find_first_center_point(self, target: str, image: Optional[Union[Image, str, bytes]] = None) -> Optional[List[float]]:
        """
        获取首个检测目标的中心点坐标

        Args:
            target (str): 要检测的目标名称或描述
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图

        Returns:
            Optional[List[float]]: 目标中心点坐标[x, y]，未检测到目标时返回None

        Raises:
            TypeError: 检测结果类型错误时抛出
        """
        boxes = self.grounding(target, image=image)
        if not boxes:
            return None
        if not isinstance(boxes, list):
            raise TypeError("Grounding 目标检测失败，返回类型有误，期望得到坐标列表")

        x1, y1, x2, y2 = boxes[0]
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        return [center_x, center_y]

    def grounding_find_first_box(self, target: str, image: Optional[Union[Image, str, bytes]] = None) -> Optional[List[int]]:
        """
        获取首个检测目标的边界框坐标

        Args:
            target (str): 要检测的目标名称或描述
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图

        Returns:
            Optional[List[int]]: 目标边界框坐标[x1, y1, x2, y2]，未检测到目标时返回None

        Raises:
            TypeError: 检测结果类型错误时抛出
        """
        boxes = self.grounding(target, image=image)
        if not boxes:
            return None
        if not isinstance(boxes, list):
            raise TypeError("Grounding 目标检测失败，返回类型有误，期望得到坐标列表")
        return boxes[0]

    def grounding_batch(
        self,
        targets: List[str],
        *,
        image: Optional[Union[Image, str, bytes]] = None,
    ) -> List[List[List[int]]]:
        """
        批量执行目标检测并返回目标边界框坐标

        Args:
            targets (List[str]): 要检测的目标名称或描述列表
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图

        Returns:
            List[List[List[int]]]: 每个目标的边界框坐标列表

        Raises:
            ValueError: 当使用不支持的模型时抛出
        """
        if not image:
            image = self.device.screenshot()
        image = ImageUtil.convert_to_image(image)
        image_base64 = ImageUtil.convert_to_base64(image)

        # 构造请求数据
        message_group = []
        for target in targets:
            message_group.append([{"role": "system", "content": ""}, {"role": "user", "content": [{"type": "text", "text": generate_grounding_prompt(target)}]}])

        data = {
            "messages": [{"role": "system", "content": "messages占位用，在AI API聚合平台会自动移除"}],
            "message_group": message_group,
            "image_url": image_base64,
            "model": VQAModelEnum.CVTE_LATEST.value,
        }

        # 使用httpx client发送请求
        client = httpx_client.get_client()
        response = client.post(
            settings.ZEUS_DOMAIN + API_VQA,
            json=data,
        )
        response.raise_for_status()

        # 解析响应
        try:
            result = response.json()
            answers = [choice["text"] for choice in result["choices"]]
            return [self._parse_vlm_response(answer.strip(), image.size) for answer in answers]
        except Exception as e:
            raise AssertionError(f"批量文本基础验证失败: {str(e)}") from e

    def exists(
        self,
        targets: Union[str, List[str]],
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        kind: Literal["all", "any"] = "all",
    ) -> bool:
        """
        判断目标是否存在

        Args:
            targets (Union[str, List[str]]): 待验证的目标
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            kind (Literal["all", "any"]): 验证方式，为"all"时所有目标都必须存在，为"any"时只要有一个目标存在即可

        Returns:
            bool: 验证结果
        """
        if isinstance(targets, str):
            targets = [targets]

        boxes_list = self.grounding_batch(targets, image=image)

        if kind == "all":
            return all(boxes_list)
        return any(boxes_list)


if __name__ == "__main__":
    from pydantic import BaseModel

    class DeviceImpl(DeviceInterface):
        def screenshot(self) -> Union[Image, bytes, str]:
            image_url = "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png"
            return image_url

    # 定义一个示例响应格式
    class TestResponse(BaseModel):
        has_button: bool
        button_text: str

    _device = DeviceImpl()
    grounding_service = GroundingServiceImpl(_device)

    # 测试 vqa_object
    # result = grounding_service.grounding(target="百度", box_color="red")
    # if isinstance(result, Image):
    #     result.show()
    # print(f"🚀 Result: {result}")

    result = grounding_service.grounding_batch(["百度", "百度一下"])
    print(f"🚀 Result: {result}")

    result = grounding_service.exists(["百度", "谷歌"], kind="all")
    print(f"🚀 Result: {result}")

    result = grounding_service.exists(["百度", "谷歌"], kind="any")
    print(f"🚀 Result: {result}")
