from typing import Generic, TypeVar
from ccontrol_ai_service.interface.device import DeviceInterface
from ccontrol_common.services.ccontrol_service import valid_login
from .grounding_service import (
    GroundingServiceImpl,
)
from .ocr_service import OCRServiceImpl
from .vqa_service import VQAServiceImpl

T_Device = TypeVar("T_Device", bound=DeviceInterface)


class AIServiceImpl(GroundingServiceImpl[T_Device], OCRServiceImpl[T_Device], VQAServiceImpl[T_Device], Generic[T_Device]):
    """AI 服务实现"""

    device: T_Device

    def __init__(self, device: T_Device):
        valid_login()
        super().__init__(device)
