import json
from abc import ABC, abstractmethod
from typing import Generic, Optional, Type, TypeVar, Union, List, Tuple

from openai import OpenAI
from openai.types.chat import ChatCompletion, ChatCompletionMessageParam

from PIL.Image import Image
from pydantic import BaseModel, ValidationError

from ccontrol_ai_service.common.constans import SYSTEM_PROMPT_JSON_RESPONSE, SYSTEM_PROMPT_VQA, SYSTEM_PROMPT_VQA_OBJECT, BaseModelT, VQAModelEnum, API_VQA
from ccontrol_ai_service.interface.device import DeviceInterface
from ccontrol_common.config.conf import settings
from ccontrol_common.core import log
from ccontrol_common.core.http_client import httpx_client
from ccontrol_common.utils.image import ImageUtil
from ccontrol_common.utils.re_verify import extract_markdown_code_block_content

T_Device = TypeVar("T_Device", bound=DeviceInterface)


class VqaModelFactory:
    @staticmethod
    def create(model: Union[VQAModelEnum, str], system_prompt: Optional[str]) -> "VqaModel":
        if VQAModelEnum.CVTE_LATEST.equals(model):
            return CvteModel(system_prompt)
        return OpenaiModel(model, system_prompt)


class VqaModel(ABC):
    @abstractmethod
    def build_messages(self, question: str, image_base64: str, system_prompt: Optional[str] = None) -> List[ChatCompletionMessageParam]:
        pass

    @abstractmethod
    def query(self, messages: List[ChatCompletionMessageParam]) -> ChatCompletion:
        pass

    @abstractmethod
    def parse_response(self, response: ChatCompletion) -> str:
        pass


class CvteModel(VqaModel):
    def __init__(self, system_prompt: Optional[str]):
        self.system_prompt = system_prompt
        self.client = OpenAI(
            base_url=settings.ZEUS_DOMAIN + "/v1",
            api_key="sk-******",
            http_client=httpx_client.get_client(),
        )

    def build_messages(self, question: str, image_base64: str, system_prompt: Optional[str] = None) -> List[ChatCompletionMessageParam]:
        return [
            {"role": "system", "content": "请回答是的或者不是。"},
            {
                "role": "user",
                "content": [{"type": "image_url", "image_url": {"url": image_base64}}, {"type": "text", "text": question}],
            },
        ]

    def query(self, messages: List[ChatCompletionMessageParam]) -> ChatCompletion:
        return self.client.chat.completions.create(
            messages=messages,
            model=VQAModelEnum.CVTE_LATEST.value,
            max_tokens=10,
            temperature=0.2,
            seed=2841,
        )

    def query_with_retry(self, messages: List[ChatCompletionMessageParam], retry_count: int = 1) -> Tuple[ChatCompletion, str]:
        response = self.client.chat.completions.create(
            messages=messages,
            model=VQAModelEnum.CVTE_LATEST.value,
            max_tokens=10,
            temperature=0.2,
            seed=2841,
        )

        try:
            content = response.choices[0].message.content
            if not content:
                raise AssertionError("Empty response content")
            return response, content.strip()
        except Exception as e:
            if retry_count <= 0:
                raise AssertionError(f"CVTE模型重试失败: {str(e)}") from e

            log.warning(f"CVTE模型解析异常，进行重试。剩余重试次数: {retry_count}")
            retry_messages = messages + [
                {"role": "assistant", "content": response.choices[0].message.content},
                {"role": "user", "content": "请回答是的或者不是。"},
            ]
            return self.query_with_retry(retry_messages, retry_count - 1)

    def parse_response(self, response: ChatCompletion) -> str:
        response_content = response.choices[0].message.content
        if not response_content:
            raise AssertionError(f"VQA 答案生成失败, response: {response.model_dump_json()}")
        return extract_markdown_code_block_content(response_content).strip()


class OpenaiModel(VqaModel):
    def __init__(self, model: Union[VQAModelEnum, str], system_prompt: Optional[str]):
        self.model = model
        self.system_prompt = system_prompt

    def build_messages(self, question: str, image_base64: str, system_prompt: Optional[str] = None) -> List[ChatCompletionMessageParam]:
        messages: List[Optional[ChatCompletionMessageParam]] = [
            {"role": "system", "content": system_prompt} if system_prompt else None,
            {"role": "user", "content": "Assertion: 这是TV画面吗?"},
            {"role": "assistant", "content": "Yes"},
            {
                "role": "user",
                "content": [{"type": "image_url", "image_url": {"url": image_base64}}, {"type": "text", "text": f"Assertion: {question}"}],
            },
        ]
        return [msg for msg in messages if msg is not None]

    def query(self, messages: List[ChatCompletionMessageParam]) -> ChatCompletion:
        openai_client = OpenAI(
            base_url=settings.ZEUS_DOMAIN + "/v1",
            api_key="sk-******",
            http_client=httpx_client.get_client(),
        )
        return openai_client.chat.completions.create(
            messages=messages,
            model=self.model.value if isinstance(self.model, VQAModelEnum) else self.model,
            temperature=0.2,
            seed=2841,
        )

    def parse_response(self, response: ChatCompletion) -> str:
        response_content = response.choices[0].message.content
        if not response_content:
            raise AssertionError(f"VQA 答案生成失败, response: {response.model_dump_json()}")
        return extract_markdown_code_block_content(response_content).strip()


class VQAServiceImpl(Generic[T_Device]):
    """VQA服务实现"""

    # 定义标准答案列表
    LABEL_FALSE_LIST = [
        "false",
        "no",
        "否",
        "不是",
        "不对",
        "错误",
        "假",
        "假的",
        "['不是']",
        "无",
        "没有",
        "不存在",
        "看不到",
        "没看到",
        "看不见",
        "未显示",
        "没显示",
        "没出现",
        "不可见",
        "未出现",
    ]
    LABEL_TRUE_LIST = [
        "true",
        "yes",
        "是",
        "是的",
        "对",
        "正确",
        "真",
        "真的",
        "['是的']",
        "有",
        "存在",
        "看到",
        "看得到",
        "能看到",
        "可以看到",
        "显示",
        "显示了",
        "出现",
        "出现了",
        "已经有了",
    ]

    device: T_Device

    def __init__(self, device: T_Device):
        self.device = device

    @staticmethod
    def _auto_select_model(question: str) -> VQAModelEnum:
        """自动选择模型"""
        default_model = VQAModelEnum.GEMINI_LATEST

        # model rule:
        openai_keywords = ["开关", "进度条", "button", "switch", "progress"]
        for keyword in openai_keywords:
            if keyword.lower() in question.lower():
                return VQAModelEnum.OPENAI_LATEST

        return default_model

    @staticmethod
    def _verify_answer(response_content: str, model_dump_json: str) -> bool:
        """
        校验答案并返回布尔值结果
        Args:
            response_content: 模型返回的原始答案
            model_dump_json: 模型响应的完整JSON，用于错误提示
        Returns:
            bool: True 表示肯定答案，False 表示否定答案
        Raises:
            AssertionError: 当答案不在预定义的标签列表中时抛出
        """
        # 转换为小写并去除首尾空白
        normalized_content = response_content.lower().strip()

        # 检查是否在假值列表中（假值判定必须优先于真值，因为假值内有概率包含真值的词）
        if any(label.lower() in normalized_content for label in VQAServiceImpl.LABEL_FALSE_LIST):
            return False

        # 检查是否在真值列表中
        if any(label.lower() in normalized_content for label in VQAServiceImpl.LABEL_TRUE_LIST):
            return True

        # 都不在则抛出异常
        raise AssertionError(f"VQA 答案生成失败, response: {model_dump_json}, error: {response_content}")

    def ask_vlm(
        self,
        question: str,
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        model: Optional[Union[VQAModelEnum, str]] = None,
        system_prompt: Optional[str] = None,
    ):
        """
        使用视觉语言模型(VLM)进行图像开放性问答
        Args:
            question (str): 待回答的问题
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            model (Optional[Union[VQAModelEnum, str]]): 使用的VQA模型,可以是VQAModelEnum枚举或字符串,默认为None时自动选择合适的模型
            system_prompt (Optional[str]): 系统提示词,用于引导模型生成特定类型的回答

        Returns:
            str: 模型生成的文本回答

        Raises:
            AssertionError: 当VQA回答生成失败时抛出
        """
        # 获取截图并转换为 base64
        if not image:
            image = self.device.screenshot()
        image_base64 = ImageUtil.convert_to_base64(image)

        # 若未传入模型，则自动选择模型
        if not model:
            model = self._auto_select_model(question)

        is_cvte_model = VQAModelEnum.CVTE_LATEST.equals(model)
        if is_cvte_model:
            raise ValueError("CVTE 模型不支持 ask_vlm")

        openai_client = OpenAI(
            base_url=settings.ZEUS_DOMAIN + "/v1",
            api_key="sk-******",
            http_client=httpx_client.get_client(),
        )

        # 调用 VQA 模型进行答案生成
        messages = [
            {"role": "system", "content": system_prompt} if system_prompt else None,
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"{image_base64}"},
                    },
                    {"type": "text", "text": question},
                ],
            },
        ]
        messages = [item for item in messages if item]
        response = openai_client.chat.completions.create(
            messages=messages,
            model=model.value if isinstance(model, VQAModelEnum) else model,
        )
        response_content = response.choices[0].message.content

        if not response_content:
            raise AssertionError(f"VQA 答案生成失败, response: {response.model_dump_json()}")

        return response_content

    def vqa(
        self,
        question: str,
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        model: Union[VQAModelEnum, str] = VQAModelEnum.CVTE_LATEST,
        system_prompt: Optional[str] = SYSTEM_PROMPT_VQA,
    ):
        """
        进行视觉问答(VQA),生成布尔类型的回答结果

        Args:
            question (str): 待回答的问题
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            model (Optional[Union[VQAModelEnum, str]]): VQA模型,可以是VQAModelEnum枚举或字符串,默认为None时自动选择
            system_prompt (Optional[str]): 系统提示词,用于引导模型生成特定类型的回答。注意：对CVTE模型无效。

        Returns:
            bool: True表示问题成立,False表示问题不成立

        Raises:
            AssertionError: 当VQA回答生成失败时抛出
        """
        if not image:
            image = self.device.screenshot()
        image_base64 = ImageUtil.convert_to_base64(image)

        model_processor = VqaModelFactory.create(model, system_prompt)
        messages = model_processor.build_messages(question, image_base64, system_prompt)
        if isinstance(model_processor, CvteModel):
            response, answer = model_processor.query_with_retry(messages)
        else:
            response = model_processor.query(messages)
            answer = model_processor.parse_response(response)

        try:
            return self._verify_answer(answer, response.model_dump_json())
        except AssertionError as e:
            if not isinstance(model_processor, CvteModel):
                raise
            log.warning(f"CVTE模型验证失败: {str(e)}")
            raise

    def vqa_batch(
        self,
        questions: List[str],
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        model: Union[VQAModelEnum, str] = VQAModelEnum.CVTE_LATEST,
        system_prompt: Optional[str] = None,
    ) -> List[bool]:
        """
        批量进行视觉问答(VQA)，仅支持CVTE模型

        Args:
            questions (List[str]): 待回答的问题列表
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            model (Union[VQAModelEnum, str]): 仅支持CVTE_LATEST模型
            system_prompt (Optional[str]): 系统提示词(可选)

        Returns:
            List[bool]: 每个问题的布尔结果列表

        Raises:
            ValueError: 当使用非CVTE模型时抛出
        """
        if not VQAModelEnum.CVTE_LATEST.equals(model):
            raise ValueError("vqa_batch目前仅支持CVTE_LATEST模型")

        if not image:
            image = self.device.screenshot()
        image_base64 = ImageUtil.convert_to_base64(image)

        # 构建批量请求数据
        message_group = []
        for question in questions:
            message_group.append([{"role": "system", "content": system_prompt or "请回答是的或者不是。"}, {"role": "user", "content": [{"type": "text", "text": question}]}])

        data = {
            "message_group": message_group,
            "messages": [{"role": "system", "content": "messages占位用，在AI API聚合平台会自动移除"}],
            "image_url": image_base64,
            "model": VQAModelEnum.CVTE_LATEST.value,
            "max_tokens": 10,
            "temperature": 0.2,
            "seed": 2841,
        }

        # 使用httpx client发送请求
        client = httpx_client.get_client()
        response = client.post(
            settings.ZEUS_DOMAIN + API_VQA,
            json=data,
        )
        response.raise_for_status()

        # 解析响应
        try:
            result = response.json()
            answers = [choice["text"] for choice in result["choices"]]
            return [self._verify_answer(answer.strip(), response.text) for answer in answers]
        except Exception as e:
            raise AssertionError(f"批量VQA处理失败: {str(e)}") from e

    def vqa_object(
        self,
        question: str,
        response_format: Type[BaseModelT],
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        model: Optional[Union[VQAModelEnum, str]] = None,
        system_prompt: str = SYSTEM_PROMPT_VQA_OBJECT,
    ):
        """
        进行视觉问答(VQA),生成结构化的响应对象

        Args:
            question (str): 待回答的问题
            response_format (Type[BaseModelT]): 期望的响应格式类型,必须继承自BaseModelT
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            model (Optional[Union[VQAModelEnum, str]]): VQA模型,可以是VQAModelEnum枚举或字符串,默认为None时自动选择
            system_prompt (str): 系统提示词,默认使用VQA_OBJECT系统提示

        Returns:
            BaseModelT: 按照指定格式解析的结构化响应对象

        Raises:
            AssertionError: 当VQA回答生成失败或响应解析失败时抛出
            ValidationError: 当响应内容无法按照指定格式解析时抛出
        """
        if not image:
            image = self.device.screenshot()
        image_base64 = ImageUtil.convert_to_base64(image)

        openai_client = OpenAI(
            base_url=settings.ZEUS_DOMAIN + "/v1",
            api_key="sk-******",
            http_client=httpx_client.get_client(),
        )

        # 若未传入模型，则自动选择模型
        if not model:
            model = self._auto_select_model(question)

        # 拦截 CVTE 自研模型
        if VQAModelEnum.CVTE_LATEST.equals(model):
            raise AssertionError("CVTE 自研模型不支持 vqa_object")

        # 调用 VQA 模型进行答案生成
        json_schema = json.dumps(response_format.model_json_schema(), ensure_ascii=False)
        messages = [
            {"role": "system", "content": system_prompt} if system_prompt else None,
            {
                "role": "system",
                "content": SYSTEM_PROMPT_JSON_RESPONSE.format(json_schema=json_schema),
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"{image_base64}"},
                    },
                    {"type": "text", "text": question},
                ],
            },
        ]
        messages = [item for item in messages if item]
        response = openai_client.chat.completions.create(
            messages=messages,
            model=model.value if isinstance(model, VQAModelEnum) else model,
            # https://platform.openai.com/docs/api-reference/chat/create#chat-create-temperature
            temperature=0.2,
        )
        response_content = response.choices[0].message.content

        if not response_content:
            raise AssertionError(f"VQA 答案生成失败, response: {response.model_dump_json()}")

        # 特殊情况：模型有可能返回 markdown 代码块内容，只需要提取代码块中的内容即可
        response_content = extract_markdown_code_block_content(response_content).strip()

        try:
            safe_parsed = response_format.model_validate_json(response_content)
        except ValidationError as e:
            raise AssertionError(f"VQA 答案生成失败, response: {response.model_dump_json()}, error: {e.json()}") from e

        return safe_parsed


if __name__ == "__main__":
    from pydantic import BaseModel

    class DeviceImpl(DeviceInterface):
        def screenshot(self) -> Union[Image, bytes, str]:
            image_url = "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png"
            return image_url

    # 定义一个示例响应格式
    class TestResponse(BaseModel):
        has_button: bool
        button_text: str

    _device = DeviceImpl()
    vqa_service = VQAServiceImpl(_device)

    # result = vqa_service.vqa("这是百度画面吗？")
    # print(f"🚀 VQA Result: {result}")

    result = vqa_service.vqa_batch(["这是百度画面吗？", "这是百度画面吗？"])
    print(f"🚀 VQA Result: {result}")

    # # 测试 vqa_object
    # result = vqa_service.vqa_object(
    #     question="电视界面是否显示了返回按钮，如果有返回按钮显示的文字是什么？",
    #     response_format=TestResponse,
    #     system_prompt=SYSTEM_PROMPT_VQA_OBJECT,
    # )
    # print(f"🚀 VQA Object Result: {result}")

    # result = vqa_service.vqa("这是百度画面吗？")
    # print(f"🚀 VQA Result: {result}")
