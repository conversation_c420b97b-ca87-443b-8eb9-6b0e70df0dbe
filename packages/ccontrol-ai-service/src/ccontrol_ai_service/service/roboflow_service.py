from functools import lru_cache
from typing import List, Optional, Union
from pydantic import BaseModel, Field
from ccontrol_common.config.conf import settings
from ccontrol_common.core.http_client import DEFAULT_LOG_LEN, do_get, do_post
from ccontrol_common.core.errors import ServerError

# API 常量定义
API_ROBOFLOW_CLASSES = "/api/v1/roboflow/classes"
API_ROBOFLOW_IMAGES = "/api/v1/roboflow/images"
API_ROBOFLOW_IMAGES_SEARCH = "/api/v1/roboflow/images/search"


# 模型定义
class BoundingBox(BaseModel):
    """边界框模型"""

    label: str
    x: float
    y: float
    width: float
    height: float


class ImageUrls(BaseModel):
    """图片URL模型"""

    original: str
    thumb: str
    annotation: str


class Box(BaseModel):
    """图像边界框坐标信息"""

    x: float
    y: float
    width: float
    height: float


class RoboflowImage(BaseModel):
    """Roboflow 图像数据模型"""

    id: str
    className: str
    imageUrl: str
    base64: str
    box: Box


class ImageAnnotation(BaseModel):
    """图片标注模型"""

    key: str
    width: float
    height: float
    boxes: List[BoundingBox]


class ImageDetail(BaseModel):
    """图片详情模型"""

    id: str
    name: str
    annotation: Optional[ImageAnnotation]
    labels: List[str]
    split: Optional[str]
    tags: List[str]
    created: float
    urls: ImageUrls
    embedding: Optional[List[float]]


class SearchImagesInput(BaseModel):
    """搜索图片输入参数模型"""

    likeImage: Optional[str] = None
    prompt: Optional[str] = None
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=20, ge=1, le=250)
    tag: Optional[str] = None
    className: Optional[str] = None
    inDataset: Optional[bool] = None
    batch: Optional[bool] = None
    batchId: Optional[str] = None
    fields: Optional[List[str]] = None


class ImageData(BaseModel):
    """图片基础数据模型"""

    id: str
    labels: List[str] = Field(default_factory=list)
    split: Optional[str]
    owner: Optional[str]
    annotations: Optional[dict]


class SearchImagesResult(BaseModel):
    """搜索图片结果模型"""

    results: List[ImageData]
    total: int
    offset: int = 0


class RoboflowServiceImpl:
    """Roboflow 服务实现"""

    @lru_cache(maxsize=1)
    def get_all_classes(self) -> List[str]:
        """
        获取所有可用的类别

        Returns:
            List[str]: 类别列表，结果会被缓存1小时

        Raises:
            ServerError: 服务调用失败
        """
        resp = do_get(f"{settings.ZEUS_DOMAIN}{API_ROBOFLOW_CLASSES}", log_resp_len=DEFAULT_LOG_LEN, timeout=60)

        if not resp.is_success:
            raise ServerError("获取类别列表失败")

        data = resp.json().get("data", [])
        if not isinstance(data, list):
            raise ServerError("返回数据格式错误")

        return data

    def get_images(self, classes: Union[str, List[str], None] = None, limit: int = 20) -> List[RoboflowImage]:
        """
        获取图片列表，支持按类别筛选

        Args:
            classes: 类别列表，多个类别用逗号分隔
            limit: 返回结果数量限制，默认20，最大250

        Returns:
            List[RoboflowImage]: 图片列表，每个元素包含图片ID、类名、图片URL、base64数据和边界框信息

        Raises:
            ServerError: 服务调用失败
        """
        params = {}
        if classes:
            params["classes"] = classes
        if limit:
            params["limit"] = min(limit, 250)

        resp = do_get(f"{settings.ZEUS_DOMAIN}{API_ROBOFLOW_IMAGES}", params=params, log_resp_len=DEFAULT_LOG_LEN, timeout=60)

        if not resp.is_success:
            raise ServerError("获取图片列表失败")

        # 处理返回数据
        json_data = resp.json()
        if not json_data or "data" not in json_data:
            raise ServerError("返回数据格式错误")

        data = json_data.get("data", {})
        if isinstance(data, list):
            return [RoboflowImage.model_validate(item) for item in data]

        results = data.get("results", [])
        if not isinstance(results, list):
            raise ServerError("返回数据格式错误")

        return [RoboflowImage.model_validate(item) for item in results]

    def get_image_detail(self, image_id: str) -> ImageDetail:
        """
        获取单个图片详情

        Args:
            image_id: 图片ID

        Returns:
            ImageDetail: 图片详情对象

        Raises:
            ServerError: 服务调用失败
        """
        if not image_id:
            raise ServerError("图片ID不能为空")

        resp = do_get(f"{settings.ZEUS_DOMAIN}{API_ROBOFLOW_IMAGES}/{image_id}")
        json_data = resp.json()

        if not resp.is_success or (json_data and json_data.get("code") == 500):
            if resp.status_code == 404 or (json_data and "404" in str(json_data.get("msg", ""))):
                raise ServerError(f"图片 {image_id} 不存在")
            raise ServerError("获取图片详情失败")

        if not json_data or "data" not in json_data:
            raise ServerError("返回数据格式错误")

        data = json_data.get("data", {})
        if not data:
            raise ServerError("返回数据格式错误")

        image = data.get("image")
        if not image:
            raise ServerError("返回数据格式错误")

        return ImageDetail.model_validate(image)

    def search_images(self, search_input: SearchImagesInput) -> SearchImagesResult:
        """
        搜索图片

        Args:
            search_input: 搜索参数对象

        Returns:
            SearchImagesResult: 搜索结果对象

        Raises:
            ServerError: 服务调用失败
        """
        resp = do_post(
            f"{settings.ZEUS_DOMAIN}{API_ROBOFLOW_IMAGES_SEARCH}",
            json_data=search_input.model_dump(exclude_none=True),
            log_resp_len=DEFAULT_LOG_LEN,
        )

        if not resp.is_success:
            raise ServerError("搜索图片失败")

        data = resp.json().get("data", {})
        if not isinstance(data, dict):
            raise ServerError("返回数据格式错误")

        return SearchImagesResult.model_validate(data)


if __name__ == "__main__":
    service = RoboflowServiceImpl()
    # print(service.get_all_classes())
    print(service.get_images(classes=["j", "k"], limit=5))
    # print(service.get_image_detail("1"))
    # print(service.search_images(SearchImagesInput(limit=5)))
