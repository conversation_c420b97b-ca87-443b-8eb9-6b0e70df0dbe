import hashlib
from typing import Dict, List, Tuple
from pydantic import BaseModel

from ccontrol_common.core.log import log
from ccontrol_common.config.conf import settings
from ccontrol_common.core.http_client import DEFAULT_LOG_LEN, do_post
from ccontrol_common.core.errors import ServerError

# API 常量定义
API_IMAGE_COMPARE = "/api/v1/image-compare"
API_IMAGE_COMPARE_BATCH = "/api/v1/image-compare/batch"


class ImageCompareRequest(BaseModel):
    """图片比对请求"""

    imageUrl1: str
    imageUrl2: str


class ImageCompareResult(BaseModel):
    """图片比对结果"""

    confidence: float
    is_similar: bool

    @classmethod
    def create(cls, confidence: float, threshold: float = 0.85) -> "ImageCompareResult":
        """便捷创建结果对象的工厂方法"""
        return cls(confidence=confidence, is_similar=confidence >= threshold)


class BatchImageCompareTask(BaseModel):
    """批量图片比对任务"""

    image_1: str
    image_2: str


class ImageMapItem(BaseModel):
    """图片映射项"""

    image_url: str


class BatchImageCompareRequest(BaseModel):
    """批量图片比对请求"""

    tasks: List[BatchImageCompareTask]
    image_map: Dict[str, ImageMapItem]


class BatchImageCompareResultItem(BaseModel):
    """批量图片比对结果项"""

    image_1: str
    image_2: str
    confidence: float


class BatchImageCompareResponse(BaseModel):
    """批量图片比对响应"""

    code: int
    msg: str
    data: List[BatchImageCompareResultItem]


class ImageCompareServiceImpl:
    """图片比对服务实现"""

    def compare_images(self, image_url_1: str, image_url_2: str, threshold: float = 0.85) -> ImageCompareResult:
        """
        比对两张图片的相似度

        Args:
            image_url_1: 第一张图片的URL或base64
            image_url_2: 第二张图片的URL或base64
            threshold: 相似度阈值,默认为0.85

        Returns:
            ImageCompareResult: 比对结果，包含相似度置信值和是否相似的判断

        Raises:
            ServerError: 服务调用失败时抛出
        """
        if not image_url_1 or not image_url_2:
            raise ServerError("图片数据不能为空")

        request = ImageCompareRequest(imageUrl1=image_url_1, imageUrl2=image_url_2)

        resp = do_post(
            f"{settings.ZEUS_DOMAIN}{API_IMAGE_COMPARE}",
            json_data=request.model_dump(),
            log_req_len=0,  # 禁用请求日志
            log_resp_len=0,  # 禁用响应日志
        )

        if not resp.is_success:
            raise ServerError("图片比对失败")

        json_data = resp.json()
        if not json_data or "data" not in json_data:
            raise ServerError("返回数据格式错误")

        data = json_data.get("data", {})
        confidence = data.get("confidence", 0.0)
        return ImageCompareResult.create(confidence, threshold)

    def _generate_image_id(self, image_content: str) -> str:
        """为图片内容生成唯一ID"""
        return f"img_{hashlib.md5(image_content.encode('utf-8')).hexdigest()[:8]}"

    def compare_images_batch(self, image_pairs: List[Tuple[str, str]], threshold: float = 0.85) -> List[ImageCompareResult]:
        """批量比对图片相似度，优化图片传输（去重）"""
        if not image_pairs:
            return []

        unique_images = {}
        image_map = {}
        tasks = []
        image_pair_to_task = {}

        for i, (image_1, image_2) in enumerate(image_pairs):
            if not image_1 or not image_2:
                raise ServerError(f"图片数据不能为空: pair {i}")

            if image_1 not in unique_images:
                image_1_id = self._generate_image_id(image_1)
                unique_images[image_1] = image_1_id
                image_map[image_1_id] = ImageMapItem(image_url=image_1)
            else:
                image_1_id = unique_images[image_1]

            if image_2 not in unique_images:
                image_2_id = self._generate_image_id(image_2)
                unique_images[image_2] = image_2_id
                image_map[image_2_id] = ImageMapItem(image_url=image_2)
            else:
                image_2_id = unique_images[image_2]

            task = BatchImageCompareTask(image_1=image_1_id, image_2=image_2_id)
            tasks.append(task)
            image_pair_to_task[(image_1, image_2)] = (image_1_id, image_2_id)

        request = BatchImageCompareRequest(tasks=tasks, image_map=image_map)

        resp = do_post(
            f"{settings.ZEUS_DOMAIN}{API_IMAGE_COMPARE_BATCH}",
            json_data=request.model_dump(),
            log_req_len=DEFAULT_LOG_LEN,
            log_resp_len=DEFAULT_LOG_LEN,
        )

        if not resp.is_success:
            raise ServerError(f"批量图片比对失败: {resp.text}")

        try:
            response = BatchImageCompareResponse.model_validate(resp.json())
        except Exception as e:
            log.error(f"解析批量图片比对响应失败: {e}")
            raise ServerError(f"返回数据格式错误: {e}") from e

        if response.code != 200:
            raise ServerError(f"批量图片比对失败: {response.msg}")

        result_map = {}
        for item in response.data:
            result_map[(item.image_1, item.image_2)] = item.confidence

        results = []
        for image_1, image_2 in image_pairs:
            task_key = image_pair_to_task[(image_1, image_2)]
            confidence = result_map.get(task_key, 0.0)
            results.append(ImageCompareResult.create(confidence, threshold))

        return results


if __name__ == "__main__":
    service = ImageCompareServiceImpl()
    result = service.compare_images("http://dummyimage.com/400x400", "http://dummyimage.com/800x800")
    print(f"result: {result}")
    print(f"图片相似度：{result.confidence}")
    print(f"是否相似：{result.is_similar}")
