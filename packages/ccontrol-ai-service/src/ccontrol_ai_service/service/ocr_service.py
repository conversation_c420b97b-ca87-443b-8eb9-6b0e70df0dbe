import re
from typing import Generic, List, Optional, TypeVar, Union

from PIL.Image import Image

from ccontrol_ai_service.common.constans import API_OCR_PREDICT, OCR_FLAG_CASE_SENSITIVE, OCR_FLAG_CONTAINS_MATCH, OCR_FLAG_STRIP_SPACES
from ccontrol_ai_service.interface.device import DeviceInterface
from ccontrol_common.config.conf import settings
from ccontrol_common.models.ocr_model import OCRPredictModel
from ccontrol_common.core.http_client import (
    DEFAULT_LOG_LEN,
    do_post,
)
from ccontrol_common.utils.image import ImageUtil

T_Device = TypeVar("T_Device", bound=DeviceInterface)


class OCRServiceImpl(Generic[T_Device]):
    """OCR服务实现"""

    device: T_Device

    def __init__(self, device: T_Device):
        self.device = device

    def _fetch_ocr(self, *, image: Optional[Union[Image, str, bytes]] = None) -> List[OCRPredictModel]:
        """
        执行OCR文字识别并获取预测结果

        Args:
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图

        Returns:
            List[OCRPredictModel]: OCR识别结果列表

        Raises:
            AssertionError: OCR识别失败时抛出,包含以下情况:
                - API响应状态码非200
                - 业务响应code非200
                - 响应数据格式异常
            ValueError: 当输入图片格式无效或处理失败时抛出
        """
        try:
            if not image:
                image = self.device.screenshot()
            processed_image = ImageUtil.convert_to_base64(image)

            # 构造 OCR 请求数据
            ocr_request_data = {"imageUrl": processed_image}

            # 调用 OCR API （增加重试机制和超时设置为最佳实践，示例中未添加）
            resp = do_post(
                settings.ZEUS_DOMAIN + API_OCR_PREDICT,
                json_data=ocr_request_data,
                log_req_len=DEFAULT_LOG_LEN,
                log_resp_len=DEFAULT_LOG_LEN,
            )

            # 检查响应状态
            if not resp.is_success:
                raise AssertionError("OCR 文字识别失败，状态码非 200")

            # 解析响应数据
            resp_json = resp.json()
            code, msg, data = (
                resp_json.get("code", None),  # 提供默认值避免 None 后续操作异常
                resp_json.get("msg", ""),
                resp_json.get("data", []),
            )

            # 校验响应代码
            if code != 200:
                raise AssertionError(f"OCR 文字识别失败，代码: {code}, 消息: {msg}")

            # 数据类型检查
            if not isinstance(data, list):
                raise AssertionError(f"OCR 响应数据异常，期望 list но获得 {type(data)}")

            # 校验并返回 OCR 预测模型列表
            return [OCRPredictModel.model_validate(item) for item in data if item]
        except Exception as e:
            raise ValueError(f"OCR 识别失败，错误: {e}") from e

    def _apply_filters(
        self,
        predict_list: List[OCRPredictModel],
        *,
        filter_text: Optional[str] = None,
        filter_pattern: Optional[str] = None,
        filter_score: Optional[float] = None,
        flag_case_sensitive: bool = OCR_FLAG_CASE_SENSITIVE,
        flag_strip_spaces: bool = OCR_FLAG_STRIP_SPACES,
        flag_contains_match: bool = OCR_FLAG_CONTAINS_MATCH,
    ) -> List[OCRPredictModel]:
        """
        应用过滤器对OCR预测结果进行筛选
        默认不区分大小写、去除所有空格、精确匹配（非包含匹配）

        Args:
            predict_list (List[OCRPredictModel]): OCR预测模型列表
            filter_text (Optional[str]): 要匹配的文本内容,None表示不进行文本匹配
            filter_pattern (Optional[str]): 要匹配的正则表达式,None表示不进行正则匹配
            filter_score (Optional[float]): OCR置信度阈值,小于该值的结果会被过滤
            flag_case_sensitive (bool): 是否区分大小写,默认False
            flag_strip_spaces (bool): 是否去除文本中的所有空白符,默认True
            flag_contains_match (bool): 是否使用包含匹配而不是精确匹配,默认False
        Returns:
            List[OCRPredictModel]: 符合过滤条件的OCR预测结果列表
        """
        results = predict_list.copy()

        if filter_text is not None:
            results = self._apply_text_filter(
                results,
                filter_text,
                flag_case_sensitive=flag_case_sensitive,
                flag_strip_spaces=flag_strip_spaces,
                flag_contains_match=flag_contains_match,
            )
        if filter_pattern:
            results = self._apply_pattern_filter(results, filter_pattern, case_sensitive=flag_case_sensitive)

        if filter_score is not None:
            results = self._apply_score_filter(results, filter_score)

        return results

    def _apply_text_filter(
        self,
        items: List[OCRPredictModel],
        filter_text: str,
        *,
        flag_case_sensitive: bool,
        flag_strip_spaces: bool,
        flag_contains_match: bool,
    ) -> List[OCRPredictModel]:
        """应用文本匹配过滤
        Args:
            items (List[OCRPredictModel]): 需要过滤的OCR预测结果列表
            filter_text (str): 过滤文本
            flag_case_sensitive (bool): 是否区分大小写
            flag_strip_spaces (bool): 是否去除空白符
            flag_contains_match (bool): 是否使用包含匹配

        Returns:
            List[OCRPredictModel]: 过滤后的OCR预测结果列表
        """
        normalized_filter = self._normalize_text(
            filter_text,
            case_sensitive=flag_case_sensitive,
            strip_spaces=flag_strip_spaces,
        )

        def text_matches(item: OCRPredictModel) -> bool:
            normalized_item = self._normalize_text(
                item.text,
                case_sensitive=flag_case_sensitive,
                strip_spaces=flag_strip_spaces,
            )
            return normalized_filter in normalized_item if flag_contains_match else normalized_filter == normalized_item

        return [item for item in items if text_matches(item)]

    def _apply_pattern_filter(self, items: List[OCRPredictModel], pattern: str, *, case_sensitive: bool) -> List[OCRPredictModel]:
        """应用正则表达式匹配过滤

        Args:
            items (List[OCRPredictModel]): 需要过滤的OCR预测结果列表
            pattern (str): 正则表达式模式
            case_sensitive (bool): 是否区分大小写

        Returns:
            List[OCRPredictModel]: 过滤后的OCR预测结果列表
        """
        flags = 0 if case_sensitive else re.IGNORECASE
        compiled_pattern = re.compile(pattern, flags)
        return [item for item in items if compiled_pattern.search(item.text)]

    def _apply_score_filter(self, items: List[OCRPredictModel], min_score: float) -> List[OCRPredictModel]:
        """应用分数阈值过滤"""
        return [item for item in items if item.recognition_score >= min_score]

    def _normalize_text(self, text: str, *, case_sensitive: bool, strip_spaces: bool) -> str:
        """标准化处理文本
        Args:
            text (str): 要处理的文本
            case_sensitive (bool): 是否区分大小写
            strip_spaces (bool): 是否去除空白符

        Returns:
            str: 标准化后的文本
        """
        result = text
        if strip_spaces:
            result = "".join(result.split())
        if not case_sensitive:
            result = result.lower()
        return result

    def ocr(
        self,
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        filter_text: Optional[str] = None,
        filter_pattern: Optional[str] = None,
        filter_score: Optional[float] = None,
        flag_case_sensitive: bool = OCR_FLAG_CASE_SENSITIVE,
        flag_strip_spaces: bool = OCR_FLAG_STRIP_SPACES,
        flag_contains_match: bool = OCR_FLAG_CONTAINS_MATCH,
    ) -> List[OCRPredictModel]:
        """
        执行OCR文字识别并根据过滤条件筛选结果
        默认不区分大小写、去除所有空格、精确匹配（非包含匹配）

        Args:
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            filter_text (Optional[str]): 要匹配的文本内容
            filter_pattern (Optional[str]): 要匹配的正则表达式
            filter_score (Optional[float]): OCR置信度阈值,小于该值的结果会被过滤
            flag_case_sensitive (bool): 是否区分大小写,默认False
            flag_strip_spaces (bool): 是否去除所有空格,默认True
            flag_contains_match (bool): 是否使用包含匹配而不是精确匹配,默认False

        Returns:
            List[OCRPredictModel]: 符合过滤条件的OCR预测结果列表
        """
        # 获取初始 OCR 预测列表
        predict_list = self._fetch_ocr(image=image)

        # 应用过滤器
        filtered_predict_list = self._apply_filters(
            predict_list,
            filter_text=filter_text,
            filter_pattern=filter_pattern,
            filter_score=filter_score,
            flag_case_sensitive=flag_case_sensitive,
            flag_strip_spaces=flag_strip_spaces,
            flag_contains_match=flag_contains_match,
        )

        return filtered_predict_list

    def ocr_find_first_by_text(
        self,
        filter_text: str,
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        filter_score: Optional[float] = None,
        flag_case_sensitive: bool = OCR_FLAG_CASE_SENSITIVE,
        flag_strip_spaces: bool = OCR_FLAG_STRIP_SPACES,
        flag_contains_match: bool = OCR_FLAG_CONTAINS_MATCH,
    ) -> Optional[OCRPredictModel]:
        """
        查找图片中第一个匹配指定文本的OCR结果
        默认不区分大小写、去除所有空格、精确匹配（非包含匹配）

        Args:
            filter_text (str): 要匹配的文本内容
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            filter_score (Optional[float]): OCR置信度阈值,小于该值的结果会被过滤
            flag_case_sensitive (bool): 是否区分大小写,默认False
            flag_strip_spaces (bool): 是否去除所有空格,默认True
            flag_contains_match (bool): 是否使用包含匹配而不是精确匹配,默认False

        Returns:
            Optional[OCRPredictModel]: 首个匹配的OCR结果,无匹配时返回None
        """
        matched_predict_list = self.ocr(
            image=image,
            filter_text=filter_text,
            filter_score=filter_score,
            flag_case_sensitive=flag_case_sensitive,
            flag_strip_spaces=flag_strip_spaces,
            flag_contains_match=flag_contains_match,
        )
        return matched_predict_list[0] if matched_predict_list else None

    def ocr_find_first_center_point_by_text(
        self,
        filter_text: str,
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        flag_case_sensitive: bool = OCR_FLAG_CASE_SENSITIVE,
        flag_strip_spaces: bool = OCR_FLAG_STRIP_SPACES,
        flag_contains_match: bool = OCR_FLAG_CONTAINS_MATCH,
    ):
        """
        获取图片中第一个匹配指定文本的OCR结果的中心点坐标
        默认不区分大小写、去除所有空格、精确匹配（非包含匹配）

        Args:
            filter_text (str): 要匹配的文本内容
            image (Optional[Union[Image, str, bytes]]): 指定图像，为None时默认使用设备截图
            flag_case_sensitive (bool): 是否区分大小写,默认False
            flag_strip_spaces (bool): 是否去除所有空格,默认True
            flag_contains_match (bool): 是否使用包含匹配而不是精确匹配,默认False

        Returns:
            Optional[Point]: OCR结果的中心点坐标,无匹配时返回None
        """
        first_match = self.ocr_find_first_by_text(
            filter_text,
            image=image,
            flag_case_sensitive=flag_case_sensitive,
            flag_strip_spaces=flag_strip_spaces,
            flag_contains_match=flag_contains_match,
        )
        if not first_match:
            return None
        return first_match.center_point

    def ocr_find_first_by_regex(
        self,
        filter_pattern: str,
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        filter_score: Optional[float] = None,
        flag_case_sensitive: bool = OCR_FLAG_CASE_SENSITIVE,
    ) -> Optional[OCRPredictModel]:
        """
        查找图片中第一个匹配正则表达式的OCR结果
        默认不区分大小写

        Args:
            filter_pattern (str): 要匹配的正则表达式
            filter_score (Optional[float]): OCR置信度阈值,小于该值的结果会被过滤
            flag_case_sensitive (bool): 是否区分大小写,默认False,不区分大小写

        Returns:
            Optional[OCRPredictModel]: 首个匹配的OCR结果,无匹配时返回None
        """
        matched_predict_list = self.ocr(
            image=image,
            filter_pattern=filter_pattern,
            filter_score=filter_score,
            flag_case_sensitive=flag_case_sensitive,
        )
        return matched_predict_list[0] if matched_predict_list else None

    def ocr_find_first_center_point_by_regex(
        self,
        filter_pattern: str,
        *,
        image: Optional[Union[Image, str, bytes]] = None,
        filter_score: Optional[float] = None,
        flag_case_sensitive: bool = OCR_FLAG_CASE_SENSITIVE,
    ):
        """
        获取图片中第一个匹配正则表达式的OCR结果的中心点坐标
        默认不区分大小写

        Args:
            filter_pattern (str): 要匹配的正则表达式
            filter_score (Optional[float]): OCR置信度阈值,小于该值的结果会被过滤
            flag_case_sensitive (bool): 是否区分大小写,默认False,不区分大小写

        Returns:
            Optional[Point]: OCR结果的中心点坐标,无匹配时返回None
        """
        first_match = self.ocr_find_first_by_regex(
            filter_pattern,
            image=image,
            filter_score=filter_score,
            flag_case_sensitive=flag_case_sensitive,
        )
        if not first_match:
            return None
        return first_match.center_point
