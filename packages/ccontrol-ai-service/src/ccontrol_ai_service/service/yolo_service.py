from typing import List, Union
from PIL.Image import Image
from pydantic import BaseModel
from ccontrol_common.config.conf import settings
from ccontrol_common.core.http_client import DEFAULT_LOG_LEN, do_post
from ccontrol_common.core.errors import ServerError
from ccontrol_common.utils.image import ImageUtil

# API 常量定义
API_YOLO_DETECT = "/api/v1/yolo/interactive-element/detect"


class BoundingBox(BaseModel):
    """YOLO 检测边界框"""

    x_min: float
    y_min: float
    x_max: float
    y_max: float

    @classmethod
    def from_list(cls, bbox: List[float]) -> "BoundingBox":
        """从列表创建边界框对象"""
        return cls(x_min=bbox[0], y_min=bbox[1], x_max=bbox[2], y_max=bbox[3])


class Detection(BaseModel):
    """YOLO 检测结果"""

    label: str
    confidence: float
    bbox: List[float]

    def get_bounding_box(self) -> BoundingBox:
        """获取标准化的边界框对象"""
        return BoundingBox.from_list(self.bbox)


class DetectionResult(BaseModel):
    """检测结果模型"""

    detections: List[Detection]


class YOLOServiceImpl:
    """YOLO 服务实现"""

    def detect_interactive_elements(self, image_url: str) -> DetectionResult:
        """
        检测图片中的交互元素

        Args:
            image_url: 图片URL或base64编码的图片数据

        Returns:
            DetectionResult: 检测结果对象，包含所有检测到的元素信息

        Raises:
            ServerError: 服务调用失败
        """
        if not image_url:
            raise ServerError("图片数据不能为空")

        resp = do_post(f"{settings.ZEUS_DOMAIN}{API_YOLO_DETECT}", json_data={"imageUrl": image_url}, log_resp_len=DEFAULT_LOG_LEN)

        if not resp.is_success:
            raise ServerError("YOLO检测失败")

        json_data = resp.json()
        if not json_data or "data" not in json_data:
            raise ServerError("返回数据格式错误")

        data = json_data.get("data", {})
        return DetectionResult.model_validate(data)

    def detect_and_crop_elements(self, image: Union[Image, str, bytes]) -> List[Image]:
        """
        检测图片中的交互元素并进行分割

        Args:
            image: PIL Image对象、图片URL或base64编码的图片数据

        Returns:
            List[Image]: 分割后的图像列表，每个图像对应一个检测到的交互元素

        Raises:
            ServerError: 服务调用失败
            TypeError: 图像格式转换失败
        """
        # 转换输入图像为PIL Image对象
        img = ImageUtil.convert_to_image(image)

        # 如果输入是URL，直接使用；否则转换为base64
        if isinstance(image, str) and (image.startswith("http://") or image.startswith("https://")):
            image_url = image
        else:
            image_url = ImageUtil.convert_to_base64(img)

        # 调用YOLO检测服务
        result = self.detect_interactive_elements(image_url)

        # 根据检测结果分割图像
        cropped_images = []
        for detection in result.detections:
            bbox = detection.get_bounding_box()
            # 裁剪图像
            crop_img = img.crop((bbox.x_min, bbox.y_min, bbox.x_max, bbox.y_max))
            cropped_images.append(crop_img)

        return cropped_images


if __name__ == "__main__":
    yolo_service = YOLOServiceImpl()
    result = yolo_service.detect_interactive_elements("https://eloutput.com/wp-content/uploads/2021/12/send-file-app.jpg")
    for detection in result.detections:
        print(f"检测到交互元素：{detection.label}，置信度：{detection.confidence}")
        bbox = detection.get_bounding_box()
        print(f"边界框坐标：({bbox.x_min}, {bbox.y_min}) - ({bbox.x_max}, {bbox.y_max})")

    cropped_images = yolo_service.detect_and_crop_elements("https://eloutput.com/wp-content/uploads/2021/12/send-file-app.jpg")
    for idx, img in enumerate(cropped_images):
        img.save(f"cropped_{idx}.jpg")
        print(f"保存分割后的图像：cropped_{idx}.jpg")
