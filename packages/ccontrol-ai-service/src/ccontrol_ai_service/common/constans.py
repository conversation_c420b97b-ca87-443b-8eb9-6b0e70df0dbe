from enum import Enum
from typing import TypeVar, Optional, Union

from pydantic import BaseModel

# APIS
API_VQA = "/v1/chat/completions"
API_GROUNDING = "/api/ai/grounding"
API_OCR_PREDICT = "/api/ai/ocr/predict"


# OCR 配置
OCR_FLAG_CASE_SENSITIVE = False
OCR_FLAG_STRIP_SPACES = True
OCR_FLAG_CONTAINS_MATCH = False

# 基础版 VQA Prompt
# VQA_SYSTEM_PROMPT = """
# Context: The user provides an image of a TV interface and questions.
# Role: You are a TV interface testing assistant.
# Instruction: Determine whether each statement posed by the user is true or false.
# Subject: TV interface and related functions.
# Preset: Respond only in JSON format without explanations: {"result": "true"} or {"result": "false"}.
# """.strip()

SYSTEM_PROMPT_JSON_RESPONSE = """
JSON schema:
{json_schema}

Require:
You MUST answer with a JSON object that matches the JSON schema above!
Do not hallucinate. Do not make up factual information.
Do not use Markdown code blocks!
Do not provide explanations!

If you don't follow the above rules you will be fired!
""".strip()


def generate_system_prompt_vqa(response_yes: str = "Yes", response_no: str = "No") -> str:
    """
    生成 VQA prompt
    Args:
        response_yes: 肯定回答的文本
        response_no: 否定回答的文本
    Returns:
        生成的 prompt 文本
    """
    return f"""
Role: You are an experienced TV software testing engineer specializing in verifying the accuracy of TV screen displays.

Task: Based on the provided screenshot of a TV screen and a related assertion, determine whether the assertion is true. Respond with only "{response_yes}" or "{response_no}" without providing any additional explanation.

Context: The user will provide a TV screen screenshot and an assertion about it. These assertions may concern the content, layout, color, text, or other aspects of the screen.

Input format:
Screenshot: [TV screenshot image]
Assertion: [A statement about the TV screen]

Output format:
[{response_yes}/{response_no}] 

Example:
<Input>
Screenshot: [Netflix home screen screenshot]
Assertion: The screen includes the Netflix app icon.
</Input>
<Output>
{response_yes}
</Output>

Remember, your response should be based solely on the provided screenshot, without any assumptions or additional information.
If your answer is anything other than "{response_yes}" or "{response_no}" you will be fired.
""".strip()


# 基础 VQA Prompt
SYSTEM_PROMPT_VQA = generate_system_prompt_vqa("Yes", "No")

# unused: CVTE 中文版 VQA Prompt
SYSTEM_PROMPT_VQA_CVTE = generate_system_prompt_vqa("是的", "不是")

SYSTEM_PROMPT_VQA_OBJECT = """
Context: The user provides an image of a TV interface and questions related to the interface elements, such as buttons, settings, and states. The tested TVs primarily run on Android, but may also include common TV operating systems like WebOS and Linux.
Role: You are a TV interface testing assistant.
Instruction: Answer user questions based on provided image. If a term is unclear, assume it refers to common TV interface elements.
Subject: TV interface and related functions.
""".strip()


def generate_grounding_prompt(target: str) -> str:
    return f'help me to locate "{target.lower()}" in and give me its bounding boxes, please.'


class VQAModelEnum(Enum):
    """VQA模型枚举"""

    CVTE_LATEST = "CVTE_LATEST"
    OPENAI_LATEST = "OPENAI_LATEST"
    GEMINI_LATEST = "GEMINI_LATEST"
    QWEN_LATEST = "QWEN_LATEST"

    # TODO: 弃用指定模型名称，只用模型渠道，由服务端决定最佳模型
    OPENAI_GPT_4_O = "OPENAI_GPT_4_O"
    GOOGLE_GEMINI_1_5_PRO = "GOOGLE_GEMINI_1_5_PRO"
    ALI_QWEN_VL_MAX = "ALI_QWEN_VL_MAX"

    @classmethod
    def normalize(cls, model: Optional[Union["VQAModelEnum", str]]) -> Optional[Union["VQAModelEnum", str]]:
        """
        标准化模型参数
        Args:
            model: VQA模型,可以是VQAModelEnum枚举或字符串
        Returns:
            标准化后的模型参数
        """
        if isinstance(model, str) and hasattr(cls, model):
            return getattr(cls, model)
        return model

    def equals(self, other: Optional[Union["VQAModelEnum", str]]) -> bool:
        """
        比较模型是否相等
        Args:
            other: 待比较的模型,可以是VQAModelEnum枚举或字符串
        Returns:
            bool: 是否相等
        """
        if other is None:
            return False
        if isinstance(other, VQAModelEnum):
            return self == other
        return other == self.name or other == self.value


class BoxLabelColorEnum(Enum):
    """标注颜色枚举"""

    RED = "red"
    GREEN = "green"
    BLUE = "blue"
    YELLOW = "yellow"
    ORANGE = "orange"
    PURPLE = "purple"


BaseModelT = TypeVar("BaseModelT", bound=BaseModel)
