from typing import Optional, Any
from ccontrol_common.core.errors import BaseError, NotFoundError, ServerError


class AIServiceError(BaseError):
    """AI服务错误"""

    def __init__(self, message: str, data: Optional[Any] = None):
        super().__init__(code=500, message=message, data=data)


class AIModelError(BaseError):
    """AI模型错误"""

    def __init__(self, message: str, data: Optional[Any] = None):
        super().__init__(code=503, message=message, data=data)


class AIToolError(BaseError):
    """AI工具错误"""

    def __init__(self, message: str, data: Optional[Any] = None):
        super().__init__(code=500, message=message, data=data)


class AITimeoutError(BaseError):
    """AI超时错误"""

    def __init__(self, message: str, data: Optional[Any] = None):
        super().__init__(code=504, message=message, data=data)


class ElementNotFoundError(NotFoundError):
    """元素未找到错误"""

    def __init__(self, message: str, data: Optional[Any] = None):
        super().__init__(message=message, data=data)


class ElementOperationError(ServerError):
    """元素操作错误"""

    def __init__(self, message: str, data: Optional[Any] = None):
        super().__init__(message=message, data=data)
