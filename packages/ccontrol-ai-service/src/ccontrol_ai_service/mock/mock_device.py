import os
from enum import Enum
from PIL.Image import Image, open as open_image

from ccontrol_ai_service.interface.device import InteractiveDeviceInterface


class MockScene(str, Enum):
    """测试场景枚举"""

    DEFAULT = "image.png"  # 默认场景（浏览器场景）
    SOUND = "sound.png"  # 音频设置场景


class MockDeviceImpl(InteractiveDeviceInterface):
    """模拟设备实现

    提供场景切换能力，用于不同场景的测试
    """

    def __init__(self):
        self._current_scene: MockScene = MockScene.DEFAULT
        self._assets_dir = os.path.join(os.path.dirname(__file__), "assets")

    @property
    def current_scene(self) -> MockScene:
        """获取当前场景"""
        return self._current_scene

    def set_scene(self, scene: MockScene) -> None:
        """切换测试场景

        Args:
            scene: 目标场景
        """
        self._current_scene = scene

    def screenshot(self) -> Image:
        """获取当前场景的截图

        Returns:
            Image: PIL图像对象

        Raises:
            RuntimeError: 当图片文件不存在或无法打开时抛出
        """
        image_path = os.path.join(self._assets_dir, self._current_scene.value)
        try:
            return open_image(image_path)
        except Exception as e:
            raise RuntimeError(f"Failed to load mock image: {image_path}") from e

    def click(self, x: int, y: int) -> None:
        """模拟点击操作

        Args:
            x: 横坐标
            y: 纵坐标
        """
        pass  # 在测试中我们不需要实际实现点击功能


# 全局单例实例
mock_device = MockDeviceImpl()

if __name__ == "__main__":
    # 测试不同场景
    print(f"Default scene: {mock_device.screenshot()}")
    mock_device.set_scene(MockScene.SOUND)
    print(f"Sound scene: {mock_device.screenshot()}")
