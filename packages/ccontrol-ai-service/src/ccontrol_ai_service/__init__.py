__version__ = "0.3.28"
from ccontrol_common.models.ocr_model import OCRPredictModel

from .service import AIServiceImpl
from .common.constans import (
    OCR_FLAG_CASE_SENSITIVE,
    OCR_FLAG_CONTAINS_MATCH,
    OCR_FLAG_STRIP_SPACES,
    SYSTEM_PROMPT_JSON_RESPONSE,
    SYSTEM_PROMPT_VQA,
    SYSTEM_PROMPT_VQA_OBJECT,
    BaseModelT,
    BoxLabelColorEnum,
    VQAModelEnum,
)
from .interface.device import DeviceInterface, InteractiveDeviceInterface

__all__ = [
    "AIServiceImpl",
    "SYSTEM_PROMPT_VQA",
    "SYSTEM_PROMPT_VQA_OBJECT",
    "SYSTEM_PROMPT_JSON_RESPONSE",
    "OCR_FLAG_CASE_SENSITIVE",
    "OCR_FLAG_CONTAINS_MATCH",
    "OCR_FLAG_STRIP_SPACES",
    "BaseModelT",
    "BoxLabelColorEnum",
    "VQAM<PERSON>lEnum",
    "OCRPredictModel",
    # Protocol
    "DeviceInterface",
    "InteractiveDeviceInterface",
]
