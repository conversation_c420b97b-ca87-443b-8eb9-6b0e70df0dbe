from typing import Protocol, Union
from PIL.Image import Image


class DeviceInterface(Protocol):
    """设备交互接口"""

    def screenshot(self) -> Union[Image, str, bytes]:
        """获取设备截图

        Returns:
            Union[Image, str, bytes]: 不同实现类可能返回不同类型：
            - PIL.Image: 用于直接图像处理
            - str: URL或base64字符串
            - bytes: 图片二进制数据

        Raises:
            ScreenshotError: 截图失败时抛出
        """
        raise NotImplementedError()


class InteractiveDeviceInterface(DeviceInterface, Protocol):
    """可交互设备接口"""

    def click(self, x: int, y: int) -> None:
        """点击指定坐标

        Args:
            x: 横坐标
            y: 纵坐标

        Raises:
            ClickError: 点击操作失败时抛出
        """
        raise NotImplementedError()


if __name__ == "__main__":
    from PIL.Image import open as open_image

    class DeviceImpl(DeviceInterface):
        # pass
        def screenshot(self) -> Image:
            image_url = "/Users/<USER>/Downloads/utools_1730812551021.png"
            return open_image(image_url)

    device = DeviceImpl()
    print(device.screenshot())
