from typing import Optional, Union

from ccontrol_common.core.log import log
from ccontrol_ai_service import AIServiceImpl
from ccontrol_ai_service.common.constans import VQAModelEnum
from ccontrol_ai_service.agent.protocols import AIAgentProtocol
from ccontrol_ai_service.interface.device import InteractiveDeviceInterface


class AssertAgent(AIAgentProtocol):
    """断言Agent

    负责处理断言相关的操作，通过 AIServiceImpl 提供的 VQA 功能实现自然语言断言。
    """

    def __init__(self, device: InteractiveDeviceInterface, ai_service: AIServiceImpl):
        """初始化

        Args:
            device: 设备接口实例
            ai_service: AIServiceImpl 实例，由 AIAgent 传入以复用
        """
        self.device = device
        self._ai_service = ai_service

    def execute(
        self,
        user_prompt: str,
        *,
        raise_error: bool = True,
        error_message: Optional[str] = None,
        model: Optional[Union[VQAModelEnum, str]] = None,
    ) -> Optional[bool]:
        """执行断言操作

        使用 AIServiceImpl 提供的 VQA 功能，将自然语言断言转换为布尔结果。
        主要使用 VQA 能力进行断言判断。

        Args:
            user_prompt: 用户输入的断言语句
            raise_error: 断言失败时是否抛出异常，默认为 True
            error_message: 自定义错误信息，默认使用断言语句作为错误信息
            model: 可选的 VQA 模型，默认为 None 时会自动选择合适的模型

        Returns:
            Optional[bool]: True 表示断言成立，False 表示断言不成立，None 表示断言执行失败

        Raises:
            AssertionError: 当 raise_error=True 且断言失败时抛出
        """
        try:
            # 使用 VQA 进行断言判断
            result = self._ai_service.vqa(
                question=user_prompt,
                model=model,
            )
            log.info(f"🚀 AssertAgent.execute: prompt={user_prompt}, result={result}")

            if not result and raise_error:
                error_msg = error_message or f"断言失败: {user_prompt}"
                raise AssertionError(error_msg)

            return result
        except Exception as e:
            if isinstance(e, AssertionError):
                raise
            log.error(f"AssertAgent.execute failed: {str(e)}")
            if raise_error:
                raise AssertionError(f"断言执行失败: {str(e)}") from e
            return None
