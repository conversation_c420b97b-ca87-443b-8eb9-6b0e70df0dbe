"""AI Agent主模块

该模块实现了AI Agent的核心功能，包括:
1. AIAgent - 主Agent类，负责协调ActionAgent、QueryAgent和AssertAgent
"""

from typing import Optional, Type

from ccontrol_ai_service import AIServiceImpl, BaseModelT
from ccontrol_ai_service.agent.action_agent import ActionAgent
from ccontrol_ai_service.agent.assert_agent import AssertAgent
from ccontrol_ai_service.agent.query_agent import QueryAgent
from ccontrol_ai_service.interface.device import InteractiveDeviceInterface
from .models import AgentConfig


class AIAgent(AIServiceImpl[InteractiveDeviceInterface]):
    """AI Agent主类

    负责协调和管理其他功能性Agent，包括:
    1. ActionAgent - 执行UI操作
    2. QueryAgent - 结构化信息提取
    3. AssertAgent - 自然语言断言

    该类继承自AIServiceImpl，使用InteractiveDeviceInterface作为设备接口类型。
    """

    def __init__(self, device: InteractiveDeviceInterface, default_config: Optional[AgentConfig] = None):
        """初始化AI Agent

        Args:
            device: 设备接口实例，用于与实际设备交互
            default_config: 默认配置，如果未提供则使用AgentConfig.default()
        """
        super().__init__(device)
        if not default_config:
            default_config = AgentConfig.default()
        self.default_config = default_config

        # 初始化功能性Agent
        self._action_agent = ActionAgent(device, self)
        self._query_agent = QueryAgent(device, self)
        self._assert_agent = AssertAgent(device, self)

    def action(self, user_prompt: str):
        """执行UI操作

        使用ActionAgent执行用户描述的UI操作，如点击、滑动等。

        Args:
            user_prompt: 用户输入的自然语言指令，描述要执行的操作

        Returns:
            操作执行的结果

        Raises:
            可能抛出与UI操作相关的异常
        """
        return self._action_agent.execute(user_prompt)

    def query(self, user_prompt: str, response_format: Type[BaseModelT]):
        """结构化信息提取

        使用QueryAgent从屏幕提取结构化信息。

        Args:
            user_prompt: 用户输入的提示词，描述要提取的信息
            response_format: 期望的响应格式类型，必须继承自BaseModelT

        Returns:
            BaseModelT: 按照指定格式解析的结构化响应对象

        Raises:
            可能抛出与信息提取相关的异常
        """
        return self._query_agent.execute(user_prompt, response_format)

    def assert_true(
        self,
        user_prompt: str,
        *,
        raise_error: bool = True,
        error_message: Optional[str] = None,
    ) -> Optional[bool]:
        """自然语言断言

        使用AssertAgent验证屏幕状态或元素状态是否符合预期。

        Args:
            user_prompt: 用户输入的断言语句，描述期望的状态
            raise_error: 断言失败时是否抛出异常，默认为 True
            error_message: 自定义错误信息，默认使用断言语句作为错误信息

        Returns:
            Optional[bool]: True表示断言成立，False表示断言不成立，None表示断言执行失败

        Raises:
            AssertionError: 当 raise_error=True 且断言失败时抛出
            可能抛出与状态验证相关的异常
        """
        return self._assert_agent.execute(
            user_prompt,
            raise_error=raise_error,
            error_message=error_message,
        )
