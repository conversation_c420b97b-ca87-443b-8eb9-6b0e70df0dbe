import io
from enum import Enum
from typing import Any, List, Optional, Tuple, Union

from PIL import Image
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.tools import tool
from langchain_core.runnables.config import RunnableConfig
from langchain_openai.chat_models import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, MessagesState, StateGraph
from langgraph.prebuilt.tool_node import Tool<PERSON>ode
from pydantic import SecretStr
from pydantic import BaseModel, Field

from ccontrol_common.core.log import log
from ccontrol_common.utils.image import ImageUtil
from ccontrol_common.config.conf import settings
from ccontrol_common.core.http_client import httpx_client
from ccontrol_ai_service.common.errors import (
    AIModelError,
    AIServiceError,
    AITimeoutError,
    AIToolError,
    ElementNotFoundError,
    ElementOperationError,
)
from ccontrol_ai_service.common.prompts import ACTION_AGENT_PROMPT
from ccontrol_ai_service.interface.device import InteractiveDeviceInterface
from ccontrol_ai_service.service.ai_service import <PERSON>ServiceImpl
from ccontrol_ai_service.agent.models import ModelConfig
from ccontrol_ai_service.agent.protocols import AIAgentProtocol


class WorkflowState(str, Enum):
    """工作流状态枚举"""

    START = START  # 开始
    TOOLS = "tools"  # 需要调用工具
    END = END  # 工作流结束

    def __str__(self) -> str:
        return self.value

    def __repr__(self) -> str:
        return self.value


class UIElement(BaseModel):
    """UI元素定位结果"""

    element_id: Optional[str] = Field(description="元素唯一标识", default=None)
    text: Optional[str] = Field(description="元素文本内容", default=None)
    bounds: Optional[Tuple[int, int, int, int]] = Field(description="元素边界框 (left, top, right, bottom)", default=None)
    center_point: Optional[Tuple[int, int]] = Field(description="元素中心点坐标 (x, y)", default=None)

    def __init__(self, *args, **kwargs):
        """自动转换框选坐标系和自动计算中心点坐标"""
        center_point = kwargs.get("center_point")
        bounds = kwargs.get("bounds")

        # 转换坐标系，将四角坐标转换成斜对角坐标
        if bounds and len(bounds) == 8:  # OCR_SERVICE_COORDINATE_COUNT
            x1, y1 = min(bounds[::2]), min(bounds[1::2])
            x2, y2 = max(bounds[::2]), max(bounds[1::2])
            bounds = (x1, y1, x2, y2)
            kwargs["bounds"] = bounds

        # 计算中心点坐标
        if center_point is None and bounds:
            left, top, right, bottom = bounds
            center_point = ((left + right) // 2, (top + bottom) // 2)
            kwargs["center_point"] = center_point

        super().__init__(*args, **kwargs)

    def get_center_point(self) -> Tuple[int, int]:
        """获取元素中心点坐标"""
        if self.center_point is None:
            raise ElementOperationError("获取中心点", "中心点未设置且无法从边界框计算")
        return self.center_point


class ActionAgent(AIAgentProtocol):
    """动作执行Agent

    负责执行UI交互动作，如点击、定位等操作
    """

    def __init__(self, device: InteractiveDeviceInterface, ai_service: AIServiceImpl, model_config: Optional[ModelConfig] = None):
        """初始化

        Args:
            device: 设备接口实例
            model_config: 模型配置
        """
        self.device = device
        self.ai_service = ai_service
        self.model_config = model_config or ModelConfig()

        # 初始化模型
        try:
            self.model = ChatOpenAI(
                model=self.model_config.model,
                api_key=SecretStr("sk-******"),
                base_url=settings.ZEUS_DOMAIN + "/v1",
                temperature=self.model_config.temperature,
                timeout=self.model_config.timeout,
                http_client=httpx_client.get_client(),
                max_retries=0
            )
        except Exception as e:
            log.error(f"初始化AI模型失败: {str(e)}")
            raise AIModelError(f"Failed to initialize AI model: {str(e)}") from e

        # 初始化工具
        self.tools = self._init_tools()
        self.tool_node = ToolNode(self.tools)
        self.model = self.model.bind_tools(self.tools)

        # 初始化工作流
        self.workflow = self._init_workflow()
        self.checkpointer = MemorySaver()
        self.app = self.workflow.compile(checkpointer=self.checkpointer)

    def _init_tools(self) -> List[Any]:
        """初始化工具列表"""

        @tool
        def locate(query: str) -> List[UIElement]:
            """定位UI元素

            Args:
                query: 用自然语言描述要查找的元素

            Returns:
                匹配的UI元素列表

            Raises:
                ElementNotFoundError: 未找到匹配的元素
            """
            log.info(f"🔍 ~ locate: {query}")
            try:
                targets = self.ai_service.grounding(query)
                if not targets:
                    raise ElementNotFoundError(query)

                if isinstance(targets, Image.Image):
                    raise ElementNotFoundError(query)

                elements = [UIElement(bounds=target) for target in targets]
                log.info(f"🚀 ~ locate: {elements}")
                return elements
            except Exception as e:
                log.exception(f"元素定位失败: {str(e)}")
                raise AIToolError(f"Element location failed: {str(e)}") from e

        @tool
        def click(x: int, y: int) -> None:
            """点击UI元素

            Args:
                x: 点击位置的x坐标
                y: 点击位置的y坐标

            Raises:
                ElementOperationError: 点击操作失败
            """
            log.info(f"👆 ~ click: ({x}, {y})")
            try:
                log.info(f"点击坐标: ({x}, {y})")
                self.device.click(x, y)
            except Exception as e:
                log.exception(f"点击操作失败: {str(e)}")
                raise AIToolError(f"Click operation failed: {str(e)}") from e

        return [locate, click]

    def _init_workflow(self) -> StateGraph:
        """初始化工作流图"""
        workflow = StateGraph(MessagesState)

        # 添加节点
        workflow.add_node("action_agent", self._call_model)
        if self.tool_node:
            workflow.add_node("tools", self.tool_node)

        # 添加边
        workflow.add_edge(START, "action_agent")
        workflow.add_conditional_edges(
            "action_agent",
            self._should_continue,
        )
        workflow.add_edge("tools", "action_agent")

        return workflow

    def _should_continue(self, state: MessagesState) -> Union[WorkflowState, str]:
        """判断是否继续执行"""
        try:
            messages = state["messages"]
            last_message = messages[-1]
            tool_calls = getattr(last_message, "tool_calls", [])
            return WorkflowState.TOOLS if tool_calls else WorkflowState.END
        except Exception as e:
            log.error(f"状态判断失败: {str(e)}")
            raise AIServiceError(f"Failed to determine next state: {str(e)}") from e

    def _call_model(self, state: MessagesState):
        """调用模型"""
        try:
            messages = state["messages"]
            response = self.model.invoke(messages)
            return {"messages": [response]}
        except Exception as e:
            log.error(f"模型调用失败: {str(e)}")
            raise AIModelError(f"Model invocation failed: {str(e)}") from e

    def execute(self, user_prompt: str):
        """
        执行动作

        Args:
            user_prompt: 用户输入的提示词，任务指令

        Returns:
            运行结果

        Raises:
            AITimeoutError: Agent执行超时
            AIServiceError: Agent执行失败
        """
        try:
            # 获取截图
            image = self.device.screenshot()
            image_base64 = ImageUtil.convert(image, output_format="base64")

            # 构建消息内容
            system_message = SystemMessage(content=ACTION_AGENT_PROMPT)
            content = [
                {"type": "image_url", "image_url": {"url": str(image_base64)}},
                {"type": "text", "text": str(user_prompt)},
            ]
            human_message = HumanMessage(content=content)

            # 构建初始状态
            initial_state = {"messages": [system_message, human_message]}

            # 执行工作流
            if not self.app:
                raise AIServiceError("Workflow not initialized")

            config: RunnableConfig = {"configurable": {"timeout": self.model_config.timeout, "thread_id": 42}}
            final_state = self.app.invoke(initial_state, config=config)
            return final_state
        except Exception as e:
            if "timeout" in str(e).lower():
                raise AITimeoutError(f"Agent execution timed out: {str(e)}") from e
            log.error(f"Agent执行失败: {str(e)}")
            raise AIServiceError(f"Agent execution failed: {str(e)}") from e

    def visualize(self):
        """可视化工作流图"""
        try:
            if not self.app:
                raise AIServiceError("Workflow not initialized")
            if not hasattr(self.app, "get_graph"):
                raise AIServiceError("Workflow visualization not supported")

            graph = self.app.get_graph()
            if not hasattr(graph, "draw_mermaid_png"):
                raise AIServiceError("Graph visualization not supported")

            image = Image.open(io.BytesIO(graph.draw_mermaid_png()))
            image.show()
        except Exception as e:
            log.error(f"工作流图可视化失败: {str(e)}")
            raise AIServiceError(f"Failed to visualize workflow: {str(e)}") from e


if __name__ == "__main__":
    from ccontrol_ai_service.mock.mock_device import mock_device

    agent = ActionAgent(device=mock_device, ai_service=AIServiceImpl(mock_device))
    # agent.visualize()

    agent.execute("打开YouTube")
