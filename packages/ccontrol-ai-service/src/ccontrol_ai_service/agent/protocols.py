"""Agent协议定义"""

from typing import Any, Protocol, runtime_checkable


@runtime_checkable
class AIAgentProtocol(Protocol):
    """Agent基础协议

    定义了所有Agent必须实现的基础接口。
    每个具体的Agent实现类都必须遵循该协议。
    """

    def execute(self, *args, **kwargs) -> Any:
        """执行Agent逻辑

        该方法允许任意参数，具体参数由实现类自行定义。
        实现类应该在文档中明确说明其参数要求。

        Args:
            *args: 可变位置参数
            **kwargs: 可变关键字参数

        Returns:
            Any: 执行结果，具体类型由实现类定义

        Raises:
            NotImplementedError: 当该方法未被实现时抛出
        """
        raise NotImplementedError("AIAgentProtocol.execute: 未实现") 
