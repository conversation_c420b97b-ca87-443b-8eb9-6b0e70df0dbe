from typing import Optional, Type, Union
from ccontrol_ai_service import AIServiceImpl
from ccontrol_ai_service.common.constans import SYSTEM_PROMPT_VQA_OBJECT, BaseModelT, VQAModelEnum
from ccontrol_ai_service.interface.device import InteractiveDeviceInterface
from ccontrol_ai_service.agent.protocols import AIAgentProtocol


class QueryAgent(AIAgentProtocol):
    """查询Agent

    负责处理查询相关的操作，通过 AIServiceImpl 提供的功能实现结构化信息提取
    """

    def __init__(self, device: InteractiveDeviceInterface, ai_service: AIServiceImpl):
        """初始化

        Args:
            device: 设备接口实例
            ai_service: AIServiceImpl 实例，由 AIAgent 传入以复用
        """
        self.device = device
        self._ai_service = ai_service

    def execute(
        self,
        user_prompt: str,
        response_format: Type[BaseModelT],
        *,
        model: Optional[Union[VQAModelEnum, str]] = None,
        system_prompt: str = SYSTEM_PROMPT_VQA_OBJECT,
    ):
        """
        结构化信息提取

        使用 AIServiceImpl 提供的功能，从设备界面中提取结构化信息。
        主要使用 OCR 和 VQA 能力进行信息提取。

        Args:
            user_prompt: 用户输入的提示词，描述需要提取的信息

        Returns:
            结构化信息，具体格式取决于提示词要求
        """
        # 使用 VQA 进行结构化信息提取
        result = self._ai_service.vqa_object(
            user_prompt,
            response_format,
            model=model,
            system_prompt=system_prompt,
        )

        return result
