from pydantic import BaseModel, Field

from ccontrol_ai_service.common.constans import VQAModelEnum


class ModelConfig(BaseModel):
    """模型基础配置"""

    model: str = Field(default=VQAModelEnum.OPENAI_LATEST.value, description="模型名称")
    temperature: float = Field(default=0.2, description="温度参数，控制输出的随机性")
    timeout: int = Field(default=60, description="超时时间（秒）")

    class Config:
        frozen = True


class AgentConfig(BaseModel):
    """Agent运行时配置"""

    ai_model_config: ModelConfig = Field(default_factory=ModelConfig, description="模型配置")

    @classmethod
    def default(cls) -> "AgentConfig":
        """创建默认配置"""
        return cls(ai_model_config=ModelConfig())

    class Config:
        frozen = True
