[project]
name = "ccontrol-ai-service"
description = "CControl AI Service"
authors = [{ name = "<PERSON><PERSON><PERSON><PERSON><PERSON>", email = "lian<PERSON><PERSON><PERSON>@cvte.com" }]
dynamic = ["version"]
dependencies = [
    "ccontrol-common",
    "langchain-core>=0.3.29; python_version >= '3.9'",
    "langchain-openai>=0.3.0; python_version >= '3.9'",
    "langgraph>=0.2.63,<0.3.0; python_version >= '3.9'",
    # 兼容 win7, rapidfuzz 3.10.0 及以上版本将不支持 python 3.8
    "rapidfuzz>=3.9.0",
]
requires-python = "<4,>=3.8"
readme = "README.md"
license = "MIT"

[project.optional-dependencies]
dev = ["pytest>=8.3.4", "pytest-mock>=3.12.0"]


[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
include = ["ccontrol_ai_service*"]
exclude = ["*.tests*", "*.testing*", "*__pycache__*"]

[tool.setuptools.dynamic]
version = { attr = "ccontrol_ai_service.__version__" }

[build-system]
requires = ["setuptools>=78.1.0"]
build-backend = "setuptools.build_meta"
