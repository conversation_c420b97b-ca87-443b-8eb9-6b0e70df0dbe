import re

import pytest

from ccontrol_common.core.log import log
from ccontrol_common.models.ocr_model import OCRPredictModel
from ccontrol_ai_service import AIServiceImpl
from ccontrol_ai_service.mock.mock_device import mock_device, MockScene

# 测试数据
TEST_CASES = [
    ("Apps", "text", True),  # 预期能找到的文本
    ("NonExistText", "text", False),  # 预期找不到的文本
    (r"\d+", "regex", True),  # 预期能匹配的正则
    (r"778899", "regex", False),  # 预期不能匹配的正则
]


@pytest.fixture(scope="module")
def ai_service():
    """创建 AI Service 实例"""
    service = AIServiceImpl(device=mock_device)
    yield service


@pytest.fixture(autouse=True)
def reset_mock_device():
    """每个测试前重置 mock_device 到默认场景"""
    mock_device.set_scene(MockScene.DEFAULT)
    yield
    mock_device.set_scene(MockScene.DEFAULT)


def test_basic_ocr(ai_service):
    """测试基础 OCR 功能

    验证:
    1. OCR 返回结果不为空
    2. OCR 结果格式正确
    """
    result = ai_service.ocr()

    assert result is not None, "OCR 结果不应为空"
    assert isinstance(result, list), "OCR 结果应该是列表类型"
    assert all(isinstance(item, OCRPredictModel) for item in result), "OCR 结果项应该是字典类型"

    log.info(f"OCR 识别到 {len(result)} 个文本项")


@pytest.mark.parametrize("search_text,search_type,should_find", TEST_CASES)
def test_ocr_search(ai_service, search_text: str, search_type: str, should_find: bool):
    """参数化测试 OCR 搜索功能

    Args:
        search_text: 要搜索的文本或正则表达式
        search_type: 搜索类型 ('text' 或 'regex')
        should_find: 是否应该找到结果
    """
    if search_type == "text":
        result = ai_service.ocr_find_first_by_text(search_text)
    else:
        result = ai_service.ocr_find_first_by_regex(search_text)

    if should_find:
        assert result is not None, f"应该找到匹配项: {search_text}"
        assert isinstance(result, OCRPredictModel), "搜索结果应该是字典类型"
        if search_type == "text":
            assert search_text in result.text, "搜索结果应包含目标文本"
        else:
            assert re.search(search_text, result.text), "搜索结果应匹配正则表达式"
    else:
        assert result is None, f"不应该找到匹配项: {search_text}"


def test_invalid_image(ai_service):
    """测试无效图片输入场景"""
    # 这个测试可能需要修改 mock_device 来模拟无效图片场景
    # 或者考虑移除这个测试，因为 device 应该保证返回有效图片
    pass


if __name__ == "__main__":
    pytest.main(["-s", "-v"])
