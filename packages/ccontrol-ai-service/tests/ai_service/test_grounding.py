import os

import pytest
from PIL import Image

from ccontrol_ai_service.common.constans import BoxLabelColorEnum
from ccontrol_ai_service.service.grounding_service import GroundingServiceImpl
from ccontrol_ai_service.mock.mock_device import mock_device, MockScene

# 测试资源路径配置
TEST_ASSETS_DIR = os.path.join(os.path.dirname(__file__), "assets")
TEST_IMAGE_PATH = os.path.join(TEST_ASSETS_DIR, "image.png")


class TestGroundingService:
    """测试 Grounding 服务"""

    @pytest.fixture(scope="class")
    def service(self):
        """创建 Grounding 服务实例"""
        return GroundingServiceImpl(device=mock_device)

    @pytest.fixture(autouse=True)
    def reset_mock_device(self):
        """每个测试前重置 mock_device 到默认场景"""
        mock_device.set_scene(MockScene.DEFAULT)
        yield
        mock_device.set_scene(MockScene.DEFAULT)

    def test_grounding(self, service):
        """测试基础的目标检测功能"""
        target = "浏览器"
        boxes = service.grounding(target)

        assert isinstance(boxes, list), "应返回坐标列表"
        assert len(boxes) > 0, "应至少检测到一个目标"

        first_box = boxes[0]
        assert len(first_box) == 4, "边界框应包含4个坐标值"
        assert all(isinstance(coord, (int, float)) for coord in first_box), "坐标值应为数字"

    def test_grounding_with_box_color(self, service):
        """测试带标注框的目标检测"""
        target = "浏览器"
        result_image = service.grounding(target, box_color=BoxLabelColorEnum.RED)

        assert isinstance(result_image, Image.Image), "应返回 PIL Image 对象"
        # 不再比较尺寸，因为尺寸由 device 决定

    def test_grounding_with_crop(self, service):
        """测试目标区域裁剪功能"""
        target = "浏览器"
        crop_image = service.grounding(target, is_crop=True)

        assert isinstance(crop_image, Image.Image), "应返回 PIL Image 对象"
        # 裁剪后的尺寸一定小于等于原图，但我们不需要具体比较尺寸

    def test_grounding_find_first_center_point(self, service):
        """测试获取首个目标中心点"""
        target = "浏览器"
        center_point = service.grounding_find_first_center_point(target)

        assert isinstance(center_point, list), "应返回坐标列表"
        assert len(center_point) == 2, "应包含x和y坐标"
        assert all(isinstance(coord, (int, float)) for coord in center_point), "坐标值应为数字"
        # 不再检查具体的坐标范围，因为这取决于 device 返回的图片尺寸

    def test_grounding_find_first_box(self, service):
        """测试获取首个目标边界框"""
        target = "浏览器"
        box = service.grounding_find_first_box(target)

        assert isinstance(box, list), "应返回坐标列表"
        assert len(box) == 4, "应包含4个坐标值"
        assert all(isinstance(coord, (int, float)) for coord in box), "坐标值应为数字"
        assert box[0] <= box[2], "x1应小于等于x2"
        assert box[1] <= box[3], "y1应小于等于y2"

    def test_grounding_not_found(self, service):
        """测试未检测到目标的情况"""
        target = "不存在的目标"

        # 测试基础检测
        boxes = service.grounding(target)
        assert len(boxes) == 0, "应返回空列表"

        # 测试中心点检测
        center_point = service.grounding_find_first_center_point(target)
        assert center_point is None, "应返回None"

        # 测试边界框检测
        box = service.grounding_find_first_box(target)
        assert box is None, "应返回None"
