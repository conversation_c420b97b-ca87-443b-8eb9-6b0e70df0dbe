import os
import time
from pydantic import BaseModel, Field
import pytest
from ccontrol_common.core import log
from ccontrol_ai_service.mock.mock_device import mock_device, MockScene
from ccontrol_ai_service.service.vqa_service import VQAServiceImpl, VQAModelEnum

# 测试资源路径配置（保留用于参考）
TEST_ASSETS_DIR = os.path.join(os.path.dirname(__file__), "assets")
TEST_IMAGE_PATH = os.path.join(TEST_ASSETS_DIR, "image.png")
TEST_IMAGE_PATH_SOUND = os.path.join(TEST_ASSETS_DIR, "sound.png")


class VQAResponseModel(BaseModel):
    """VQA响应模型"""

    result: bool = Field(..., description="Whether the statement made by the user is true or false")


@pytest.fixture
def service():
    """创建 VQA 服务实例"""
    return VQAServiceImpl(device=mock_device)


@pytest.fixture(autouse=True)
def reset_mock_device():
    """每个测试前重置 mock_device 到默认场景"""
    mock_device.set_scene(MockScene.DEFAULT)
    yield
    mock_device.set_scene(MockScene.DEFAULT)


def test_ask_vlm(service):
    """测试开放性问答功能"""
    question = "图片中的浏览器是什么类型的?"
    response = service.ask_vlm(question)

    assert isinstance(response, str), "应返回字符串类型的回答"
    assert len(response) > 0, "回答不应为空"


def test_ask_vlm_with_model(service):
    """测试指定模型的开放性问答"""
    question = "图片中的浏览器是什么类型的?"
    response = service.ask_vlm(question, model=VQAModelEnum.GEMINI_LATEST)

    assert isinstance(response, str), "应返回字符串类型的回答"
    assert len(response) > 0, "回答不应为空"


def test_vqa(service):
    """测试布尔类型问答"""
    start_time = time.time()
    models = [
        VQAModelEnum.GEMINI_LATEST,
        VQAModelEnum.OPENAI_LATEST,
        VQAModelEnum.QWEN_LATEST,
    ]

    # 切换到音频场景
    mock_device.set_scene(MockScene.SOUND)

    for model in models:
        question = "当前音频模式是标准模式?"
        result = service.vqa(question, model=model)
        log.info(f"🚀 model:{model}, question: {question}, result: {result}")

        assert isinstance(result, bool), "应返回布尔类型的结果"
        assert result is True, "应返回True"

        question = "当前音频模式是电影模式?"
        result = service.vqa(question, model=model)
        log.info(f"🚀 model:{model}, question: {question}, result: {result}")

        assert isinstance(result, bool), "应返回布尔类型的结果"
        assert result is False, "应返回False"
    log.info(f"🚀 总耗时: {time.time() - start_time:.2f} 秒")


def test_vqa_with_model(service):
    """测试指定模型的布尔类型问答"""
    question = "图片中是否包含浏览器?"
    result = service.vqa(question, model=VQAModelEnum.GEMINI_LATEST)

    assert isinstance(result, bool), "应返回布尔类型的结果"


def test_vqa_object(service):
    """测试结构化响应问答"""
    question = "图片中是否包含浏览器?"
    response = service.vqa_object(question, response_format=VQAResponseModel)

    assert isinstance(response, VQAResponseModel), "应返回 VQAResponseModel 对象"
    assert isinstance(response.result, bool), "result 字段应为布尔类型"


def test_vqa_object_with_model(service):
    """测试指定模型的结构化响应问答"""
    question = "图片中是否包含浏览器?"
    response = service.vqa_object(
        question,
        response_format=VQAResponseModel,
        model=VQAModelEnum.GEMINI_LATEST,
    )

    assert isinstance(response, VQAResponseModel), "应返回 VQAResponseModel 对象"
    assert isinstance(response.result, bool), "result 字段应为布尔类型"


def test_model_normalize():
    """测试模型标准化功能"""
    # 测试字符串转枚举
    model = VQAModelEnum.normalize("GEMINI_LATEST")
    assert model == VQAModelEnum.GEMINI_LATEST, "字符串应转换为对应的枚举值"

    # 测试枚举保持不变
    model = VQAModelEnum.normalize(VQAModelEnum.GEMINI_LATEST)
    assert model == VQAModelEnum.GEMINI_LATEST, "枚举值应保持不变"

    # 测试无效字符串保持不变
    model = VQAModelEnum.normalize("INVALID_MODEL")
    assert model == "INVALID_MODEL", "无效的模型名称应保持不变"


def test_model_equals():
    """测试模型比较功能"""
    # 测试枚举比较
    assert VQAModelEnum.GEMINI_LATEST.equals(VQAModelEnum.GEMINI_LATEST), "相同枚举值应该相等"

    # 测试字符串名称比较
    assert VQAModelEnum.GEMINI_LATEST.equals("GEMINI_LATEST"), "字符串名称应该与对应枚举值相等"

    # 测试字符串值比较
    assert VQAModelEnum.GEMINI_LATEST.equals(VQAModelEnum.GEMINI_LATEST.value), "字符串值应该与对应枚举值相等"

    # 测试不相等情况
    assert not VQAModelEnum.GEMINI_LATEST.equals(VQAModelEnum.OPENAI_LATEST), "不同枚举值不应该相等"
    assert not VQAModelEnum.GEMINI_LATEST.equals("OPENAI_LATEST"), "不同模型名称不应该相等"
    assert not VQAModelEnum.GEMINI_LATEST.equals(None), "None不应该与任何模型相等"


def test_auto_select_model(service):
    """测试模型自动选择功能"""
    # 测试包含关键词的情况
    question_with_switch = "图片中有没有开关?"
    model = service._auto_select_model(question_with_switch)
    assert model == VQAModelEnum.OPENAI_LATEST, f"包含关键词时应选择 {VQAModelEnum.OPENAI_LATEST}"

    # 测试不包含关键词的情况
    question_without_keyword = "图片中有没有浏览器?"
    model = service._auto_select_model(question_without_keyword)
    assert model == VQAModelEnum.GEMINI_LATEST, "不包含关键词时应选择 Gemini-1.5-Pro"


def test_empty_question(service):
    """测试空问题的处理"""
    with pytest.raises(Exception):
        service.ask_vlm("")


def test_vqa_with_model_string(service):
    """测试使用字符串指定模型的布尔类型问答"""
    question = "图片中是否包含浏览器?"
    # 使用字符串模型名称
    result = service.vqa(question, model="GEMINI_LATEST")
    assert isinstance(result, bool), "应返回布尔类型的结果"

    # 使用枚举值
    result = service.vqa(question, model=VQAModelEnum.GEMINI_LATEST.value)
    assert isinstance(result, bool), "应返回布尔类型的结果"


def test_vqa_object_with_model_string(service):
    """测试使用字符串指定模型的结构化响应问答"""
    question = "图片中是否包含浏览器?"
    # 使用字符串模型名称
    response = service.vqa_object(
        question,
        response_format=VQAResponseModel,
        model="GEMINI_LATEST",
    )
    assert isinstance(response, VQAResponseModel), "应返回 VQAResponseModel 对象"
    assert isinstance(response.result, bool), "result 字段应为布尔类型"

    # 使用枚举值
    response = service.vqa_object(
        question,
        response_format=VQAResponseModel,
        model=VQAModelEnum.GEMINI_LATEST.value,
    )
    assert isinstance(response, VQAResponseModel), "应返回 VQAResponseModel 对象"
    assert isinstance(response.result, bool), "result 字段应为布尔类型"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
