import pytest

from ccontrol_common.core.errors import ServerError

from ccontrol_ai_service.service.roboflow_service import (
    RoboflowServiceImpl,
    SearchImagesInput,
    ImageDetail,
    SearchImagesResult,
)


@pytest.fixture
def service():
    """创建 RoboflowService 实例"""
    return RoboflowServiceImpl()


def test_get_all_classes_success(service: RoboflowServiceImpl):
    """测试成功获取所有类别"""
    classes = service.get_all_classes()
    assert isinstance(classes, list)
    assert len(classes) > 0
    assert all(isinstance(c, str) for c in classes)


def test_get_images_without_filter(service: RoboflowServiceImpl):
    """测试不带过滤条件获取图片列表"""
    images = service.get_images()
    assert isinstance(images, list)
    assert len(images) <= 20  # 默认限制

    # 验证返回的图片数据结构
    for img in images:
        assert "id" in img
        assert "annotations" in img
        assert "labels" in img


def test_get_images_with_filter(service: RoboflowServiceImpl):
    """测试带类别过滤获取图片列表"""
    # 先获取一个有效的类别
    classes = service.get_all_classes()
    assert len(classes) > 0

    # 使用第一个类别作为过滤条件
    images = service.get_images(classes=classes[0], limit=5)
    assert isinstance(images, list)
    assert len(images) <= 5


def test_get_image_detail_success(service: RoboflowServiceImpl):
    """测试成功获取图片详情"""
    # 先获取一个有效的图片ID
    images = service.get_images(limit=1)
    assert len(images) > 0
    image_id = images[0]["id"]

    detail = service.get_image_detail(image_id)
    assert isinstance(detail, ImageDetail)
    assert detail.id == image_id


def test_get_image_detail_not_found(service: RoboflowServiceImpl):
    """测试获取不存在的图片详情"""
    with pytest.raises(ServerError) as exc_info:
        service.get_image_detail("non_existent_id")
    assert "不存在" in str(exc_info.value)


def test_search_images_success(service: RoboflowServiceImpl):
    """测试成功搜索图片"""
    search_input = SearchImagesInput(limit=5, offset=0)
    result = service.search_images(search_input)

    assert isinstance(result, SearchImagesResult)
    assert len(result.results) <= 5
    assert result.total >= 0
    assert isinstance(result.offset, int)

    # 验证返回的图片数据结构
    for image in result.results:
        assert isinstance(image.id, str)
        assert isinstance(image.labels, list)
        assert "annotations" in image.model_dump()


def test_search_images_with_filters(service: RoboflowServiceImpl):
    """测试带过滤条件搜索图片"""
    # 先获取一个有效的类别
    classes = service.get_all_classes()
    assert len(classes) > 0

    search_input = SearchImagesInput(className=classes[0], limit=5)
    result = service.search_images(search_input)

    assert isinstance(result, SearchImagesResult)
    for image in result.results:
        assert classes[0] in image.model_dump().get("annotations", {}).get("classes", {})


def test_empty_image_id(service: RoboflowServiceImpl):
    """测试空图片ID"""
    with pytest.raises(ServerError) as exc_info:
        service.get_image_detail("")
    assert "图片ID不能为空" in str(exc_info.value)
