from unittest.mock import Mock, patch

import pytest
from PIL import Image
from ccontrol_ai_service.service.grounding_service import GroundingServiceImpl
from ccontrol_ai_service.service.image_compare_service import (
    ImageCompareServiceImpl,
    ImageCompareResult,
)
from ccontrol_ai_service.service.roboflow_service import RoboflowServiceImpl
from ccontrol_ai_service.service.yolo_service import (
    YOLOServiceImpl,
    DetectionResult,
    Detection,
)


@pytest.fixture
def mock_device():
    device = Mock()
    # 创建一个测试用的空白图像
    device.screenshot.return_value = Image.new("RGB", (100, 100), color="white")
    return device


@pytest.fixture
def mock_roboflow_service():
    service = Mock(spec=RoboflowServiceImpl)
    service.get_all_classes.return_value = ["test_target"]
    service.get_images.return_value = [{"urls": {"original": "http://test.com/image.jpg"}}]
    return service


@pytest.fixture
def mock_yolo_service():
    service = Mock(spec=YOLOServiceImpl)
    detection = Detection(label="test", confidence=0.9, bbox=[10, 10, 50, 50])
    service.detect_interactive_elements.return_value = DetectionResult(detections=[detection])
    return service


@pytest.fixture
def mock_image_compare_service():
    service = Mock(spec=ImageCompareServiceImpl)
    service.compare_images.return_value = ImageCompareResult(confidence=0.9, is_similar=True)
    return service


def test_grounding_with_roboflow_match(mock_device, mock_roboflow_service, mock_yolo_service, mock_image_compare_service):
    # 创建 GroundingService 实例，注入 mock 服务
    grounding_service = GroundingServiceImpl(
        device=mock_device,
        roboflow_service=mock_roboflow_service,
        yolo_service=mock_yolo_service,
        image_compare_service=mock_image_compare_service,
    )

    # 执行 grounding
    result = grounding_service.grounding("test_target")

    # 验证返回结果
    assert result is not None
    assert len(result) == 1
    assert result[0] == [10, 10, 50, 50]

    # 验证各个服务是否按预期调用
    mock_roboflow_service.get_all_classes.assert_called_once()
    mock_roboflow_service.get_images.assert_called_once_with(classes=["test_target"])
    mock_yolo_service.detect_interactive_elements.assert_called_once()
    mock_image_compare_service.compare_images.assert_called_once()


def test_grounding_with_roboflow_no_match(mock_device, mock_roboflow_service, mock_yolo_service, mock_image_compare_service):
    # 配置 mock 服务返回无匹配结果
    mock_image_compare_service.compare_images.return_value = ImageCompareResult(confidence=0.5, is_similar=False)

    grounding_service = GroundingServiceImpl(
        device=mock_device,
        roboflow_service=mock_roboflow_service,
        yolo_service=mock_yolo_service,
        image_compare_service=mock_image_compare_service,
    )

    with patch("ccontrol_ai_service.service.grounding_service.do_post") as mock_do_post:
        # 配置 do_post 返回模拟的 Grounding API 响应
        mock_do_post.return_value.is_success = True
        mock_do_post.return_value.json.return_value = {"data": [[0.1, 0.1, 0.5, 0.5]]}

        # 执行 grounding
        result = grounding_service.grounding("test_target")

        # 验证是否调用了原始的 Grounding API
        assert mock_do_post.called
        assert result == [[10, 10, 50, 50]]  # 坐标已被转换为像素值


def test_grounding_with_non_roboflow_target(mock_device, mock_roboflow_service, mock_yolo_service, mock_image_compare_service):
    # 配置 mock roboflow service 返回不包含目标的类别列表
    mock_roboflow_service.get_all_classes.return_value = ["other_target"]

    grounding_service = GroundingServiceImpl(
        device=mock_device,
        roboflow_service=mock_roboflow_service,
        yolo_service=mock_yolo_service,
        image_compare_service=mock_image_compare_service,
    )

    with patch("ccontrol_ai_service.service.grounding_service.do_post") as mock_do_post:
        # 配置 do_post 返回模拟的 Grounding API 响应
        mock_do_post.return_value.is_success = True
        mock_do_post.return_value.json.return_value = {"data": [[0.1, 0.1, 0.5, 0.5]]}

        # 执行 grounding
        result = grounding_service.grounding("test_target")

        # 验证是否直接使用了原始的 Grounding API
        assert mock_do_post.called
        assert result == [[10, 10, 50, 50]]  # 坐标已被转换为像素值


def test_grounding_with_crop_option(mock_device, mock_roboflow_service, mock_yolo_service, mock_image_compare_service):
    grounding_service = GroundingServiceImpl(
        device=mock_device,
        roboflow_service=mock_roboflow_service,
        yolo_service=mock_yolo_service,
        image_compare_service=mock_image_compare_service,
    )

    # 执行 grounding 并请求裁剪
    result = grounding_service.grounding("test_target", is_crop=True)

    # 验证返回的是 PIL Image 对象
    assert isinstance(result, Image.Image)
    assert result.size == (40, 40)  # 裁剪区域大小应为 (50-10, 50-10)


def test_grounding_with_empty_roboflow_boxes(mock_device, mock_roboflow_service, mock_yolo_service, mock_image_compare_service):
    # 配置 mock 服务返回空的检测结果
    mock_yolo_service.detect_interactive_elements.return_value = DetectionResult(detections=[])

    grounding_service = GroundingServiceImpl(
        device=mock_device,
        roboflow_service=mock_roboflow_service,
        yolo_service=mock_yolo_service,
        image_compare_service=mock_image_compare_service,
    )

    with patch("ccontrol_ai_service.service.grounding_service.do_post") as mock_do_post:
        mock_do_post.return_value.is_success = True
        mock_do_post.return_value.json.return_value = {"data": [[0.1, 0.1, 0.5, 0.5]]}

        result = grounding_service.grounding("test_target")

        # 验证在 Roboflow 检测为空时是否正确回退到原始 Grounding
        assert mock_do_post.called
        assert result == [[10, 10, 50, 50]]


def test_grounding_with_none_matching_boxes(mock_device, mock_roboflow_service, mock_yolo_service, mock_image_compare_service):
    # 配置 mock 服务返回有检测结果但没有匹配项
    detection = Detection(label="test", confidence=0.9, bbox=[10, 10, 50, 50])
    mock_yolo_service.detect_interactive_elements.return_value = DetectionResult(detections=[detection])
    mock_image_compare_service.compare_images.return_value = ImageCompareResult(confidence=0.5, is_similar=False)

    grounding_service = GroundingServiceImpl(
        device=mock_device,
        roboflow_service=mock_roboflow_service,
        yolo_service=mock_yolo_service,
        image_compare_service=mock_image_compare_service,
    )

    with patch("ccontrol_ai_service.service.grounding_service.do_post") as mock_do_post:
        mock_do_post.return_value.is_success = True
        mock_do_post.return_value.json.return_value = {"data": [[0.1, 0.1, 0.5, 0.5]]}

        result = grounding_service.grounding("test_target")

        # 验证在没有匹配框时是否正确回退到原始 Grounding
        assert mock_do_post.called
        assert result == [[10, 10, 50, 50]]


def test_grounding_with_invalid_roboflow_image_url(mock_device, mock_roboflow_service, mock_yolo_service, mock_image_compare_service):
    # 配置 mock 服务返回无效的图片 URL
    mock_roboflow_service.get_images.return_value = [{"urls": {}}]

    grounding_service = GroundingServiceImpl(
        device=mock_device,
        roboflow_service=mock_roboflow_service,
        yolo_service=mock_yolo_service,
        image_compare_service=mock_image_compare_service,
    )

    with patch("ccontrol_ai_service.service.grounding_service.do_post") as mock_do_post:
        mock_do_post.return_value.is_success = True
        mock_do_post.return_value.json.return_value = {"data": [[0.1, 0.1, 0.5, 0.5]]}

        result = grounding_service.grounding("test_target")

        # 验证在图片 URL 无效时是否正确回退到原始 Grounding
        assert mock_do_post.called
        assert result == [[10, 10, 50, 50]]


class MockDevice:
    def screenshot(self):
        return "mock_image"


@pytest.fixture
def grounding_service():
    device = MockDevice()
    return GroundingServiceImpl(device)


def test_parse_vlm_response_to_boxes(grounding_service):
    response_content = "[100, 200, 300, 400], (500, 600, 700, 800), 900,1000,1100,1200"
    expected_boxes = [
        [100, 200, 300, 400],
        [500, 600, 700, 800],
        [0.9, 1.0, 1.1, 1.2],
    ]
    boxes = grounding_service._parse_vlm_response_to_boxes(response_content)
    assert boxes == expected_boxes


def test_parse_vlm_response_to_boxes_invalid_format(grounding_service):
    response_content = "invalid format"
    boxes = grounding_service._parse_vlm_response_to_boxes(response_content)
    assert boxes == []
