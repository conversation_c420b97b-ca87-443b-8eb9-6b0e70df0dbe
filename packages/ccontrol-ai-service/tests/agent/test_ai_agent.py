"""AIAgent 单元测试

该模块包含 AIAgent 的单元测试，主要测试：
1. 初始化
2. action 方法
3. query 方法
4. assert_true 方法
"""

import pytest
from pydantic import BaseModel

from ccontrol_ai_service.agent.ai_agent import AIAgent
from ccontrol_ai_service.mock.mock_device import mock_device, MockScene
from ccontrol_ai_service.agent.models import AgentConfig, ModelConfig
from ccontrol_ai_service.common.errors import (
    AIServiceError,
    AIModelError,
    ElementNotFoundError,
)


class TestAIAgent:
    """AIAgent 测试类"""

    @pytest.fixture(scope="class")
    def agent(self):
        """创建 AIAgent 实例"""
        return AIAgent(device=mock_device)

    @pytest.fixture(autouse=True)
    def reset_mock_device(self):
        """每个测试前重置 mock_device 到默认场景"""
        mock_device.set_scene(MockScene.DEFAULT)
        yield
        mock_device.set_scene(MockScene.DEFAULT)

    def test_init_with_default_config(self):
        """测试使用默认配置初始化"""
        agent = AIAgent(device=mock_device)
        assert agent.default_config == AgentConfig.default()
        assert agent.device == mock_device

    def test_init_with_custom_config(self):
        """测试使用自定义配置初始化"""
        custom_config = AgentConfig(
            ai_model_config=ModelConfig(
                model="gpt-4",
                temperature=0.2,
                timeout=30,
            )
        )
        agent = AIAgent(device=mock_device, default_config=custom_config)
        assert agent.default_config == custom_config

    def test_action(self, agent, mocker):
        """测试 action 方法"""
        # Mock ActionAgent.execute
        mock_execute = mocker.patch.object(agent._action_agent, "execute")
        mock_execute.return_value = "Action executed"

        # 执行操作
        result = agent.action("点击确定按钮")

        # 验证
        mock_execute.assert_called_once_with("点击确定按钮")
        assert result == "Action executed"

    def test_action_with_element_not_found(self, agent, mocker):
        """测试 action 方法 - 元素未找到的情况"""
        # Mock ActionAgent.execute 抛出 ElementNotFoundError
        mock_execute = mocker.patch.object(agent._action_agent, "execute")
        mock_execute.side_effect = ElementNotFoundError("未找到确定按钮")

        # 验证异常抛出
        with pytest.raises(ElementNotFoundError) as exc_info:
            agent.action("点击确定按钮")
        assert "未找到确定按钮" in str(exc_info.value)

    def test_query(self, agent, mocker):
        """测试 query 方法"""

        # 定义测试响应格式
        class TestResponse(BaseModel):
            text: str
            value: int

        # Mock QueryAgent.execute
        mock_execute = mocker.patch.object(agent._query_agent, "execute")
        expected_response = TestResponse(text="test", value=42)
        mock_execute.return_value = expected_response

        # 执行查询
        result = agent.query("获取按钮文本", TestResponse)

        # 验证
        mock_execute.assert_called_once_with("获取按钮文本", TestResponse)
        assert result == expected_response
        assert isinstance(result, TestResponse)

    def test_query_with_model_error(self, agent, mocker):
        """测试 query 方法 - 模型错误的情况"""

        class TestResponse(BaseModel):
            text: str
            value: int

        # Mock QueryAgent.execute 抛出 AIModelError
        mock_execute = mocker.patch.object(agent._query_agent, "execute")
        mock_execute.side_effect = AIModelError("模型响应超时")

        # 验证异常抛出
        with pytest.raises(AIModelError) as exc_info:
            agent.query("获取按钮文本", TestResponse)
        assert "模型响应超时" in str(exc_info.value)

    def test_assert_true(self, agent, mocker):
        """测试 assert_true 方法"""
        # Mock AssertAgent.execute
        mock_execute = mocker.patch.object(agent._assert_agent, "execute")
        mock_execute.return_value = True

        # 执行断言
        result = agent.assert_true("页面是否显示登录按钮")

        # 验证
        mock_execute.assert_called_once_with("页面是否显示登录按钮")
        assert result is True

    def test_assert_true_with_service_error(self, agent, mocker):
        """测试 assert_true 方法 - 服务错误的情况"""
        # Mock AssertAgent.execute 抛出 AIServiceError
        mock_execute = mocker.patch.object(agent._assert_agent, "execute")
        mock_execute.side_effect = AIServiceError("AI服务暂时不可用")

        # 验证异常抛出
        with pytest.raises(AIServiceError) as exc_info:
            agent.assert_true("页面是否显示登录按钮")
        assert "AI服务暂时不可用" in str(exc_info.value)

    def test_assert_true_returns_false(self, agent, mocker):
        """测试 assert_true 方法 - 断言为 False 的情况"""
        # Mock AssertAgent.execute 返回 False
        mock_execute = mocker.patch.object(agent._assert_agent, "execute")
        mock_execute.return_value = False

        # 执行断言
        result = agent.assert_true("页面是否显示注销按钮")

        # 验证
        mock_execute.assert_called_once_with("页面是否显示注销按钮")
        assert result is False
