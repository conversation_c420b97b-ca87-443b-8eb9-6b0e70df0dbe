from enum import Enum
import json
from datetime import datetime
from typing import Optional

from pydantic import BaseModel
from pydantic.alias_generators import to_camel

from ccontrol_test_engine.model.agent import AgentModel
from ccontrol_common.config.conf import settings
from ccontrol_common.core.http_client import do_get


# 枚举定义
class DeviceStatusEnum(str, Enum):
    IDLE = "idle"
    OCCUPIED = "occupied"
    TESTING = "testing"
    ERROR = "error"


class OccupancyTypeEnum(str, Enum):
    EXCLUSIVE = "exclusive"
    SHARED = "shared"


class StatusJson(BaseModel):
    id: Optional[int] = None
    led_red: Optional[bool] = None
    slot_sn: Optional[str] = None
    device_sn: Optional[str] = None
    ir_status: Optional[str] = None
    led_green: Optional[bool] = None
    cam1_power: Optional[bool] = None
    cam2_power: Optional[bool] = None
    usb30_power: Optional[bool] = None
    tv_power_12v: Optional[bool] = None
    keypad_status: Optional[str] = None
    usb_device_mount: Optional[bool] = None
    adb_link: Optional[bool] = None
    usb_hid_exist: Optional[bool] = None


class DeviceModel(BaseModel):
    id: int
    serial_number: str
    name: str
    status: DeviceStatusEnum
    occupancy_type: Optional[OccupancyTypeEnum] = None
    occupied_by: Optional[str] = None
    occupied_at: Optional[datetime] = None
    occupy_expires_at: Optional[datetime] = None
    status_json: Optional[StatusJson] = None
    mac: Optional[str] = None
    ram: Optional[int] = None
    rom: Optional[int] = None
    platform: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None
    sos_name: Optional[str] = None
    batch_id: Optional[str] = None
    img_url: Optional[str] = None
    adb_forward_port: Optional[int] = 5555

    agent_id: str

    agent: AgentModel

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
    }


def get_device_info(device_id: int):
    """获取设备信息"""
    resp = do_get(
        settings.ZEUS_DOMAIN + "/api/model/cControlDevice/findFirst",
        params={"q": json.dumps({"where": {"id": device_id}, "include": {"agent": True}})},
    )
    if not resp.is_success:
        return None

    device_entity = resp.json().get("data")
    if not device_entity:
        return None

    return DeviceModel.model_validate(device_entity)


if __name__ == "__main__":
    result = get_device_info(102)
    if result:
        print(f"result: {result.model_dump_json(indent=2)}")
