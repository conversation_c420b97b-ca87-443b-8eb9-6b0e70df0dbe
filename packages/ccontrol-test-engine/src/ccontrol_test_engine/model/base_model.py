from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, <PERSON><PERSON>


def to_camel(string: str) -> str:
    """Convert snake_case to camelCase"""
    components = string.split("_")
    return components[0] + "".join(x.title() for x in components[1:])


class BaseModelMixin(BaseModel):
    """Base model with user tracking"""

    creator_id: str
    created_at: datetime = Field(default_factory=datetime.now)
    updater_id: str
    updated_at: datetime = Field(default_factory=datetime.now)
    deleted_at: Optional[datetime] = None

    class Config:
        alias_generator = to_camel
        populate_by_name = True


class BaseWithoutUserModelMixin(BaseModel):
    """Base model without user tracking"""

    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    deleted_at: Optional[datetime] = None

    class Config:
        alias_generator = to_camel
        populate_by_name = True


class SystemConfigBase(BaseWithoutUserModelMixin):
    """System configuration base model"""

    key: str = Field(..., max_length=100)
    value: Json
    description: Optional[str] = Field(None, max_length=255)
