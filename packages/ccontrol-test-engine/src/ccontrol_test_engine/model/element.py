from typing import Optional, <PERSON><PERSON>

from PIL import Image, ImageDraw, ImageFont
from pydantic import BaseModel


OCR_SERVICE_COORDINATE_COUNT = 8


class ElementModel(BaseModel):
    element_name: Optional[str]
    screenshot_image: Image.Image
    element_image: Image.Image
    bounds: Tuple[int, int, int, int]
    center_point: Tuple[int, int]

    # 配置pydantic允许使用PIL.Image.Image类型，使用后将不支持序列化
    class Config:
        arbitrary_types_allowed = True

    def __init__(self, *args, **kwargs):
        """自动转换框选坐标系和自动计算中心点坐标"""
        center_point = kwargs.get("center_point")
        bounds = kwargs.get("bounds")

        # 转换坐标系，将四角坐标转换成斜对角坐标
        if bounds and len(bounds) == OCR_SERVICE_COORDINATE_COUNT:
            x1, y1 = min(bounds[::2]), min(bounds[1::2])
            x2, y2 = max(bounds[::2]), max(bounds[1::2])
            bounds = (x1, y1, x2, y2)
            kwargs["bounds"] = bounds

        # 计算中心点坐标
        if center_point is None and bounds:
            left, top, right, bottom = bounds
            center_point = ((left + right) // 2, (top + bottom) // 2)
            kwargs["center_point"] = center_point

        # 模型实例化
        super().__init__(*args, **kwargs)

    def get_center_point(self) -> Tuple[int, int]:
        """Retrieve the center point of the element."""
        if self.center_point is None:
            raise ValueError(
                "Center point is not set and cannot be calculated without bounds update."
            )
        return self.center_point

    def highlight_element(self):
        """Highlight the element on the full-screen screenshot using Pillow."""
        if not self.bounds:
            print("Bounds not set. Cannot highlight element.")
            return

        img = self.screenshot_image.copy()
        draw = ImageDraw.Draw(img)

        # 画矩形区域（突出显示元素）
        left, top, right, bottom = self.bounds
        draw.rectangle((left, top, right, bottom), outline="green", width=4)

        # 画中心点
        center_x, center_y = self.get_center_point()
        draw.ellipse(
            (center_x - 2, center_y - 2, center_x + 2, center_y + 2), fill="red"
        )

        # 添加文字描述（在矩形右下角）
        if self.element_name:
            font = ImageFont.load_default()
            text_bbox = font.getbbox(self.element_name)
            text_x = right - text_bbox[2] - 5  # Leave 5px margin (right edge of bbox)
            text_y = bottom - text_bbox[3] - 5  # Leave 5px margin (bottom edge of bbox)
            draw.text((text_x, text_y), self.element_name, font=font, fill="green")

        return img


# Example Usage
if __name__ == "__main__":
    full_screenshot_img = Image.new(
        "RGB", (800, 600)
    )  # Placeholder: Replace with actual image loading/code
    element_img = Image.new(
        "RGB", (50, 50)
    )  # Placeholder: Replace with actual image loading/code

    element = ElementModel(
        element_name="Login Button",
        screenshot_image=full_screenshot_img,
        element_image=element_img,
        bounds=(100, 100, 200, 200),
    )

    print(element.get_center_point())
    image = element.highlight_element()
    if image:
        image.show()
