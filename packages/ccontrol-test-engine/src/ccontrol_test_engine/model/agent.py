from datetime import datetime
from typing import Optional

from pydantic import BaseModel, IPvAnyAddress
from pydantic.alias_generators import to_camel


class AgentModel(BaseModel):
    id: str
    serial_number: str
    name: Optional[str] = None
    heartbeat_time: Optional[datetime] = None
    mac: Optional[str] = None
    host: Optional[IPvAnyAddress] = None
    system_type: Optional[str] = None
    version: Optional[str] = None
    status_json: Optional[dict] = None

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
    }
