from enum import Enum
from typing import Optional
from pydantic import BaseModel, Field, Json
from ccontrol_test_engine.model.base_model import to_camel


class EventTypeEnum(str, Enum):
    """事件类型枚举"""

    # 注意: 这里需要根据实际业务补充具体的枚举值
    AI = "ai"
    AGENT_RPC = "agent_rpc"
    PYTHON = "python"


class CControlEventCreate(BaseModel):
    """创建CControl事件的模型"""

    method: str = Field(..., max_length=255)
    nick_name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    event_type: EventTypeEnum
    input_schema: Optional[Json] = None
    output_schema: Optional[Json] = None
    agent_id: Optional[str] = Field(None, max_length=64)
    python_code: Optional[str] = None
    is_published: bool = False

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
        "json_encoders": {EventTypeEnum: lambda v: v.value},
    }

    def model_dump_exclude_none(self, *args, **kwargs):
        return super().model_dump(*args, exclude_none=True, by_alias=True, **kwargs)


class CControlEventUpdate(BaseModel):
    """更新CControl事件的模型"""

    method: Optional[str] = Field(None, max_length=255)
    nick_name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    event_type: Optional[EventTypeEnum] = None
    input_schema: Optional[Json] = None
    output_schema: Optional[Json] = None
    agent_id: Optional[str] = Field(None, max_length=64)
    python_code: Optional[str] = None
    is_published: Optional[bool] = None

    model_config = {
        "extra": "forbid",
        "alias_generator": to_camel,
        "populate_by_name": True,
    }

    def model_dump_exclude_none(self, *args, **kwargs):
        return super().model_dump(*args, exclude_none=True, by_alias=True, **kwargs)


class CControlEventUpload(BaseModel):
    """上传CControl事件的模型，数据来源于用户自定义和动态获取 bundle code"""

    method: str = Field(..., max_length=255)
    nick_name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    event_type: Optional[str] = Field(default=EventTypeEnum.PYTHON.value)
    agent_id: Optional[str] = Field(None, max_length=64)
    python_code: Optional[str] = None
    is_published: bool = False

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
        "json_encoders": {EventTypeEnum: lambda v: v.value},
    }

    def model_dump_exclude_none(self, *args, **kwargs):
        return super().model_dump(*args, exclude_none=True, by_alias=True, **kwargs)


if __name__ == "__main__":
    result = CControlEventUpdate.model_validate(
        {
            "method": "test",
            "python_code": "test",
            "nick_name": None,
            "description": None,
        }
    )
    print(f"result: {result}")
    print(f"result: {result.model_dump_exclude_none()}")
