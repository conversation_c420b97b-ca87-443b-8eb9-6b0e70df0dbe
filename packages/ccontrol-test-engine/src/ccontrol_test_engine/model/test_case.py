from enum import Enum
from typing import Optional, List
from pydantic import BaseModel, Field, Json
from ccontrol_test_engine.model.base_model import to_camel


class TestCaseTypeEnum(str, Enum):
    """测试用例类型枚举"""

    FUNCTION = "function"  # 功能测试
    PERFORMANCE = "performance"  # 性能测试
    SECURITY = "security"  # 安全测试
    COMPATIBILITY = "compatibility"  # 兼容性测试
    USABILITY = "usability"  # 可用性测试


class TestCaseStatusEnum(str, Enum):
    """测试用例状态枚举"""

    ACTIVE = "active"  # 活跃状态
    DEPRECATED = "deprecated"  # 已废弃
    DRAFT = "draft"  # 草稿状态


class CControlTestCaseCreate(BaseModel):
    """创建CControl测试用例的模型"""

    name: str = Field(..., max_length=255)
    description: Optional[str] = None
    test_type: TestCaseTypeEnum
    event_method: str = Field(..., max_length=255)
    input_data: Optional[Json] = None
    expected_output: Optional[Json] = None
    tags: Optional[List[str]] = None
    status: TestCaseStatusEnum = Field(default=TestCaseStatusEnum.ACTIVE)
    timeout_seconds: Optional[int] = Field(default=30)
    python_code: Optional[str] = None

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
        "json_encoders": {
            TestCaseTypeEnum: lambda v: v.value,
            TestCaseStatusEnum: lambda v: v.value,
        },
    }

    def model_dump_json_exclude_none(self, *args, **kwargs):
        return super().model_dump_json(*args, exclude_none=True, **kwargs)


class CControlTestCaseUpdate(BaseModel):
    """更新CControl测试用例的模型"""

    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    test_type: Optional[TestCaseTypeEnum] = None
    event_method: Optional[str] = Field(None, max_length=255)
    input_data: Optional[Json] = None
    expected_output: Optional[Json] = None
    tags: Optional[List[str]] = None
    status: Optional[TestCaseStatusEnum] = None
    timeout_seconds: Optional[int] = None
    python_code: Optional[str] = None

    model_config = {
        "extra": "forbid",
        "alias_generator": to_camel,
        "populate_by_name": True,
    }

    def model_dump_json_exclude_none(self, *args, **kwargs):
        return super().model_dump_json(*args, exclude_none=True, **kwargs)


class CControlTestCaseUpload(BaseModel):
    """上传CControl测试用例的模型，数据来源于用户自定义和动态获取 bundle code"""

    name: str = Field(..., max_length=255)
    description: Optional[str] = Field(None)
    test_type: TestCaseTypeEnum = Field(default=TestCaseTypeEnum.FUNCTION)
    event_method: str = Field(..., max_length=255)
    input_data: Optional[Json] = None
    expected_output: Optional[Json] = None
    tags: Optional[List[str]] = None
    status: TestCaseStatusEnum = Field(default=TestCaseStatusEnum.ACTIVE)
    timeout_seconds: Optional[int] = Field(default=30)
    python_code: Optional[str] = None

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
        "json_encoders": {
            TestCaseTypeEnum: lambda v: v.value,
            TestCaseStatusEnum: lambda v: v.value,
        },
    }

    def model_dump_json_exclude_none(self, *args, **kwargs):
        return super().model_dump_json(*args, exclude_none=True, **kwargs)


if __name__ == "__main__":
    result = CControlTestCaseUpdate.model_validate(
        {
            "id": "123",
            "name": "测试用例",
            "test_type": "function",
            "event_method": "test.method",
            "python_code": "print('hello')",
            "description": None,
        }
    )
    print(f"result: {result}")
    print(f"result: {result.model_dump_json_exclude_none()}")
