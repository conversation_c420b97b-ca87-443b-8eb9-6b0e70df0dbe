import os

from ccontrol_test_engine.config.path_config import TEST_FOLDER_PATH
from ccontrol_test_engine.service.event.model.event_model import (
    EventTypeEnum,
    PythonEventModel,
)
from ccontrol_test_engine.service.task.model.task_model import (
    TaskNodeModel,
    TestSuiteNodeModel,
    TestCaseNodeModel,
    EventNodeModel,
)


def mock_task_print_hello_world():
    task_node = TaskNodeModel(task_id="local_task", name="local_task")

    test_suite = TestSuiteNodeModel(
        test_suite_id="local_test_suite", name="local_test_suite"
    )
    task_node.children = test_suite

    test_case_node = TestCaseNodeModel(
        test_case_id="local_test_case", name="local_test_case"
    )
    event_node = EventNodeModel(
        event_id="event",
        name="event",
        data=PythonEventModel(
            type=EventTypeEnum.PYTHON, content="print('hello world')"
        ),
    )
    test_case_node.children.append(event_node)

    test_suite.children.append(test_case_node)
    return task_node


def mock_task(
    test_case_path: str = os.path.join(TEST_FOLDER_PATH, "test_case", "case_001.py"),
) -> TaskNodeModel:
    task_node = TaskNodeModel(task_id="local_task", name="local_task")

    test_suite = TestSuiteNodeModel(
        test_suite_id="local_test_suite", name="local_test_suite"
    )
    task_node.children = test_suite

    test_case_node = TestCaseNodeModel(
        test_case_id="local_test_case", name="local_test_case"
    )
    event_node = EventNodeModel(
        event_id="event",
        name="event",
        data=PythonEventModel(
            type=EventTypeEnum.PYTHON, content="print('hello world')"
        ),
    )
    test_case_node.children.append(event_node)

    with open(test_case_path, "r", encoding="utf-8") as f:
        event_content = f.read()
    test_case_node.children.append(
        EventNodeModel(
            event_id="event",
            name="event",
            data=PythonEventModel(type=EventTypeEnum.PYTHON, content=event_content),
        )
    )

    test_suite.children.append(test_case_node)
    return task_node


def test_mock_task():
    task = mock_task()
    print(task.model_dump_json(indent=2))


if __name__ == "__main__":
    test_mock_task()
