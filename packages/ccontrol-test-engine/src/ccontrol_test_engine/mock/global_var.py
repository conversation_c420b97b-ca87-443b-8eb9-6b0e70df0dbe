from ccontrol_test_engine.model.device import get_device_info
from ccontrol_test_engine.service.task.model.global_model import GlobalVarModel


def mock_global_var(device_id: int):
    device_info = get_device_info(device_id)
    if not device_info:
        raise AssertionError(f"device_id: {device_id} not found")
    global_var = GlobalVarModel(device=device_info, agent=device_info.agent)
    return global_var


if __name__ == "__main__":
    DEVICE_ID = 102
    result = mock_global_var(DEVICE_ID)
    print(f"global_var: {result}")
