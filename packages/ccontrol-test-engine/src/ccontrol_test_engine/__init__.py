__version__ = "0.3.28"
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from ccontrol_common import (
    log,
    record_to_tvtest,
    record_to_tv_statistics,
    create_tool_statistics_decorator,
)

from ccontrol_ai_service import (
    AIService<PERSON>mp<PERSON>,
    OCR_FLAG_CASE_SENSITIVE,
    OCR_FLAG_CONTAINS_MATCH,
    OCR_FLAG_STRIP_SPACES,
    SYSTEM_PROMPT_JSON_RESPONSE,
    SYSTEM_PROMPT_VQA,
    SYSTEM_PROMPT_VQA_OBJECT,
    BoxLabelColorEnum,
    VQAModelEnum,
)

from ccontrol_test_engine.model.device import get_device_info
from ccontrol_test_engine.service.event.event_scanner import scan_directory
from ccontrol_test_engine.service.event.generator.generator import (
    gen_agent_rpc,
)
from ccontrol_test_engine.service.task.runner import TaskRunner, quick_init, quick_start
from ccontrol_test_engine.service.task.model import (
    GlobalVarModel,
    GlobalStore,
    TaskNodeModel,
    EventNodeModel,
    TestCaseNodeModel,
    TestSuiteNodeModel,
    NodeTypeEnum,
    NodeStatusEnum,
)
from ccontrol_test_engine.service.task.model.global_model import GlobalVarBaseModel
from ccontrol_test_engine.service.test_case_upload.python_bundler import PythonBundler
from ccontrol_test_engine.service.test_case_upload.upload import upload_event
from ccontrol_test_engine.model.event import CControlEventUpload


__all__ = [
    "get_device_info",
    "scan_directory",
    "TaskRunner",
    "GlobalVarModel",
    "GlobalStore",
    "GlobalVarBaseModel",
    "TaskNodeModel",
    "EventNodeModel",
    "TestCaseNodeModel",
    "TestSuiteNodeModel",
    "NodeTypeEnum",
    "NodeStatusEnum",
    "quick_init",
    "quick_start",
    "log",
    "gen_agent_rpc",
    # AIService
    "AIServiceImpl",
    "OCR_FLAG_CASE_SENSITIVE",
    "OCR_FLAG_CONTAINS_MATCH",
    "OCR_FLAG_STRIP_SPACES",
    "SYSTEM_PROMPT_JSON_RESPONSE",
    "SYSTEM_PROMPT_VQA",
    "SYSTEM_PROMPT_VQA_OBJECT",
    "BoxLabelColorEnum",
    "VQAModelEnum",
    # Analysis
    "record_to_tvtest",
    "record_to_tv_statistics",
    "create_tool_statistics_decorator",
    # bundle & upload
    "PythonBundler",
    "CControlEventUpload",
    "upload_event",
]
