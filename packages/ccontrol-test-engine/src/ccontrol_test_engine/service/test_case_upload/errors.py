from typing import Any, Optional
from ccontrol_common.core.errors import BaseError, NotFoundError, ServerError


class UploadError(ServerError):
    """上传错误"""

    def __init__(self, message: str, data: Optional[Any] = None):
        super().__init__(message=message, data=data)


class EventNotFoundError(NotFoundError):
    """事件不存在错误"""

    def __init__(self, event_name: str, data: Optional[Any] = None):
        super().__init__(message=f"事件 '{event_name}' 不存在", data=data)


class EventExistsError(BaseError):
    """事件已存在错误"""

    def __init__(self, event_name: str, data: Optional[Any] = None):
        super().__init__(code=409, message=f"事件 '{event_name}' 已存在", data=data)


class TestCaseNotFoundError(NotFoundError):
    """测试用例不存在错误"""

    def __init__(self, test_case_name: str, data: Optional[Any] = None):
        super().__init__(message=f"测试用例 '{test_case_name}' 不存在", data=data)


class TestCaseExistsError(BaseError):
    """测试用例已存在错误"""

    def __init__(self, test_case_name: str, data: Optional[Any] = None):
        super().__init__(code=409, message=f"测试用例 '{test_case_name}' 已存在", data=data)
