#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import json
from typing import Dict, Optional, Any

from pydantic import BaseModel, Field

from ccontrol_common.core import log
from ccontrol_common.core.errors import BaseError
from ccontrol_common.core.http_client import do_get, do_post
from ccontrol_common.config.conf import settings
from ccontrol_test_engine.service.test_case_upload.errors import (
    UploadError,
    EventExistsError,
    TestCaseExistsError,
    EventNotFoundError,
)
from ccontrol_test_engine.model.event import (
    CControlEventCreate,
    CControlEventUpdate,
    CControlEventUpload,
)
from ccontrol_test_engine.model.test_case import (
    CControlTestCaseCreate,
    CControlTestCaseUpdate,
    CControlTestCaseUpload,
)
from ccontrol_test_engine.service.test_case_upload.python_bundler import PythonBundler

# API Routes
API_EVENT_FIND_FIRST = "/api/model/cControlEvent/findFirst"
API_EVENT_UPSERT = "/api/model/cControlEvent/upsert"
API_TEST_CASE_FIND_FIRST = "/api/model/cControlTestCase/findFirst"
API_TEST_CASE_UPSERT = "/api/model/cControlTestCase/upsert"


class EventModel(BaseModel):
    """事件模型"""

    name: str = Field(..., description="事件名称")
    description: Optional[str] = Field(None, description="事件描述")
    entry_file: str = Field(..., description="入口文件路径")
    content: Dict[str, Any] = Field(default_factory=dict, description="事件内容")


def check_event_exists(method: str) -> bool:
    """检查事件是否存在

    Args:
        method (str): 事件名称

    Returns:
        bool: True表示存在，False表示不存在
    """
    try:
        url = settings.ZEUS_DOMAIN + API_EVENT_FIND_FIRST
        query = {"where": {"method": method}}
        resp = do_get(url=url, params={"q": json.dumps(query)})

        if not resp.is_success:
            log.warning(f"查询事件失败: {resp}")
            return False

        # 如果返回数据为空，表示事件不存在
        return resp.json().get("data") is not None
    except Exception as e:
        log.error(f"查询事件异常: {str(e)}")
        return False


def upload_event(event_info: CControlEventUpload, entry_file: str, overwrite: bool = False):
    """上传事件

    Args:
        event_info (CControlEventUpload): 事件信息
        entry_file (str): 入口文件路径
        overwrite (bool, optional): 是否覆盖已存在的事件. 默认为 False.

    Returns:
        Dict[str, Any]: 上传结果

    Raises:
        EventExistsError: 当事件已存在且不允许覆盖时
        UploadError: 当上传失败时
    """
    bundler = PythonBundler()
    bundled_code = bundler.bundle(entry_file)

    # 检查文件是否存在
    if not os.path.exists(entry_file):
        raise UploadError(f"入口文件 '{entry_file}' 不存在")

    # 1. 先判断是否已经存在该事件
    event_exists = check_event_exists(event_info.method)

    # 2. 如果事件已存在并且 overwrite 为 False，则抛出异常
    if event_exists and not overwrite:
        raise EventExistsError(event_info.method)

    # 3. 如果事件不存在或 overwrite 为 True，则 upsert
    try:
        # 构建请求数据
        create_data = CControlEventCreate.model_validate(
            {
                **event_info.model_dump_exclude_none(),
                "python_code": bundled_code,
            }
        )
        update_data = CControlEventUpdate.model_validate(
            {
                **event_info.model_dump_exclude_none(),
                "python_code": bundled_code,
            }
        )
        upsert_data = {
            "where": {"method": event_info.method},
            "create": create_data.model_dump_exclude_none(),
            "update": update_data.model_dump_exclude_none(),
        }

        # 发送请求
        url = settings.ZEUS_DOMAIN + API_EVENT_UPSERT
        resp = do_post(url=url, json_data=upsert_data)

        # 4. 返回 upsert 结果
        if not resp.is_success:
            raise UploadError(f"上传事件失败: {resp}, content: {resp.content}")

        log.success(f"事件 '{event_info.method}' 上传成功")
        return resp.json()
    except BaseError:
        # 已经是自定义错误，直接抛出
        raise
    except Exception as e:
        # 其他未知错误，包装为UploadError
        log.exception(f"上传事件出现异常: {str(e)}")
        raise UploadError(f"上传事件失败: {str(e)}") from e


def check_test_case_exists(name: str) -> bool:
    """检查测试用例是否存在

    Args:
        name (str): 测试用例名称

    Returns:
        bool: True表示存在，False表示不存在
    """
    try:
        url = settings.ZEUS_DOMAIN + API_TEST_CASE_FIND_FIRST
        query = {"where": {"name": name}}
        resp = do_get(url=url, params={"q": json.dumps(query)})

        if not resp.is_success:
            log.warning(f"查询测试用例失败: {resp}")
            return False

        # 如果返回数据为空，表示测试用例不存在
        return resp.json().get("data") is not None
    except Exception as e:
        log.error(f"查询测试用例异常: {str(e)}")
        return False


def upload_test_case(test_case_info: CControlTestCaseUpload, entry_file: str, overwrite: bool = False):
    """上传测试用例

    Args:
        test_case_info (CControlTestCaseUpload): 测试用例信息
        entry_file (str): 入口文件路径
        overwrite (bool, optional): 是否覆盖已存在的测试用例. 默认为 False.

    Returns:
        Dict[str, Any]: 上传结果

    Raises:
        TestCaseExistsError: 当测试用例已存在且不允许覆盖时
        EventNotFoundError: 当关联事件不存在时
        UploadError: 当上传失败时
    """
    bundler = PythonBundler()
    bundled_code = bundler.bundle(entry_file)

    # 检查文件是否存在
    if not os.path.exists(entry_file):
        raise UploadError(f"入口文件 '{entry_file}' 不存在")

    # 1. 检查关联的事件是否存在
    event_exists = check_event_exists(test_case_info.event_method)
    if not event_exists:
        raise EventNotFoundError(test_case_info.event_method)

    # 2. 判断是否已经存在该测试用例
    test_case_exists = check_test_case_exists(test_case_info.name)

    # 3. 如果测试用例已存在并且 overwrite 为 False，则抛出异常
    if test_case_exists and not overwrite:
        raise TestCaseExistsError(test_case_info.name)

    # 4. 如果测试用例不存在或 overwrite 为 True，则 upsert
    try:
        # 构建请求数据
        create_data = CControlTestCaseCreate.model_validate(
            {
                **test_case_info.model_dump(exclude_none=True),
                "python_code": bundled_code,
            }
        )
        update_data = CControlTestCaseUpdate.model_validate(
            {
                "id": test_case_info.name,  # 使用name作为唯一标识
                **test_case_info.model_dump(exclude_none=True),
                "python_code": bundled_code,
            }
        )
        upsert_data = {
            "where": {"name": test_case_info.name},
            "create": create_data.model_dump_json_exclude_none(),
            "update": update_data.model_dump_json_exclude_none(),
        }

        # 发送请求
        url = settings.ZEUS_DOMAIN + API_TEST_CASE_UPSERT
        resp = do_post(url=url, json_data=upsert_data)

        # 5. 返回 upsert 结果
        if not resp.is_success:
            raise UploadError(f"上传测试用例失败: {resp}")

        log.success(f"测试用例 '{test_case_info.name}' 上传成功")
        return resp.json()
    except BaseError:
        # 已经是自定义错误，直接抛出
        raise
    except Exception as e:
        # 其他未知错误，包装为UploadError
        log.exception(f"上传测试用例出现异常: {str(e)}")
        raise UploadError(f"上传测试用例失败: {str(e)}") from e


if __name__ == "__main__":
    event_info = CControlEventUpload.model_validate({"method": "test_upload"})
    result = upload_event(event_info, "vqa_speedtest.py", overwrite=True)
