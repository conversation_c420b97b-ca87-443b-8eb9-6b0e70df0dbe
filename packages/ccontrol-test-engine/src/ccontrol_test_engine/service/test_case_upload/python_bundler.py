#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import ast
import re
import importlib.util
import sysconfig

from ccontrol_common.core.log import log
from ccontrol_test_engine.service.test_case_upload.constants import BUILTIN_MODULES, PYTHON39_STDLIB_MODULES
from ccontrol_test_engine.service.task.code_security import check_code_security, remove_unsafe_code


class PythonBundler:
    """
    Python打包工具
    """

    def __init__(self, output_file=None, excluded_modules=None, entry_file=None, enable_security_check=True):
        self.output_file = output_file
        self.excluded_modules = excluded_modules or []
        self.processed_files = {}
        self.module_dependencies = {}  # 记录模块依赖关系
        self.imported_modules = set()  # 跟踪已导入的模块
        self.system_imports = {}  # 记录所有系统级导入
        self.from_imports = {}  # 记录所有from语句导入
        self.excluded_imports = {}  # 记录排除模块的导入语句
        self.excluded_from_imports = {}  # 记录排除模块的from导入语句
        self.entry_file = entry_file  # 入口文件名
        self.model_files = set()  # 存储识别出的模型文件
        self.import_depth = {}  # 记录每个模块的导入深度
        self.bundled_code = None  # 存储打包后的代码
        self.enable_security_check = enable_security_check  # 是否启用安全检查
        self.security_check_results = {}  # 存储安全检查结果

        # 预定义排除的模块（内置模块和第三方库）
        self.builtin_modules = BUILTIN_MODULES
        self.excluded_modules.extend(self.builtin_modules)

        # 项目根目录
        self.project_root = os.getcwd()

    def is_local_module(self, module_name, module_path):
        """判断是否为需要包含的本地模块

        使用排除法：先排除明确不需要的模块，剩下的就是本地模块
        """
        # 1. 排除在排除列表中的模块
        if self._is_excluded_module(module_name):
            return False

        # 2. 如果是标准库模块，则排除
        if self._is_standard_library_module(module_name):
            return False

        # 3. 如果是第三方库模块，则排除
        if self._is_third_party_module(module_path):
            return False

        # 4. 如果模块路径存在且是 .py 文件，则认为是本地模块
        if module_path and module_path.endswith(".py") and os.path.exists(module_path):
            return True

        return False

    def _is_excluded_module(self, module_name):
        """检查模块是否在排除列表中"""
        if module_name in self.excluded_modules:
            log.info(f"排除模块: {module_name} (在排除列表中)")
            return True

        for excluded in self.excluded_modules:
            if module_name.startswith(excluded + "."):
                log.info(f"排除模块: {module_name} (匹配排除项: {excluded})")
                return True

        return False

    def _is_third_party_module(self, module_path):
        """检查是否是第三方库模块"""
        if not module_path:
            return False

        # 检查是否在 site-packages 中
        if "site-packages" in module_path or "dist-packages" in module_path:
            return True

        # 检查是否在虚拟环境的 lib 目录中
        if "/lib/python" in module_path and "/site-packages" in module_path:
            return True

        # 检查是否在系统 Python 库目录中
        try:
            purelib = sysconfig.get_path("purelib")
            platlib = sysconfig.get_path("platlib")
            if purelib and os.path.abspath(module_path).startswith(os.path.abspath(purelib)):
                return True
            if platlib and os.path.abspath(module_path).startswith(os.path.abspath(platlib)):
                return True
        except (AttributeError, OSError):
            pass

        return False

    def resolve_module_path(self, module_name):
        """解析模块路径"""
        try:
            # 尝试直接导入模块
            spec = importlib.util.find_spec(module_name)
            if spec and spec.origin and spec.origin.endswith(".py"):
                return spec.origin
        except (ImportError, ValueError):
            pass

        # 尝试按部分导入路径解析
        parts = module_name.split(".")
        for i in range(len(parts)):
            prefix = ".".join(parts[: i + 1])
            try:
                spec = importlib.util.find_spec(prefix)
                if spec and spec.origin and spec.origin.endswith(".py"):
                    # 找到前缀模块，继续查找子模块
                    base_path = os.path.dirname(spec.origin)
                    remaining_parts = parts[i + 1 :]
                    if not remaining_parts:  # 如果没有剩余部分，直接返回
                        return spec.origin

                    # 构建剩余路径
                    rel_path = os.path.join(*remaining_parts)
                    py_path = os.path.join(base_path, f"{rel_path}.py")
                    if os.path.exists(py_path):
                        return py_path

                    # 尝试查找__init__.py
                    init_path = os.path.join(base_path, rel_path, "__init__.py")
                    if os.path.exists(init_path):
                        return init_path
            except (ImportError, ValueError):
                continue

        # 特殊处理项目内的路径
        for path in [".", "package_b", "package_b/children"]:
            full_path = os.path.join(self.project_root, path, module_name.replace(".", "/") + ".py")
            if os.path.exists(full_path):
                return full_path

            # 尝试目录加__init__.py
            init_path = os.path.join(self.project_root, path, module_name.replace(".", "/"), "__init__.py")
            if os.path.exists(init_path):
                return init_path

        return None

    def collect_imports(self, file_path, source_code):
        """收集给定源代码中的导入语句，并详细记录导入类型和导入的对象"""
        imports = []
        try:
            tree = ast.parse(source_code)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for name in node.names:
                        imports.append(name.name)
                        # 检查是否在排除列表中
                        if self._is_excluded_module(name.name):
                            self.excluded_imports[name.name] = name.asname if name.asname else name.name
                        # 检查是否为标准库模块
                        elif self._is_standard_library_module(name.name):
                            self.system_imports[name.name] = name.asname if name.asname else name.name
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
                        # 检查是否在排除列表中
                        if self._is_excluded_module(node.module):
                            for alias in node.names:
                                key = f"{node.module}:{alias.name}"
                                as_name = alias.asname if alias.asname else alias.name
                                self.excluded_from_imports[key] = (
                                    node.module,
                                    alias.name,
                                    as_name,
                                )
                        # 检查是否为标准库模块
                        elif self._is_standard_library_module(node.module):
                            for alias in node.names:
                                key = f"{node.module}:{alias.name}"
                                as_name = alias.asname if alias.asname else alias.name
                                self.from_imports[key] = (
                                    node.module,
                                    alias.name,
                                    as_name,
                                )
        except SyntaxError:
            log.warning(f"警告: 无法解析 {file_path} 中的导入")
        return imports

    def process_file(self, file_path):
        """处理单个文件及其依赖"""
        if file_path in self.processed_files:
            # 如果已经处理过这个文件，直接返回
            return

        log.info(f"处理文件: {file_path}")

        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 添加到处理列表
        self.processed_files[file_path] = content

        # 解析导入语句
        imports = self.collect_imports(file_path, content)

        # 优先处理数据模型依赖
        model_imports = [imp for imp in imports if "model" in imp.lower()]
        other_imports = [imp for imp in imports if "model" not in imp.lower()]

        # 先处理模型导入
        for module_name in model_imports:
            if module_name not in self.imported_modules:
                module_path = self.resolve_module_path(module_name)
                if module_path and self.is_local_module(module_name, module_path):
                    self.imported_modules.add(module_name)  # 只有本地模块才添加到 imported_modules
                    self.process_file(module_path)
                    # 记录依赖关系：当前文件依赖于被导入的模块
                    self.module_dependencies.setdefault(file_path, []).append(module_path)

        # 再处理其他导入
        for module_name in other_imports:
            if module_name not in self.imported_modules:
                module_path = self.resolve_module_path(module_name)
                if module_path and self.is_local_module(module_name, module_path):
                    self.imported_modules.add(module_name)  # 只有本地模块才添加到 imported_modules
                    self.process_file(module_path)
                    # 记录依赖关系：当前文件依赖于被导入的模块
                    self.module_dependencies.setdefault(file_path, []).append(module_path)

    def _is_single_line_import(self, line):
        """检查是否是单行导入语句（完整的导入语句）"""
        line = line.strip()

        # 检查是否是 import 语句
        if re.match(r"^import\s+", line):
            return True

        # 检查是否是 from...import 语句，且没有未闭合的括号
        if re.match(r"^from\s+", line) and "import" in line:
            # 检查是否有未闭合的括号
            open_paren = line.count("(")
            close_paren = line.count(")")
            return open_paren == close_paren

        return False

    def _is_multiline_import_start(self, line):
        """检查是否是多行导入语句的开始"""
        line = line.strip()

        # 检查是否是 from...import 语句，且有未闭合的括号
        if re.match(r"^from\s+", line) and "import" in line:
            # 检查是否有未闭合的括号
            open_paren = line.count("(")
            close_paren = line.count(")")
            return open_paren > close_paren

        return False

    def _remove_redundant_imports(self, code, remove_local_imports=False):
        """移除冗余的导入语句，支持多行导入语句

        Args:
            code: 源代码字符串
            remove_local_imports: 是否移除本地模块的导入语句
        """
        lines = code.split("\n")
        filtered_lines = []
        i = 0

        while i < len(lines):
            line = lines[i]

            # 检查是否是单行导入语句
            if self._is_single_line_import(line):
                if self._is_excluded_import(line, remove_local_imports):
                    # 跳过这个导入语句
                    i += 1
                    continue
                else:
                    # 保留这个导入语句
                    filtered_lines.append(line)
                    i += 1
                    continue

            # 检查是否是多行导入语句的开始
            if self._is_multiline_import_start(line):
                # 收集完整的多行导入语句
                import_lines = []
                j = i
                paren_count = 0
                in_string = False
                string_char = None

                while j < len(lines):
                    current_line = lines[j]
                    import_lines.append(current_line)

                    # 分析括号平衡
                    for char in current_line:
                        if char in ['"', "'"] and (j == i or not in_string):
                            if not in_string:
                                in_string = True
                                string_char = char
                            elif char == string_char:
                                in_string = False
                                string_char = None
                        elif not in_string:
                            if char == "(":
                                paren_count += 1
                            elif char == ")":
                                paren_count -= 1
                                if paren_count == 0:
                                    break

                    if paren_count == 0:
                        break
                    j += 1

                # 检查是否需要排除这个多行导入
                first_line = import_lines[0]
                if self._is_excluded_import(first_line, remove_local_imports):
                    # 跳过整个多行导入语句
                    i = j + 1
                    continue
                else:
                    # 保留整个多行导入语句
                    filtered_lines.extend(import_lines)
                    i = j + 1
                    continue

            # 不是导入语句，直接添加
            filtered_lines.append(line)
            i += 1

        return "\n".join(filtered_lines)

    def _is_excluded_import(self, line, remove_local_imports=False):
        """检查是否为需要排除的导入语句

        Args:
            line: 代码行
            remove_local_imports: 是否移除本地模块的导入语句
        """
        import_match = re.match(r"^import\s+([\w\.]+)(\s+as\s+\w+)?.*$", line.strip())
        from_import_match = re.match(r"^from\s+([\w\.]+)\s+import.*$", line.strip())

        if import_match:
            module_name = import_match.group(1)
            return self._should_exclude_module(module_name, remove_local_imports)

        if from_import_match:
            module_name = from_import_match.group(1)
            return self._should_exclude_module(module_name, remove_local_imports)

        return False

    def _should_exclude_module(self, module_name, remove_local_imports=False):
        """检查模块是否在排除列表中或匹配排除模式

        Args:
            module_name: 模块名称
            remove_local_imports: 是否移除本地模块的导入语句
        """
        # 检查是否在排除列表中
        if module_name in self.excluded_modules:
            return True

        for excluded in self.excluded_modules:
            if module_name.startswith(excluded + ".") or module_name == excluded:
                log.info(f"排除导入: {module_name}")
                return True

        # 如果需要移除本地模块导入，检查是否为已处理的本地模块
        if remove_local_imports:
            # 检查是否在已导入的模块列表中
            if module_name in self.imported_modules:
                log.info(f"移除本地模块导入: {module_name}")
                return True

            # 检查是否为已处理文件的模块路径
            for processed_file in self.processed_files.keys():
                # 从文件路径生成模块名
                module_path = os.path.relpath(processed_file, self.project_root)
                if module_path.endswith(".py"):
                    module_path = module_path[:-3]  # 移除 .py 后缀
                file_module_name = module_path.replace(os.sep, ".")

                if module_name == file_module_name or module_name.startswith(file_module_name + "."):
                    log.info(f"移除本地文件模块导入: {module_name} (对应文件: {processed_file})")
                    return True

            # 使用更可靠的方法判断项目模块
            if not self._is_standard_library_module(module_name):
                if self._is_project_module(module_name):
                    log.info(f"移除项目模块导入: {module_name}")
                    return True

        return False

    def _is_project_module(self, module_name):
        """检查模块是否为项目模块

        使用更可靠的方法判断模块是否属于当前项目：
        1. 尝试解析模块路径
        2. 检查路径是否在项目根目录内
        3. 检查模块是否在已导入的本地模块列表中
        4. 检查模块是否对应已处理的文件

        Args:
            module_name: 模块名称

        Returns:
            bool: 如果是项目模块则返回True，否则返回False
        """
        # 1. 检查是否在已导入的本地模块列表中
        if module_name in self.imported_modules:
            return True

        # 2. 尝试解析模块路径
        try:
            module_path = self.resolve_module_path(module_name)
            if module_path:
                # 获取模块的绝对路径
                abs_module_path = os.path.abspath(module_path)
                abs_project_root = os.path.abspath(self.project_root)

                # 检查模块是否在项目根目录内
                if abs_module_path.startswith(abs_project_root):
                    return True
        except Exception:
            pass

        # 3. 检查模块是否对应已处理的文件
        for processed_file in self.processed_files.keys():
            # 从文件路径生成模块名
            try:
                module_path = os.path.relpath(processed_file, self.project_root)
                if module_path.endswith(".py"):
                    module_path = module_path[:-3]  # 移除 .py 后缀
                file_module_name = module_path.replace(os.sep, ".")

                if module_name == file_module_name or module_name.startswith(file_module_name + "."):
                    return True
            except Exception:
                continue

        # 4. 使用现有的is_local_module方法作为最后的检查
        try:
            resolved_path = self.resolve_module_path(module_name)
            if resolved_path and self.is_local_module(module_name, resolved_path):
                return True
        except Exception:
            pass

        return False

    def _is_standard_library_module(self, module_name):
        """检查是否为标准库模块"""
        # 获取模块的根名称
        root_module = module_name.split(".")[0]

        # 使用缓存避免重复计算
        if not hasattr(self, "_stdlib_cache"):
            self._stdlib_cache = {}

        if root_module in self._stdlib_cache:
            return self._stdlib_cache[root_module]

        result = self._check_stdlib_module(root_module)
        self._stdlib_cache[root_module] = result
        return result

    def _check_stdlib_module(self, module_name):
        """检查模块是否为标准库模块的核心逻辑"""
        # 方法1: 使用 sys.stdlib_module_names (Python 3.10+)
        if hasattr(sys, "stdlib_module_names"):
            # 使用 getattr 来避免类型检查器警告
            stdlib_names = getattr(sys, "stdlib_module_names", set())
            return module_name in stdlib_names

        # 方法2: 使用 importlib.util.find_spec 和路径检查 (Python 3.9 兼容)
        try:
            import site

            spec = importlib.util.find_spec(module_name)
            if spec is None:
                return False

            # 如果模块没有 origin，检查是否为内置模块还是 namespace package
            if spec.origin is None:
                # 如果有 submodule_search_locations，通常是 namespace package，不是标准库
                if spec.submodule_search_locations is not None:
                    return False
                # 否则可能是内置模块
                return True

            # 如果是 built-in 模块
            if spec.origin == "built-in":
                return True

            # 检查模块路径是否在标准库路径中
            module_path = spec.origin
            if module_path:
                # 规范化路径
                module_path = os.path.abspath(module_path)

                # 获取 site-packages 路径
                site_packages = []
                try:
                    site_packages.extend(site.getsitepackages())
                except (AttributeError, OSError):
                    pass

                # 获取用户 site-packages 路径
                try:
                    if hasattr(site, "getusersitepackages"):
                        user_site = site.getusersitepackages()
                        if user_site:
                            site_packages.append(user_site)
                except (AttributeError, OSError):
                    pass

                # 如果模块在 site-packages 中，则不是标准库
                for site_path in site_packages:
                    if site_path:
                        site_path = os.path.abspath(site_path)
                        if module_path.startswith(site_path):
                            return False

                # 检查是否在 Python 标准库路径中
                try:
                    # 方法 2a: 使用 sysconfig.get_path('stdlib')
                    stdlib_path = sysconfig.get_path("stdlib")
                    if stdlib_path:
                        stdlib_path = os.path.abspath(stdlib_path)
                        if module_path.startswith(stdlib_path):
                            return True
                except (AttributeError, OSError):
                    pass

                # 方法 2b: 使用 sysconfig.get_path('platstdlib')
                try:
                    platstdlib_path = sysconfig.get_path("platstdlib")
                    if platstdlib_path:
                        platstdlib_path = os.path.abspath(platstdlib_path)
                        if module_path.startswith(platstdlib_path):
                            return True
                except (AttributeError, OSError):
                    pass

                # 方法 2c: 检查是否在 Python 可执行文件的 lib 目录中
                try:
                    python_home = os.path.dirname(os.path.dirname(sys.executable))
                    lib_paths = [
                        os.path.join(python_home, "lib"),
                        os.path.join(python_home, "Lib"),  # Windows
                        os.path.join(python_home, "lib", f"python{sys.version_info.major}.{sys.version_info.minor}"),
                    ]

                    for lib_path in lib_paths:
                        if os.path.exists(lib_path):
                            lib_path = os.path.abspath(lib_path)
                            if module_path.startswith(lib_path):
                                return True
                except (AttributeError, OSError):
                    pass

                # 方法 2d: 检查是否在 sys.prefix 下的 lib 目录中
                try:
                    prefix_lib_paths = [
                        os.path.join(sys.prefix, "lib"),
                        os.path.join(sys.prefix, "Lib"),  # Windows
                        os.path.join(sys.prefix, "lib", f"python{sys.version_info.major}.{sys.version_info.minor}"),
                    ]

                    for lib_path in prefix_lib_paths:
                        if os.path.exists(lib_path):
                            lib_path = os.path.abspath(lib_path)
                            if module_path.startswith(lib_path):
                                return True
                except (AttributeError, OSError):
                    pass

        except (ImportError, AttributeError, OSError):
            pass

        # 方法3: 使用更完善的 Python 3.9 兼容的标准库模块列表
        return module_name in PYTHON39_STDLIB_MODULES

    def _is_import_statement(self, line):
        """检查是否为导入语句"""
        return bool(re.match(r"^import\s+([\w\.]+)(\s+as\s+\w+)?.*$", line.strip()) or re.match(r"^from\s+([\w\.]+)\s+import.*$", line.strip()))

    def sort_modules_by_dependency(self):
        """根据依赖关系和模型类分析结果排序模块，确保依赖在前"""
        result = []
        visited = set()

        # 计算每个模块的导入深度
        self.calculate_import_depth()

        # 识别所有模型文件
        self.identify_model_files()

        def dfs_visit(module, depth=0):
            """深度优先遍历访问模块及其依赖，确保依赖在前"""
            if module in visited:
                return
            visited.add(module)

            # 先访问当前模块的所有依赖
            for dependency in self.module_dependencies.get(module, []):
                dfs_visit(dependency, depth + 1)

            # 所有依赖都已访问，现在添加当前模块
            result.append(module)
            # 更新导入深度（取最大值）
            self.import_depth[module] = max(self.import_depth.get(module, 0), depth)

        # 先处理模型文件，按照导入深度从深到浅排序
        model_modules_with_depth = [(m, self.import_depth.get(m, 0)) for m in self.model_files]
        model_modules_with_depth.sort(key=lambda x: x[1], reverse=True)  # 深度更高的先处理

        for module, _ in model_modules_with_depth:
            dfs_visit(module)

        # 再处理其他文件，也按导入深度排序
        other_modules = []
        for module in self.processed_files.keys():
            if module not in visited:
                other_modules.append((module, self.import_depth.get(module, 0)))

        other_modules.sort(key=lambda x: x[1], reverse=True)  # 深度更高的先处理

        for module, _ in other_modules:
            if module not in visited:
                dfs_visit(module)

        return result

    def calculate_import_depth(self):
        """计算每个模块的导入深度（包含多少层导入）"""

        def get_depth(module, visited=None):
            if visited is None:
                visited = set()

            if module in visited:  # 避免循环依赖
                return 0

            visited.add(module)

            if not self.module_dependencies.get(module):
                return 0

            # 计算最大深度（基于当前模块的依赖）
            max_depth = 0
            for dependency in self.module_dependencies.get(module, []):
                depth = get_depth(dependency, visited.copy()) + 1
                max_depth = max(max_depth, depth)

            self.import_depth[module] = max_depth
            return max_depth

        # 为每个模块计算深度
        for module in self.processed_files.keys():
            if module not in self.import_depth:
                get_depth(module)

    def identify_model_files(self):
        """使用AST分析识别所有模型文件"""
        for file_path, content in self.processed_files.items():
            if self.analyze_models_ast(file_path, content):
                self.model_files.add(file_path)
                log.info(f"已识别模型文件: {file_path}")

        # 如果AST分析没有识别到模型文件，使用基于文件名的简单启发式方法作为备份
        if len(self.model_files) == 0:
            for file_path in self.processed_files.keys():
                if "model" in file_path.lower() or "schema" in file_path.lower():
                    self.model_files.add(file_path)
                    log.info(f"通过文件名识别模型文件: {file_path}")

        return self.model_files

    def _apply_security_check(self, code: str, file_path: str) -> str:
        """对代码应用安全检查，移除不安全代码

        Args:
            code: 原始代码
            file_path: 文件路径，用于日志记录

        Returns:
            str: 处理后的安全代码
        """
        if not self.enable_security_check:
            return code

        try:
            # 检查代码安全性
            security_result = check_code_security(code)

            # 记录检查结果
            self.security_check_results[file_path] = {
                "has_forbidden_errors": security_result.has_forbidden_errors,
                "has_warnings": security_result.has_warnings,
                "forbidden_errors": security_result.forbidden_errors,
                "warning_errors": security_result.warning_errors,
            }

            # 如果有完全禁止的错误，记录警告但不处理（保持原代码）
            if security_result.has_forbidden_errors:
                log.warning(f"⚠️  文件 {file_path} 包含禁止的代码:")
                for error in security_result.forbidden_errors:
                    log.warning(f"   - {error}")
                log.warning("   保持原代码，请手动检查")
                return code

            # 如果有警告，尝试移除不安全代码
            if security_result.has_warnings:
                log.info(f"🔧 文件 {file_path} 包含警告代码，尝试自动移除:")
                for warning in security_result.warning_errors:
                    log.info(f"   - {warning}")

                filtered_code = remove_unsafe_code(code)
                if filtered_code is not None:
                    log.info("   ✅ 成功移除不安全代码")
                    return filtered_code
                else:
                    log.info("   ❌ 无法自动移除不安全代码，保持原代码")
                    return code

            # 代码安全，无需处理
            return code

        except Exception as e:
            print(f"⚠️  安全检查失败 {file_path}: {str(e)}")
            print("   保持原代码")
            return code

    def generate_bundle(self):
        """生成打包后的文件或代码字符串"""
        output = [
            "#!/usr/bin/env python",
            "# -*- coding: utf-8 -*-",
            "# Generated by SimplePythonBundler",
        ]

        # 如果启用了安全检查，添加相关注释
        if self.enable_security_check:
            output.append("# Security check enabled - unsafe code has been filtered")

        output.extend(
            [
                "",
                "# 系统导入",
            ]
        )

        # 添加动态收集到的系统导入
        # 直接导入语句 (import xxx)
        direct_imports = sorted(self.system_imports.keys())
        for module_name in direct_imports:
            as_name = self.system_imports[module_name]
            if module_name == as_name:
                output.append(f"import {module_name}")
            else:
                output.append(f"import {module_name} as {as_name}")

        # 添加排除的直接导入语句，这些模块不会被解析但保留导入语句
        excluded_direct_imports = sorted(self.excluded_imports.keys())
        for module_name in excluded_direct_imports:
            as_name = self.excluded_imports[module_name]
            if module_name == as_name:
                output.append(f"import {module_name}")
            else:
                output.append(f"import {module_name} as {as_name}")

        # From导入语句 (from xxx import yyy)
        from_modules = {}
        # 存储原始别名信息，避免合并导入时丢失别名
        alias_info = {}

        for key, (module, name, as_name) in self.from_imports.items():
            if module not in from_modules:
                from_modules[module] = []
                alias_info[module] = {}

            # 保存别名映射
            if name != as_name:
                alias_info[module][name] = as_name

            from_modules[module].append(name)

        # 添加排除的 from 导入语句
        excluded_from_modules = {}
        # 为排除的模块也保存别名信息
        excluded_alias_info = {}

        for key, (module, name, as_name) in self.excluded_from_imports.items():
            if module not in excluded_from_modules:
                excluded_from_modules[module] = []
                excluded_alias_info[module] = {}

            # 保存别名映射
            if name != as_name:
                excluded_alias_info[module][name] = as_name

            excluded_from_modules[module].append(name)

        # 输出常规 from 导入，保持别名
        for module in sorted(from_modules.keys()):
            items = []
            for name in sorted(from_modules[module]):
                if name in alias_info[module]:
                    items.append(f"{name} as {alias_info[module][name]}")
                else:
                    items.append(name)
            names = ", ".join(items)
            output.append(f"from {module} import {names}")

        # 输出排除模块的 from 导入，保持别名
        for module in sorted(excluded_from_modules.keys()):
            items = []
            for name in sorted(excluded_from_modules[module]):
                if name in excluded_alias_info[module]:
                    items.append(f"{name} as {excluded_alias_info[module][name]}")
                else:
                    items.append(name)
            names = ", ".join(items)
            output.append(f"from {module} import {names}")

        # 如果某些关键导入没有被自动收集到，确保添加默认的基础导入
        essential_imports = {
            "import sys": False,
            "import os": False,
            "from typing import Any, Optional": False,
            "from dataclasses import dataclass": False,
        }

        for line in output:
            for essential in essential_imports:
                if essential in line:
                    essential_imports[essential] = True

        for essential, included in essential_imports.items():
            if not included:
                output.append(essential)

        output.append("")
        output.append("# Begin bundled modules")
        output.append("")

        # 按依赖关系排序模块
        sorted_modules = self.sort_modules_by_dependency()

        # 分类文件
        model_files = list(self.model_files)  # 使用 identify_model_files 已识别的模型
        normal_files = []
        main_file = None

        # 确定入口文件的文件名（如果未指定，则使用默认值）
        entry_filename = os.path.basename(self.entry_file) if self.entry_file else "vqa_speedtest.py"

        for file_path in sorted_modules:
            if file_path in model_files:
                # 已经在模型文件列表中
                pass
            elif os.path.basename(file_path) == entry_filename:  # 检查是否为入口文件
                main_file = file_path
            else:
                normal_files.append(file_path)

        # 添加模型文件
        for file_path in model_files:
            output.append(f"# Module: {file_path}")
            processed_code = self._remove_redundant_imports(self.processed_files[file_path], remove_local_imports=True)
            # 应用安全检查
            secure_code = self._apply_security_check(processed_code, file_path)
            output.append(secure_code)
            output.append("")

        # 添加普通模块
        for file_path in normal_files:
            output.append(f"# Module: {file_path}")
            processed_code = self._remove_redundant_imports(self.processed_files[file_path], remove_local_imports=True)
            # 应用安全检查
            secure_code = self._apply_security_check(processed_code, file_path)
            output.append(secure_code)
            output.append("")

        # 最后添加主文件
        if main_file:
            output.append(f"# Module: {main_file}")
            # 对主文件，移除本地模块的导入语句
            processed_code = self._remove_redundant_imports(self.processed_files[main_file], remove_local_imports=True)

            # 应用安全检查
            secure_code = self._apply_security_check(processed_code, main_file)
            output.append(secure_code)
            output.append("")

        output.append("# End bundled modules")

        # 如果启用了安全检查，添加安全检查摘要
        if self.enable_security_check:
            self._add_security_summary(output)

        # 将结果代码保存到实例变量
        bundled_code = "\n".join(output)
        self.bundled_code = bundled_code

        # 如果指定了输出文件，则写入文件
        if self.output_file:
            with open(self.output_file, "w", encoding="utf-8") as f:
                f.write(bundled_code)
            print(f"打包完成: {self.output_file}")

        return bundled_code

    def _add_security_summary(self, output: list):
        """添加安全检查摘要到输出"""
        if not self.security_check_results:
            return

        output.append("")
        output.append("# Security Check Summary:")

        total_files = len(self.security_check_results)
        files_with_issues = 0
        files_with_warnings = 0

        for file_path, result in self.security_check_results.items():
            if result["has_forbidden_errors"] or result["has_warnings"]:
                files_with_issues += 1
            if result["has_warnings"]:
                files_with_warnings += 1

        output.append(f"# - Total files checked: {total_files}")
        output.append(f"# - Files with security issues: {files_with_issues}")
        output.append(f"# - Files with warnings (auto-fixed): {files_with_warnings}")

        if files_with_issues > 0:
            output.append("# - See console output for detailed security check results")

    def get_security_check_summary(self) -> dict:
        """获取安全检查摘要

        Returns:
            dict: 安全检查摘要信息
        """
        if not self.enable_security_check or not self.security_check_results:
            return {"enabled": self.enable_security_check, "total_files": 0, "files_with_issues": 0, "files_with_warnings": 0, "files_with_forbidden": 0}

        total_files = len(self.security_check_results)
        files_with_issues = 0
        files_with_warnings = 0
        files_with_forbidden = 0

        for result in self.security_check_results.values():
            if result["has_forbidden_errors"] or result["has_warnings"]:
                files_with_issues += 1
            if result["has_warnings"]:
                files_with_warnings += 1
            if result["has_forbidden_errors"]:
                files_with_forbidden += 1

        return {"enabled": True, "total_files": total_files, "files_with_issues": files_with_issues, "files_with_warnings": files_with_warnings, "files_with_forbidden": files_with_forbidden, "details": self.security_check_results}

    def bundle(self, entry_file):
        """执行打包流程，返回打包后的代码字符串或文件路径"""
        # 处理入口文件
        entry_path = os.path.join(self.project_root, entry_file)
        if not os.path.exists(entry_path):
            print(f"错误: 入口文件不存在: {entry_path}")
            sys.exit(1)

        # 将 entry_file 文件所在目录添加到 sys.path，以支持相对路径导入
        self._add_entry_path_to_syspath(entry_path)

        self.process_file(entry_path)

        # 如果指定了output_file，生成完整路径
        if self.output_file:
            self.output_file = os.path.join(self.project_root, self.output_file)

        # 生成打包结果
        bundled_code = self.generate_bundle()

        # 返回结果：如果设置了output_file则返回文件路径，否则返回代码字符串
        if self.output_file:
            return self.output_file
        else:
            return bundled_code

    def _add_entry_path_to_syspath(self, entry_path):
        """将 entry_file 文件所在目录添加到 sys.path，以支持相对路径导入

        Args:
            entry_path: 入口文件的完整路径
        """
        # 获取入口文件所在的目录
        entry_dir = os.path.dirname(os.path.abspath(entry_path))

        # 检查该目录是否已经在 sys.path 中
        if entry_dir not in sys.path:
            sys.path.insert(0, entry_dir)  # 插入到开头，确保优先级
            print(f"已将入口文件目录添加到 sys.path: {entry_dir}")
        else:
            print(f"入口文件目录已在 sys.path 中: {entry_dir}")

    def is_model_class(self, node, source_code):
        """
        通过 AST 分析判断一个类是否为数据模型类
        检查是否继承自 pydantic.BaseModel 或使用 @dataclass 装饰器
        """
        if not isinstance(node, ast.ClassDef):
            return False

        # 检查是否有 dataclass 装饰器
        for decorator in node.decorator_list:
            decorator_id = None
            if isinstance(decorator, ast.Name):
                decorator_id = decorator.id
            elif isinstance(decorator, ast.Attribute) and isinstance(decorator.value, ast.Name):
                decorator_id = f"{decorator.value.id}.{decorator.attr}"

            if decorator_id in ["dataclass", "dataclasses.dataclass"]:
                return True

        # 检查是否继承自 pydantic.BaseModel
        for base in node.bases:
            if isinstance(base, ast.Name) and base.id == "BaseModel":
                return True
            elif isinstance(base, ast.Attribute):
                if isinstance(base.value, ast.Name):
                    if f"{base.value.id}.{base.attr}" == "pydantic.BaseModel":
                        return True

        # 检查类的内容特征，如是否包含大量类变量定义
        has_annotations = False
        field_count = 0

        for item in node.body:
            if isinstance(item, ast.AnnAssign):  # 带类型注解的变量
                field_count += 1
                has_annotations = True

        # 如果类有多个带注解的字段，可能是数据类
        return has_annotations and field_count >= 3

    def analyze_models_ast(self, file_path, source_code):
        """
        使用 AST 分析源代码中的模型类
        """
        try:
            tree = ast.parse(source_code)
            has_model = False

            # 检查导入语句，查找与模型相关的导入
            has_pydantic_import = any(
                isinstance(node, ast.Import) and any(name.name == "pydantic" for name in node.names) or isinstance(node, ast.ImportFrom) and node.module == "pydantic" and any(alias.name == "BaseModel" for alias in node.names) for node in ast.walk(tree)
            )

            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef) and self.is_model_class(node, source_code):
                    has_model = True
                    log.info(f"发现模型类: {node.name} in {file_path}")
                    break

            return has_model or (has_pydantic_import and "Model" in file_path)
        except SyntaxError:
            log.warning(f"警告: 无法解析 {file_path} 的 AST")
            # 退回到基于文件名的简单检测
            return "model" in file_path.lower() or "schema" in file_path.lower()


if __name__ == "__main__":
    # 临时调试用
    import sys

    # entry_file = "/Users/<USER>/Project/ccontrol/ccontrol-test-case/testsuites/aosp/aosp_system/系统测试1.py"
    entry_file = "/Users/<USER>/Project/ccontrol/ccontrol-test-case/testsuites/aosp/aosp_system/run_AOSP_DVB_系统测试.py"
    output_file = os.path.join(os.getcwd(), "combined.py")

    # 临时模拟其它仓库的运行路径
    repo_path = "/Users/<USER>/Project/ccontrol/ccontrol-test-case"
    sys.path.append(repo_path)

    # TODO: 看下要不要做 compiler 优化，移除 unused 代码

    log.info("🚀 启动Python代码打包工具（启用安全检查）")
    bundler = PythonBundler(
        output_file=output_file,
        entry_file=entry_file,
        enable_security_check=True,  # 显式启用安全检查
    )
    bundled_code = bundler.bundle(entry_file)
    log.info(f"生成的代码长度: {len(bundled_code)}")

    # 输出安全检查摘要
    security_summary = bundler.get_security_check_summary()
    log.info("\n📊 安全检查摘要:")
    log.info(f"   - 安全检查: {'启用' if security_summary['enabled'] else '禁用'}")
    log.info(f"   - 检查文件总数: {security_summary['total_files']}")
    log.info(f"   - 有安全问题的文件: {security_summary['files_with_issues']}")
    log.info(f"   - 有警告的文件(已自动修复): {security_summary['files_with_warnings']}")
    log.info(f"   - 有禁止代码的文件: {security_summary['files_with_forbidden']}")

    if security_summary["files_with_issues"] > 0:
        log.warning("\n⚠️  详细安全检查结果请查看上方的控制台输出")

    log.info(f"🚀 输出文件路径: {output_file}")
