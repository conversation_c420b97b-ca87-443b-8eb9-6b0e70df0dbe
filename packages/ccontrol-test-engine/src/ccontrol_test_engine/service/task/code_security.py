import ast
import importlib
from typing import List, Optional, Union, Dict, Set, Any, Tuple, Mapping

from ccontrol_common.core.log import log
from ccontrol_common.core.errors import (
    ForbiddenImportError,
    RestrictedImportError,
    ForbiddenFunctionError,
    RestrictedFunctionError,
    ForbiddenMethodError,
    SyntaxCheckError,
    CodeSecurityLevel,
)


def get_forbidden_checks() -> Mapping[str, Dict[str, Union[str, CodeSecurityLevel]]]:
    """获取禁止的函数和方法列表及其安全级别"""
    return {
        "driver.close": {
            "module": "ccontrol_test_engine.service.driver.ccontrol_driver",
            "level": CodeSecurityLevel.WARNING,
        },
        "quick_init": {
            "module": "ccontrol_test_engine",
            "level": CodeSecurityLevel.WARNING,
        },
    }


class CodeSecurityConfig:
    """代码安全配置"""

    # 完全禁止的模块
    FORBIDDEN_IMPORTS = {
        "subprocess",
        "shutil",
        "socket",
        "ftplib",
        "telnetlib",
        "smtplib",
        "poplib",
        "imaplib",
    }

    # 部分允许的模块及其允许的属性/方法
    RESTRICTED_IMPORTS: Dict[str, Set[str]] = {
        "os": {"path", "name", "environ", "getenv", "getcwd", "listdir", "walk"},
        "os.path": {
            "join",
            "dirname",
            "basename",
            "exists",
            "isfile",
            "isdir",
            "abspath",
            "relpath",
        },
        "sys": {
            "path",
            "platform",
            "version",
            "version_info",
            "implementation",
            "getdefaultencoding",
        },
        "pathlib": {"Path"},
        "requests": {"get", "post", "put", "delete", "head", "options", "patch"},
        "urllib": {"parse", "request"},
        "http": {"client"},
    }

    # 不允许使用的内置函数及其安全级别
    FORBIDDEN_BUILTINS = {
        "eval": CodeSecurityLevel.FORBIDDEN,
        "exec": CodeSecurityLevel.FORBIDDEN,
        "compile": CodeSecurityLevel.FORBIDDEN,
        "__import__": CodeSecurityLevel.FORBIDDEN,
        "exit": CodeSecurityLevel.FORBIDDEN,
        "quit": CodeSecurityLevel.FORBIDDEN,
    }

    # 部分允许的内置函数及其使用条件
    RESTRICTED_BUILTINS = {
        "open": {"mode": {"r", "rt"}},  # 只允许只读模式
        "input": {},  # 暂时完全禁用
        "globals": {},  # 暂时完全禁用
        "locals": {},  # 暂时完全禁用
    }


class SecurityCheckResult:
    """安全检查结果"""

    def __init__(self):
        self.forbidden_errors: List[str] = []  # 完全禁止的错误
        self.warning_errors: List[str] = []  # 警告性错误

    @property
    def has_forbidden_errors(self) -> bool:
        """是否有完全禁止的错误"""
        return len(self.forbidden_errors) > 0

    @property
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warning_errors) > 0


class CodeSecurityChecker(ast.NodeVisitor):
    """代码安全检查器"""

    def __init__(self):
        self.result = SecurityCheckResult()
        self.imported_modules = {}  # 存储已导入的模块
        self._forbidden_checks = None  # 延迟初始化
        self.config = CodeSecurityConfig()

    @property
    def forbidden_checks(
        self,
    ) -> Mapping[str, Dict[str, Union[str, CodeSecurityLevel]]]:
        """延迟获取禁止的函数和方法列表"""
        if self._forbidden_checks is None:
            self._forbidden_checks = get_forbidden_checks()
        return self._forbidden_checks

    def _format_allowed_modes(self, modes: Set[str]) -> str:
        """格式化允许的模式列表"""
        return ", ".join(sorted(modes))

    def _import_module(self, module_name: str) -> Optional[object]:
        """安全地导入模块"""
        try:
            return importlib.import_module(module_name)
        except (ImportError, ValueError):
            return None

    def _check_forbidden_function(
        self, func: Any
    ) -> Optional[Tuple[str, CodeSecurityLevel]]:
        """检查是否是禁止的函数或方法"""
        for name, check_info in self.forbidden_checks.items():
            if (
                hasattr(func, "__module__")
                and func.__module__ == check_info["module"]
                and isinstance(check_info["level"], CodeSecurityLevel)
            ):
                return name, check_info["level"]
        return None

    def visit_Call(self, node: ast.Call):
        """检查函数调用"""
        if isinstance(node.func, ast.Name):
            # 检查完全禁止的函数
            if node.func.id in self.config.FORBIDDEN_BUILTINS:
                level = self.config.FORBIDDEN_BUILTINS[node.func.id]
                error = ForbiddenFunctionError(node.func.id).message
                if level == CodeSecurityLevel.FORBIDDEN:
                    self.result.forbidden_errors.append(error)
                else:
                    self.result.warning_errors.append(error)

            # 检查受限制的函数
            elif node.func.id in self.config.RESTRICTED_BUILTINS:
                restrictions = self.config.RESTRICTED_BUILTINS[node.func.id]

                # 特殊处理 open 函数
                if node.func.id == "open":
                    # 检查 mode 参数
                    for keyword in node.keywords:
                        if keyword.arg == "mode":
                            if isinstance(keyword.value, ast.Str):
                                mode = keyword.value.s
                                if mode not in restrictions["mode"]:
                                    self.result.forbidden_errors.append(
                                        RestrictedFunctionError(
                                            "open",
                                            self._format_allowed_modes(
                                                restrictions["mode"]
                                            ),
                                        ).message
                                    )
                    # 检查位置参数中的 mode
                    if len(node.args) >= 2:
                        if isinstance(node.args[1], ast.Str):
                            mode = node.args[1].s
                            if mode not in restrictions["mode"]:
                                self.result.forbidden_errors.append(
                                    RestrictedFunctionError(
                                        "open",
                                        self._format_allowed_modes(
                                            restrictions["mode"]
                                        ),
                                    ).message
                                )
                else:
                    self.result.forbidden_errors.append(
                        ForbiddenFunctionError(node.func.id).message
                    )
            # 检查禁止的函数
            elif node.func.id in self.imported_modules:
                func = self.imported_modules[node.func.id]
                if check_result := self._check_forbidden_function(func):
                    name, level = check_result
                    error = ForbiddenMethodError(name).message
                    if level == CodeSecurityLevel.FORBIDDEN:
                        self.result.forbidden_errors.append(error)
                    else:
                        self.result.warning_errors.append(error)

        elif isinstance(node.func, ast.Attribute):
            # 检查禁止的方法调用
            if isinstance(node.func.value, ast.Name):
                obj_name = node.func.value.id
                method_name = node.func.attr

                # 特殊处理 driver.close() 调用
                if obj_name == "driver" and method_name == "close":
                    self.result.warning_errors.append(
                        ForbiddenMethodError("driver.close").message
                    )
                elif obj_name in self.imported_modules:
                    obj = self.imported_modules[obj_name]
                    try:
                        method = getattr(obj, method_name)
                        if check_result := self._check_forbidden_function(method):
                            name, level = check_result
                            error = ForbiddenMethodError(name).message
                            if level == CodeSecurityLevel.FORBIDDEN:
                                self.result.forbidden_errors.append(error)
                            else:
                                self.result.warning_errors.append(error)
                    except AttributeError:
                        pass

        self.generic_visit(node)

    def visit_Import(self, node: ast.Import):
        """检查 import 语句"""
        for name in node.names:
            base_module = name.name.split(".")[0]
            if base_module in self.config.FORBIDDEN_IMPORTS:
                self.result.forbidden_errors.append(
                    ForbiddenImportError(name.name).message
                )
            elif base_module in self.config.RESTRICTED_IMPORTS:
                # 如果是 import os 这样的完整导入，给出警告
                if name.name == base_module:
                    self.result.forbidden_errors.append(
                        RestrictedImportError(
                            base_module,
                            ", ".join(self.config.RESTRICTED_IMPORTS[base_module]),
                        ).message
                    )
            # 存储导入的模块
            module = self._import_module(name.name)
            if module:
                self.imported_modules[name.asname or name.name] = module
        self.generic_visit(node)

    def visit_ImportFrom(self, node: ast.ImportFrom):
        """检查 from ... import 语句"""
        if not node.module:
            return

        base_module = node.module.split(".")[0]
        full_module = node.module

        if base_module in self.config.FORBIDDEN_IMPORTS:
            self.result.forbidden_errors.append(
                ForbiddenImportError(node.module).message
            )
            return

        module = self._import_module(node.module)
        if module:
            # 检查是否导入了禁止的内置函数
            for name in node.names:
                if name.name in self.config.FORBIDDEN_BUILTINS:
                    level = self.config.FORBIDDEN_BUILTINS[name.name]
                    error = ForbiddenFunctionError(name.name).message
                    if level == CodeSecurityLevel.FORBIDDEN:
                        self.result.forbidden_errors.append(error)
                    else:
                        self.result.warning_errors.append(error)
                    continue

                # 检查完整模块路径
                if full_module in self.config.RESTRICTED_IMPORTS:
                    allowed_attrs = self.config.RESTRICTED_IMPORTS[full_module]
                    if name.name not in allowed_attrs:
                        self.result.forbidden_errors.append(
                            RestrictedImportError(
                                full_module, ", ".join(allowed_attrs)
                            ).message
                        )
                # 检查基础模块
                elif base_module in self.config.RESTRICTED_IMPORTS:
                    allowed_attrs = self.config.RESTRICTED_IMPORTS[base_module]
                    if name.name not in allowed_attrs:
                        self.result.forbidden_errors.append(
                            RestrictedImportError(
                                base_module, ", ".join(allowed_attrs)
                            ).message
                        )

                # 存储导入的属性
                try:
                    attr = getattr(module, name.name)
                    self.imported_modules[name.asname or name.name] = attr
                except AttributeError:
                    pass

        self.generic_visit(node)

    def visit_Expr(self, node: ast.Expr) -> Optional[ast.AST]:
        """处理表达式语句"""
        if isinstance(node.value, ast.Call):
            self.visit_Call(node.value)
        return node

    def visit_Assign(self, node: ast.Assign) -> Optional[ast.AST]:
        """处理赋值语句"""
        if isinstance(node.value, ast.Call):
            self.visit_Call(node.value)
        return node


def check_code_security(code: str) -> SecurityCheckResult:
    """
    检查代码安全性

    :param code: 要检查的代码
    :return: 安全检查结果，包含禁止错误和警告错误
    """
    try:
        tree = ast.parse(code)
    except SyntaxError as e:
        result = SecurityCheckResult()
        result.forbidden_errors.append(SyntaxCheckError(str(e)).message)
        return result

    checker = CodeSecurityChecker()
    checker.visit(tree)
    return checker.result


def remove_unsafe_code(code: str) -> Optional[str]:
    """
    移除不安全的代码

    :param code: 原始代码
    :return: 处理后的安全代码，如果无法处理则返回 None
    """
    # 处理空字符串
    if not code:
        return "\n"

    # 如果代码只包含注释或空白字符，直接返回
    stripped_code = code.strip()
    if (
        not stripped_code
        or stripped_code.startswith("#")
        or stripped_code.startswith('"""')
    ):
        return code

    try:
        tree = ast.parse(code)
    except SyntaxError:
        return None

    class UnsafeCodeRemover(ast.NodeTransformer):
        """不安全代码移除器"""

        def __init__(self):
            self.imported_modules = {}  # 存储已导入的模块
            self._forbidden_checks = None  # 延迟初始化
            self.config = CodeSecurityConfig()

        @property
        def forbidden_checks(
            self,
        ) -> Mapping[str, Dict[str, Union[str, CodeSecurityLevel]]]:
            """延迟获取禁止的函数和方法列表"""
            if self._forbidden_checks is None:
                self._forbidden_checks = get_forbidden_checks()
            return self._forbidden_checks

        def _import_module(self, module_name: str) -> Optional[object]:
            """安全地导入模块"""
            try:
                return importlib.import_module(module_name)
            except (ImportError, ValueError):
                return None

        def _check_forbidden_function(
            self, func: Any
        ) -> Optional[Tuple[str, CodeSecurityLevel]]:
            """检查是否是禁止的函数或方法"""
            for name, check_info in self.forbidden_checks.items():
                if (
                    hasattr(func, "__module__")
                    and func.__module__ == check_info["module"]
                    and isinstance(check_info["level"], CodeSecurityLevel)
                ):
                    return name, check_info["level"]
            return None

        def visit_Call(self, node: ast.Call) -> Union[ast.AST, None]:
            """移除不安全的函数调用"""
            if isinstance(node.func, ast.Name):
                if node.func.id in self.config.FORBIDDEN_BUILTINS:
                    level = self.config.FORBIDDEN_BUILTINS[node.func.id]
                    if level == CodeSecurityLevel.WARNING:
                        return ast.Constant(value=None)
                    return ast.Constant(value=None)  # 对于 FORBIDDEN 级别，替换为 None

                if node.func.id in self.config.RESTRICTED_BUILTINS:
                    restrictions = self.config.RESTRICTED_BUILTINS[node.func.id]

                    # 特殊处理 open 函数
                    if node.func.id == "open":
                        # 检查 mode 参数
                        is_safe = True
                        for keyword in node.keywords:
                            if keyword.arg == "mode":
                                if isinstance(keyword.value, ast.Str):
                                    mode = keyword.value.s
                                    if mode not in restrictions["mode"]:
                                        is_safe = False
                                        break
                        # 检查位置参数中的 mode
                        if len(node.args) >= 2:
                            if isinstance(node.args[1], ast.Str):
                                mode = node.args[1].s
                                if mode not in restrictions["mode"]:
                                    is_safe = False

                        if not is_safe:
                            return ast.Constant(
                                value=None
                            )  # 不安全的 open 调用，替换为 None
                    else:
                        return ast.Constant(value=None)  # 其他受限制的函数，替换为 None

                # 检查禁止的函数
                elif node.func.id in self.imported_modules:
                    func = self.imported_modules[node.func.id]
                    if check_result := self._check_forbidden_function(func):
                        _, level = check_result
                        if level == CodeSecurityLevel.WARNING:
                            return ast.Constant(value=None)
                        return ast.Constant(
                            value=None
                        )  # 对于 FORBIDDEN 级别，替换为 None

            elif isinstance(node.func, ast.Attribute):
                # 检查禁止的方法调用
                if isinstance(node.func.value, ast.Name):
                    obj_name = node.func.value.id
                    method_name = node.func.attr

                    # 特殊处理 driver.close() 调用
                    if obj_name == "driver" and method_name == "close":
                        return ast.Constant(value=None)
                    elif obj_name in self.imported_modules:
                        obj = self.imported_modules[obj_name]
                        try:
                            method = getattr(obj, method_name)
                            if check_result := self._check_forbidden_function(method):
                                _, level = check_result
                                if level == CodeSecurityLevel.WARNING:
                                    return ast.Constant(value=None)
                                return ast.Constant(
                                    value=None
                                )  # 对于 FORBIDDEN 级别，替换为 None
                        except AttributeError:
                            pass

            return self.generic_visit(node)

        def visit_Import(self, node: ast.Import) -> Optional[ast.AST]:
            """移除不安全的 import 语句"""
            names = []
            for name in node.names:
                base_module = name.name.split(".")[0]
                if base_module not in self.config.FORBIDDEN_IMPORTS and (
                    base_module not in self.config.RESTRICTED_IMPORTS
                    or name.name != base_module
                ):
                    names.append(name)
                    # 存储导入的模块
                    module = self._import_module(name.name)
                    if module:
                        self.imported_modules[name.asname or name.name] = module
            if not names:
                return None
            node.names = names
            return node

        def visit_ImportFrom(self, node: ast.ImportFrom) -> Optional[ast.AST]:
            """移除不安全的 from import 语句"""
            if not node.module:
                return node

            base_module = node.module.split(".")[0]
            full_module = node.module

            if base_module in self.config.FORBIDDEN_IMPORTS:
                return None

            module = self._import_module(node.module)
            if module:
                # 移除禁止的内置函数
                names = []
                for name in node.names:
                    if name.name in self.config.FORBIDDEN_BUILTINS:
                        level = self.config.FORBIDDEN_BUILTINS[name.name]
                        if level == CodeSecurityLevel.WARNING:
                            continue
                        continue  # 对于 FORBIDDEN 级别，也跳过
                    else:
                        # 检查完整模块路径
                        if full_module in self.config.RESTRICTED_IMPORTS:
                            allowed_attrs = self.config.RESTRICTED_IMPORTS[full_module]
                            if name.name in allowed_attrs:
                                names.append(name)
                                # 存储导入的属性
                                try:
                                    attr = getattr(module, name.name)
                                    self.imported_modules[name.asname or name.name] = (
                                        attr
                                    )
                                except AttributeError:
                                    pass
                        # 检查基础模块
                        elif base_module in self.config.RESTRICTED_IMPORTS:
                            allowed_attrs = self.config.RESTRICTED_IMPORTS[base_module]
                            if name.name in allowed_attrs:
                                names.append(name)
                                # 存储导入的属性
                                try:
                                    attr = getattr(module, name.name)
                                    self.imported_modules[name.asname or name.name] = (
                                        attr
                                    )
                                except AttributeError:
                                    pass
                        else:
                            names.append(name)
                            # 存储导入的属性
                            try:
                                attr = getattr(module, name.name)
                                self.imported_modules[name.asname or name.name] = attr
                            except AttributeError:
                                pass

                if not names:
                    return None
                node.names = names

            return node

        def visit_Expr(self, node: ast.Expr) -> Optional[ast.AST]:
            """处理表达式语句"""
            if isinstance(node.value, ast.Call):
                result = self.visit_Call(node.value)
                if result is None:
                    return None
                if isinstance(result, ast.expr):
                    node.value = result
            return node

        def visit_Assign(self, node: ast.Assign) -> Optional[ast.AST]:
            """处理赋值语句"""
            if isinstance(node.value, ast.Call):
                result = self.visit_Call(node.value)
                if result is None:
                    return None
                if isinstance(result, ast.expr):
                    node.value = result
            return node

    try:
        # 移除不安全代码
        remover = UnsafeCodeRemover()
        tree = remover.visit(tree)
        # 修复 AST
        ast.fix_missing_locations(tree)
        # 生成代码
        return ast.unparse(tree)
    except Exception as e:
        log.error(f"移除不安全代码失败: {e}")
        raise e
