import threading
from typing import Any

from ccontrol_common.core.singleton import SingletonMeta

from ccontrol_test_engine.model.agent import Agent<PERSON>ode<PERSON>
from ccontrol_test_engine.model.device import DeviceModel
from ccontrol_test_engine.service.driver.ccontrol_driver import CControlDriver

from pydantic import BaseModel


class GlobalVarModel(BaseModel):
    device: DeviceModel
    agent: AgentModel
    user_global_var: dict = {}

    # user_global_var 方法
    def __setattr__(self, name: str, value: Any) -> None:
        if name in self.model_fields:
            super().__setattr__(name, value)
        else:
            self.user_global_var[name] = value

    def __getattr__(self, name: str) -> Any:
        if name in self.user_global_var:
            return self.user_global_var[name]
        raise AttributeError(
            f"'{self.__class__.__name__}' object has no attribute '{name}'"
        )

    # 添加字典操作方法
    def __getitem__(self, key: str) -> Any:
        return self.user_global_var[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.user_global_var[key] = value

    def __delitem__(self, key: str) -> None:
        del self.user_global_var[key]

    def __contains__(self, key: str) -> bool:
        return key in self.user_global_var

    def get(self, key: str, default: Any = None) -> Any:
        return self.user_global_var.get(key, default)

    def items(self):
        return self.user_global_var.items()

    def keys(self):
        return self.user_global_var.keys()

    def values(self):
        return self.user_global_var.values()

    def pop(self, key: str, default: Any = None) -> Any:
        return self.user_global_var.pop(key, default)

    def update(self, other: dict) -> None:
        self.user_global_var.update(other)

    def clear(self) -> None:
        self.user_global_var.clear()

    def __len__(self) -> int:
        return len(self.user_global_var)


class GlobalStore(metaclass=SingletonMeta):
    store: threading.local

    def __init__(self):
        self.store = threading.local()

    def get_store(self):
        return self.store

    def set_global_var(self, global_var: GlobalVarModel):
        self.store.var = global_var

    def get_global_var(self) -> GlobalVarModel:
        return self.store.var

    def set_driver(self, driver: CControlDriver):
        self.store.driver = driver

    def get_driver(self) -> CControlDriver:
        return self.store.driver


class GlobalVarBaseModel(BaseModel):
    """全局变量基础模型，继承该模型后，会自动从全局存储中获取数据，并合并到模型中"""

    def __init__(self, **data):
        # 如果没有传入数据，则尝试从全局存储中获取
        if not data:
            data = self._get_stored_data()
        else:
            # 如果传入了数据，则合并全局存储中的数据
            stored_data = self._get_stored_data()
            # 合并存储的数据和传入的数据，优先使用传入的数据
            data = {**stored_data, **data}
        super().__init__(**data)

    @classmethod
    def from_store(cls):
        """从全局存储中创建实例"""
        return cls()

    def _get_stored_data(self) -> dict:
        """从全局变量 user_global_var 中获取数据"""
        global_var = GlobalStore().get_global_var()
        if global_var is None:
            return {}
        return global_var.user_global_var


if __name__ == "__main__":
    from ccontrol_common.config.conf import settings
    from ccontrol_test_engine.model.device import get_device_info

    # 全局变量
    device_id = settings.DEVICE_ID
    device_info = get_device_info(device_id)
    if not device_info:
        raise AssertionError(f"device_id: {device_id} not found")

    _global_var = GlobalVarModel(device=device_info, agent=device_info.agent)
    GlobalStore().set_global_var(_global_var)

    # 设置全局变量，测试 GlobalVarBaseModel 是否生效
    _global_var["fw_url"] = "test123"

    class TestGlobalVarModel(GlobalVarBaseModel):
        fw_url: str

    # test_global_var = TestGlobalVarModel()
    test_global_var = TestGlobalVarModel.from_store()
    print(f"🚀 result: {test_global_var}")
