import time
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field

from ccontrol_test_engine.service.event.model.event_model import EventModel


class NodeTypeEnum(Enum):
    TASK = "task"
    TEST_SUITE = "test_suite"
    TEST_CASE = "test_case"
    EVENT = "event"


class NodeStatusEnum(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    FAILED = "failed"
    FINISHED = "finished"
    SKIPPED = "skipped"


class ScreenshotModel(BaseModel):
    """截图模型"""

    name: str
    path: str
    timestamp: int = Field(default_factory=lambda: int(time.time() * 1000))


class NodeBaseModel(BaseModel):
    node_type: NodeTypeEnum
    node_status: NodeStatusEnum = NodeStatusEnum.PENDING
    error_message: Optional[str] = None


class EventNodeModel(NodeBaseModel, EventModel):
    node_type: NodeTypeEnum = NodeTypeEnum.EVENT
    event_id: Optional[str]
    name: str
    description: Optional[str] = None
    screenshots: List[ScreenshotModel] = []


class TestCaseNodeModel(NodeBaseModel):
    node_type: NodeTypeEnum = NodeTypeEnum.TEST_CASE
    test_case_id: str
    name: str
    description: Optional[str] = None
    children: List[EventNodeModel] = []


class TestSuiteNodeModel(NodeBaseModel):
    node_type: NodeTypeEnum = NodeTypeEnum.TEST_SUITE
    test_suite_id: str
    name: str
    description: Optional[str] = None
    children: List[TestCaseNodeModel] = []


class TaskNodeModel(NodeBaseModel):
    node_type: NodeTypeEnum = NodeTypeEnum.TASK
    task_id: str
    name: str
    description: Optional[str] = None
    children: Optional[TestSuiteNodeModel] = None
