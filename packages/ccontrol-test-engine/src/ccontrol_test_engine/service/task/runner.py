import inspect
import threading
from threading import Event
from typing import Callable, Optional, List

from ccontrol_common.config.conf import settings
from ccontrol_common.core.context_manager import LogContextManager
from ccontrol_common.core.log import log
from ccontrol_common.core.log_context import LogContext, LogContextType
from ccontrol_common.services.ccontrol_service import need_login
from ccontrol_common.utils.stoppable_thread import StoppableThread
from ccontrol_common.utils.url import get_query_param
from ccontrol_test_engine.model.device import get_device_info
from ccontrol_test_engine.service.driver.ccontrol_driver import CControlDriver
from ccontrol_test_engine.service.event.generator.generator import gen_agent_rpc
from ccontrol_test_engine.service.event.model.event_model import (
    EventTypeEnum,
    PythonEventModel,
)
from ccontrol_test_engine.service.task.model.global_model import (
    GlobalStore,
    GlobalVarModel,
)
from ccontrol_test_engine.service.task.model.task_model import (
    EventNodeModel,
    NodeStatusEnum,
    TaskNodeModel,
    TestCaseNodeModel,
    TestSuiteNodeModel,
    ScreenshotModel,
)


class TaskRunner(threading.Thread):
    """
    任务执行器类
    继承自 threading.Thread，用于在多线程环境中执行任务
    """

    # 任务数据
    task_data: TaskNodeModel

    # 全局变量
    global_var: GlobalVarModel
    global_driver: CControlDriver

    # 子线程集合
    event_thread_list: List[StoppableThread]

    # 事件前置钩子列表
    event_setup_hooks: List[Callable]
    # 事件后置钩子列表
    event_teardown_hooks: List[Callable]

    def __init__(
        self,
        *args,
        global_var: GlobalVarModel,
        task_data: TaskNodeModel,
        on_task_complete: Optional[Callable] = None,
        **kwargs,
    ):
        # 强制使用服务端模式运行任务，因为用户模式运行 local_task 时，同服务端模式无需执行 quick_init 逻辑
        settings.RUN_MODE = "service"
        super().__init__()
        self.name = task_data.task_id
        self.task_data = task_data
        self.global_var = global_var
        self.event_thread_list = []
        self.event_setup_hooks = [self.record_screenshot]
        self.event_teardown_hooks = [self.record_screenshot]
        # 线程事件
        self.task_stop_event = Event()
        # 线程结束回调
        self.on_task_complete = on_task_complete
        # 上下文管理器
        self.context_manager = LogContextManager()
        # 日志处理器
        self.log_handler = log.add_task_handler(task_data.task_id)

    def is_task_stopped(self):
        """判断任务是否停止"""
        return self.task_stop_event.is_set()

    def stop_task(self):
        """停止任务"""
        self.task_stop_event.set()
        for task in self.event_thread_list:
            task.stop_thread()
            self.event_thread_list.remove(task)

    def record_screenshot(self, *_, event: EventNodeModel, **__):
        """记录任务过程截图"""
        if not self.global_driver:
            return False

        try:
            log.info("[Event Setup Hook] record screenshot")
            image_url = self.global_driver.get_screenshot_url()
        except TimeoutError:
            log.error("[Event Setup Hook] record screenshot failed")
            return False

        image_path = get_query_param(image_url, "path")
        if not image_path:
            return False

        event.screenshots.append(ScreenshotModel(name=event.name, path=image_path))
        return True

    def create_driver(self):
        """创建驱动"""
        log.info(" Start create_driver ".center(60, "="))
        adb_address = f"{self.global_var.agent.host}:{self.global_var.device.adb_forward_port}"
        log.info(f"adb_address: {adb_address}")
        gen_agent_rpc(self.global_var.device.id)
        driver = CControlDriver(
            serial=adb_address,
            device_id=self.global_var.device.id,
            agent_id=self.global_var.agent.id,
            task_id=self.task_data.task_id,
        )
        GlobalStore().set_driver(driver)
        self.global_driver = driver

        # 为 AgentRPC 线程创建上下文
        agent_context = LogContext(
            context_id=f"agent_{self.task_data.task_id}",
            context_type=LogContextType.AGENT,
            parent_id=str(threading.current_thread().ident),
            task_id=self.task_data.task_id,
            metadata={"agent_id": str(self.global_var.agent.id)},
        )

        # 确保线程ID存在
        if self.global_driver.agent_rpc_thread and self.global_driver.agent_rpc_thread.ident:
            self.context_manager.register_thread_context(str(self.global_driver.agent_rpc_thread.ident), agent_context)

        log.info(" End create_driver ".center(60, "="))

    def set_up(self):
        """任务前置"""
        log.info(" Start setUp ".center(60, "="))

        # Init threading local var
        GlobalStore().set_global_var(global_var=self.global_var)
        self.global_var = GlobalStore().get_global_var()

        # Device Driver
        if self.global_var.device.adb_forward_port:
            self.create_driver()

        log.info(" End setUp ".center(60, "="))

    def execute_event_setups(self, event: EventNodeModel):
        """执行所有事件前置钩子"""
        for setup_hook in self.event_setup_hooks:
            try:
                setup_hook(event=event)
            except Exception as e:
                log.error(f"执行事件前置钩子失败: {str(e)}")
                raise

    def execute_event_teardowns(self, event: EventNodeModel):
        """执行所有事件后置钩子"""
        for teardown_hook in self.event_teardown_hooks:
            try:
                teardown_hook(event=event)
            except Exception as e:
                log.error(f"执行事件后置钩子失败: {str(e)}")
                raise

    def execute_event(self, event: EventNodeModel):
        """执行事件"""
        # 确保事件ID存在
        event_id = event.event_id or f"event_{id(event)}"

        event_context = LogContext(
            context_id=event_id,
            context_type=LogContextType.EVENT,
            parent_id=str(threading.current_thread().ident),
            task_id=self.task_data.task_id,
        )
        with self.context_manager.context(event_context):
            log.info(f" Start Event {event.name} ".center(60, "="))
            event.node_status = NodeStatusEnum.PROCESSING

            try:
                # 执行事件前置钩子
                if self.event_setup_hooks:
                    log.info(" Start execute event setups ".center(60, "="))
                    self.execute_event_setups(event=event)
                    log.info(" End execute event setups ".center(60, "="))

                # 执行python脚本
                if event.data.type == EventTypeEnum.PYTHON:
                    result = self.global_driver.event.execute_python_code(event.data.content)
                    if result.success:
                        event.node_status = NodeStatusEnum.FINISHED
                        if result.filtered_code:
                            event.data.content = result.filtered_code
                    else:
                        event.node_status = NodeStatusEnum.FAILED
                        event.error_message = result.error_message
                        log.error(result.error_message)
                else:
                    event.node_status = NodeStatusEnum.FINISHED

                # 执行事件后置钩子
                if self.event_teardown_hooks:
                    log.info(" Start execute event teardowns ".center(60, "="))
                    self.execute_event_teardowns(event=event)
                    log.info(" End execute event teardowns ".center(60, "="))

            except Exception as e:
                error_msg = f"事件执行失败: {str(e)}"
                log.error(error_msg)
                event.error_message = error_msg
                event.node_status = NodeStatusEnum.FAILED

            log.info(f" End Event {event.name}: {event.node_status}".center(60, "="))

    def execute_test_case(self, test_case: TestCaseNodeModel):
        """执行测试用例"""
        # 确保测试用例ID存在
        case_id = test_case.test_case_id or f"case_{id(test_case)}"

        case_context = LogContext(
            context_id=case_id,
            context_type=LogContextType.CASE,
            parent_id=str(threading.current_thread().ident),
            task_id=self.task_data.task_id,
        )
        with self.context_manager.context(case_context):
            log.info(f" Start Case {test_case.name} ".center(60, "="))
            test_case.node_status = NodeStatusEnum.PROCESSING
            for idx, event in enumerate(test_case.children):
                if self.is_task_stopped():  # 接收到任务停止事件，跳过后续事件
                    log.info(" task is stopped, skip other event ")
                    continue
                self.execute_event(event=event)
                if event.node_status == NodeStatusEnum.FAILED:
                    test_case.node_status = NodeStatusEnum.FAILED
                    # 标记后续事件为 skipped
                    for skipped_event in test_case.children[idx + 1 :]:
                        skipped_event.node_status = NodeStatusEnum.SKIPPED
                    break
            else:
                test_case.node_status = NodeStatusEnum.FINISHED
            log.info(f" End Case {test_case.name} : {test_case.node_status}".center(60, "="))

    def execute_test_suite(self, test_suite: TestSuiteNodeModel):
        """执行测试套件"""
        # 确保测试套件ID存在
        suite_id = test_suite.test_suite_id or f"suite_{id(test_suite)}"

        suite_context = LogContext(
            context_id=suite_id,
            context_type=LogContextType.SUITE,
            parent_id=str(threading.current_thread().ident),
            task_id=self.task_data.task_id,
        )
        with self.context_manager.context(suite_context):
            log.info(f" Start Suite {test_suite.name} ".center(60, "="))
            test_suite.node_status = NodeStatusEnum.PROCESSING
            test_case_list = test_suite.children
            for idx, test_case in enumerate(test_case_list):
                if self.is_task_stopped():  # 接收到任务停止事件，跳过后续用例
                    log.info(" task is stopped skip other test case ")
                    continue
                self.execute_test_case(test_case=test_case)
                if test_case.node_status == NodeStatusEnum.FAILED:
                    test_suite.node_status = NodeStatusEnum.FAILED
                    # 标记后续用例为 skipped
                    for skipped_case in test_case_list[idx + 1 :]:
                        skipped_case.node_status = NodeStatusEnum.SKIPPED
                    break
            else:
                test_suite.node_status = NodeStatusEnum.FINISHED
            log.info(f" End Suite {test_suite.name}: {test_suite.node_status}".center(60, "="))

    def execute_task(self):
        """执行任务"""
        log.info(" Start execute ".center(60, "="))
        test_suite = self.task_data.children
        self.task_data.node_status = NodeStatusEnum.PROCESSING
        if not self.is_task_stopped() and test_suite is not None:
            self.execute_test_suite(test_suite=test_suite)
            self.task_data.node_status = test_suite.node_status
        else:
            self.task_data.node_status = NodeStatusEnum.FINISHED
        log.info(" End execute ".center(60, "="))

    def tear_down(self):
        """任务后置"""
        log.info(" Start tearDown ".center(60, "="))
        try:
            self.global_driver.close()
            log.info(f"task data: {self.task_data.model_dump_json()}")
        except Exception as e:
            log.error(f"任务清理失败: {str(e)}")
        finally:
            # 移除日志处理器
            if hasattr(self, "log_handler"):
                try:
                    log.remove(self.log_handler)
                except Exception as e:
                    log.error(f"移除日志处理器失败: {str(e)}")
            # 回调TaskInstanceManager.complete_task方法
            if self.on_task_complete:
                self.on_task_complete(self.task_data.task_id)
            log.info(" End tearDown ".center(60, "="))

    def run(self):
        """运行任务"""
        # 确保任务ID存在
        task_id = self.task_data.task_id or f"task_{id(self.task_data)}"

        # 设置任务上下文
        task_context = LogContext(context_id=task_id, context_type=LogContextType.TASK, task_id=task_id)
        with self.context_manager.context(task_context):
            try:
                self.set_up()
                self.execute_task()
            except Exception as e:
                log.error(f"任务执行失败: {str(e)}")
                self.task_data.node_status = NodeStatusEnum.FAILED
                self.task_data.error_message = str(e)
            finally:
                self.tear_down()


@need_login
def quick_start(func):
    """快速启动当前文件"""
    source_code = inspect.getsource(func)
    device_id = settings.DEVICE_ID

    # init: global var
    device_info = get_device_info(device_id)
    if not device_info:
        raise AssertionError(f"device_id: {device_id} not found")
    global_var = GlobalVarModel(device=device_info, agent=device_info.agent)

    # init: task data
    # with open(caller_path, "r", encoding="utf-8", errors="ignore") as f:
    #     caller_content = f.read()
    task_node = TaskNodeModel(task_id="local_task", name="local_task")
    test_suite = TestSuiteNodeModel(test_suite_id="local_test_suite", name="local_test_suite")
    test_case_node = TestCaseNodeModel(test_case_id="local_test_case", name="local_test_case")
    event_node = EventNodeModel(
        event_id="local_event",
        name="local_event",
        data=PythonEventModel(type=EventTypeEnum.PYTHON, content=source_code),
    )
    task_node.children = test_suite
    test_suite.children.append(test_case_node)
    test_case_node.children.append(event_node)

    TaskRunner(global_var=global_var, task_data=task_node).run()


@need_login
def quick_init():
    """快速初始化"""
    # 测试引擎服务不需要执行quick_init
    if settings.RUN_MODE == "service":
        return

    # 获取设备信息
    device_id = settings.DEVICE_ID
    device_info = get_device_info(device_id)
    if not device_info:
        raise AssertionError(f"device_id: {device_id} not found")

    # 设置全局变量
    global_var = GlobalVarModel(device=device_info, agent=device_info.agent)
    GlobalStore().set_global_var(global_var)

    # 初始化 Driver
    if global_var.device.adb_forward_port:
        log.info(" Start create_driver ".center(60, "="))
        agent_host = global_var.agent.host
        adb_forward_port = global_var.device.adb_forward_port
        adb_address = f"{agent_host}:{adb_forward_port}"
        log.info(f"adb_address: {adb_address}")
        driver = CControlDriver(
            serial=adb_address,
            device_id=global_var.device.id,
            agent_id=global_var.agent.id,
        )
        GlobalStore().set_driver(driver)
        log.info(" End create_driver ".center(60, "="))


if __name__ == "__main__":
    from ccontrol_test_engine.mock.global_var import mock_global_var
    from ccontrol_test_engine.mock.task import mock_task

    DEVICE_ID = 147
    _global_var = mock_global_var(DEVICE_ID)
    log.info(f"global_var: {_global_var}")
    _task_data = mock_task()
    t = TaskRunner(global_var=_global_var, task_data=_task_data)
    t.start()
    t.join()
