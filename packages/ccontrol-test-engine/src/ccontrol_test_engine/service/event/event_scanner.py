import ast
import importlib
import os
import sys
from typing import Optional, Tuple, List, Dict

from pydantic import BaseModel

from ccontrol_common.core.log import log
from ccontrol_test_engine.service.event.model.event_model import (
    EventModel,
    NormalEventModel,
    EventTypeEnum,
)


def has_event_decorator(node: ast.FunctionDef, decorator_name: str) -> bool:
    """
    判断函数是否被装饰器修饰
    :param node:
    :param decorator_name:
    :return:
    """
    for decorator in node.decorator_list:
        # 装饰器：@decorator_name
        if isinstance(decorator, ast.Name) and decorator.id == decorator_name:
            return True
        # 构造装饰器：@decorator_name()
        if (
            isinstance(decorator, ast.Call)
            and hasattr(decorator.func, "id")
            and getattr(decorator.func, "id") == decorator_name
        ):
            return True
    return False


def get_func_schemas(
    node: ast.FunctionDef, module_dict: dict
) -> <PERSON><PERSON>[Optional[dict], Optional[dict]]:
    """
    获取函数的参数类型和返回值类型
    :param node:
    :param module_dict:
    :return:
    """
    module_name = module_dict.get("__name__")
    node_name = node.name

    # 校验规则：函数是否只有一个参数
    if len(node.args.args) > 1:
        raise TypeError(
            f"[{module_name}][{node_name}] Function should have only one argument"
        )

    # 获取参数和返回值类型
    input_arg = None
    output_arg = None
    if len(node.args.args) == 1:
        arg = node.args.args[0]
        if arg.annotation:
            # pylint: disable=eval-used
            input_arg = eval(ast.unparse(arg.annotation), module_dict)
    if node.returns:
        # pylint: disable=eval-used
        output_arg = eval(ast.unparse(node.returns), module_dict)

    # 校验规则：参数类型是否继承自 pydantic.BaseModel
    if input_arg and not issubclass(input_arg, BaseModel):
        raise TypeError(
            f"[{module_name}][{node_name}] Input argument should be a subclass of pydantic.BaseModel"
        )
    if output_arg and not issubclass(output_arg, BaseModel):
        raise TypeError(
            f"[{module_name}][{node_name}] Output argument should be a subclass of pydantic.BaseModel"
        )

    # 获取参数和返回值的 json schema
    input_schema = input_arg and input_arg.model_json_schema()
    print(f"🚀 input_schema: {input_schema}")
    output_schema = output_arg and output_arg.model_json_schema()
    print(f"🚀 output_schema: {output_schema}")
    return input_schema, output_schema


def scan_file_events(filename: str, decorator_name: str) -> List[EventModel]:
    """
    扫描单个文件，找出被装饰器修饰的函数
    :param filename: 文件路径
    :param decorator_name: 事件装饰器名称
    :return:
    """
    target_func_list: List[EventModel] = []
    with open(filename, "r", encoding="utf-8") as source:
        tree = ast.parse(source.read())
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and has_event_decorator(
                node, decorator_name
            ):
                print(
                    f"Function {node.name} in {filename} is decorated with @{decorator_name}"
                )
                module = (
                    os.path.splitext(filename)[0].replace(os.path.sep, ".").strip(".")
                )
                _target_module = importlib.import_module(module)
                input_schema, output_schema = get_func_schemas(
                    node, _target_module.__dict__
                )
                target_func_list.append(
                    EventModel(
                        data=NormalEventModel(
                            type=EventTypeEnum.NORMAL,
                            function_name=node.name,
                            module=module,
                            input_schema=input_schema,
                            output_schema=output_schema,
                        )
                    )
                )
    return target_func_list


# 校验规则：方法名是否唯一
def check_unique_event_name(event_list: List[EventModel]):
    event_name_group: Dict[str, List[EventModel]] = {}
    for event in event_list:
        if event.data.type != EventTypeEnum.NORMAL:
            continue
        event_name_group.setdefault(event.data.function_name, [])
        event_name_group[event.data.function_name].append(event)

    # 校验规则：方法名是否唯一
    error_flag = False
    for event_name, event_group in event_name_group.items():
        if len(event_group) > 1:
            error_flag = True
            error_msg = [
                f'Event name "{event_name}" is not unique!',
                "Please check the following events:",
                *[
                    f"\t{event.data.module}.{event.data.function_name}"
                    for event in event_group
                    if event.data.type == EventTypeEnum.NORMAL
                ],
            ]
            log.error("\n".join(error_msg))
    if error_flag:
        raise AssertionError("Event name is not unique!")


def scan_directory(path: str, decorator_name: str) -> List[EventModel]:
    """
    扫描目录下的所有文件，找出被装饰器修饰的函数
    :param path:
    :param decorator_name:
    :return:
    """
    # 将目标目录加入系统路径，以便导入模块
    sys.path.append(path)

    event_list: List[EventModel] = []
    for root, _, files in os.walk(path):
        for file in files:
            if file.endswith(".py"):
                file_events = scan_file_events(os.path.join(root, file), decorator_name)
                event_list += file_events

    check_unique_event_name(event_list)
    return event_list


if __name__ == "__main__":
    input_event_list = scan_directory("packages/ccontrol-test-engine", "event")
    print(f"input_event_list: {input_event_list}")
