import time
from functools import wraps


def event():
    def decorator(func):
        func.is_event = True

        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            print(f"函数 {func.__name__} 运行时长为 {duration} 秒")
            return result

        return wrapper

    return decorator


def log_time(func):
    def wrapper(*args, **kwargs):
        func.is_event = True
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        duration = end_time - start_time
        print(f"函数 {func.__name__} 运行时长为 {duration} 秒")
        return result

    return wrapper
