from pydantic import BaseModel

from ccontrol_test_engine.service.event.decorator import event


class CustomReqModel(BaseModel):
    name: str
    age: int


class CustomRespModel(BaseModel):
    name: str
    age: int


@event()
def test_func(data: CustomReqModel) -> CustomRespModel:
    print("This is a test function", CustomReqModel)
    return CustomRespModel(name=data.name, age=data.age)


@event()
def test_func2(data: CustomReqModel):
    print("This is a test function", data)
    return True


if __name__ == "__main__":
    CustomRespModel.model_json_schema()
