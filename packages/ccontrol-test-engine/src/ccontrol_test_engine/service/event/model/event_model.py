from enum import Enum
from typing import Optional, Union, Literal

from pydantic import BaseModel


class EventTypeEnum(str, Enum):
    NORMAL = "normal"
    RPC = "rpc"
    PYTHON = "python"


class EventBaseModel(BaseModel):
    function_name: str
    module: str
    input_schema: Optional[dict] = None
    output_schema: Optional[dict] = None


class NormalEventModel(EventBaseModel):
    type: Literal[EventTypeEnum.NORMAL]


class RPCEventModel(EventBaseModel):
    type: Literal[EventTypeEnum.RPC]
    # TODO: 待实现，存在这个信息则使用 RPC 方式调用
    rpc: Optional[dict] = None


class PythonEventModel(BaseModel):
    type: Literal[EventTypeEnum.PYTHON]
    content: str


class EventModel(BaseModel):
    # TODO: 后台事件待实现
    is_background: bool = False
    backend_strategy: Optional[str] = None
    data: Union[NormalEventModel, RPCEventModel, PythonEventModel]


if __name__ == "__main__":
    # event = EventModel(
    #     data={"type": "normal", "function_name": "test", "module": "event.test"}
    # )
    event = EventModel.model_validate(
        {"data": {"type": "normal", "function_name": "test", "module": "event.test"}}
    )
    print(f"event: {event}")

    event = EventModel(
        data=NormalEventModel(
            type=EventTypeEnum.NORMAL, function_name="test", module="event.test"
        )
    )
    print(event)

    event = EventModel.model_validate(
        {"data": {"type": "python", "content": "print('hello world')"}}
    )
    print(f"event: {event}")
