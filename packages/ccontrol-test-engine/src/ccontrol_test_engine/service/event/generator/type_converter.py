"""类型转换器模块

此模块负责将 Python 类型注解转换为字符串表示。
"""

from enum import Enum
from typing import (
    Dict,
    List,
    Literal,
    Set,
    Tuple,
    Union,
    get_args,
    get_origin,
)
from pydantic import BaseModel


def convert_type_to_python(type_annotation) -> Tuple[str, Set[str]]:
    """
    将类型注解转换为 Python 类型字符串，并返回需要导入的类型

    Args:
        type_annotation: Python 类型注解

    Returns:
        Tuple[str, Set[str]]: 返回类型字符串和需要导入的类型集合
    """
    nested_types = set()

    # 处理 None
    if type_annotation is None:
        return "None", nested_types

    # 处理基本类型
    if type_annotation in (str, int, float, bool):
        return type_annotation.__name__, nested_types

    # 处理 Optional 类型
    origin = get_origin(type_annotation)
    if origin is Union:
        args = get_args(type_annotation)
        if len(args) == 2 and args[1] is type(None):  # noqa
            inner_type, inner_nested = convert_type_to_python(args[0])
            nested_types.update(inner_nested)
            return f"Optional[{inner_type}]", nested_types

    # 处理容器类型
    if origin is not None:
        args = get_args(type_annotation)

        # 处理 List
        if origin in (list, List):
            if args:
                inner_type, inner_nested = convert_type_to_python(args[0])
                nested_types.update(inner_nested)
                return f"list[{inner_type}]", nested_types
            return "list", nested_types

        # 处理 Dict
        elif origin in (dict, Dict):
            if len(args) == 2:
                key_type, key_nested = convert_type_to_python(args[0])
                value_type, value_nested = convert_type_to_python(args[1])
                nested_types.update(key_nested)
                nested_types.update(value_nested)
                return f"dict[{key_type}, {value_type}]", nested_types
            return "dict", nested_types

        # 处理 Set
        elif origin in (set, Set):
            if args:
                inner_type, inner_nested = convert_type_to_python(args[0])
                nested_types.update(inner_nested)
                return f"set[{inner_type}]", nested_types
            return "set", nested_types

        # 处理 Tuple
        elif origin in (tuple, Tuple):
            if args:
                inner_types = []
                for arg in args:
                    inner_type, inner_nested = convert_type_to_python(arg)
                    inner_types.append(inner_type)
                    nested_types.update(inner_nested)
                return f"tuple[{', '.join(inner_types)}]", nested_types
            return "tuple", nested_types

        # 处理 Literal
        elif origin is Literal:
            literals = [
                f'"{arg}"' if isinstance(arg, str) else str(arg) for arg in args
            ]
            return f"Literal[{', '.join(literals)}]", nested_types

    # 处理枚举类型
    if isinstance(type_annotation, type) and issubclass(type_annotation, Enum):
        nested_types.add(type_annotation.__name__)
        return type_annotation.__name__, nested_types

    # 处理 pydantic 模型
    if isinstance(type_annotation, type) and issubclass(type_annotation, BaseModel):
        nested_types.add(type_annotation.__name__)
        return type_annotation.__name__, nested_types

    # 其他情况返回 Any
    return "Any", nested_types
