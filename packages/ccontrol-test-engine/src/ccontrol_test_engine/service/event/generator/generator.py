"""
Agent RPC 代码生成器

此模块负责从远程服务器获取 Agent 事件定义，并生成相应的 RPC 调用代码。
生成的代码包括：
1. 输入/输出数据模型（基于 Pydantic）
2. RPC 调用实现
3. 统一的 RPC 接口类
"""

import json
import os
import re
import shutil
import sys
import importlib.util
from importlib.machinery import ModuleSpec
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from ccontrol_common.config.api_conf import API_EVENT_FIND_MANY
from ccontrol_common.config.conf import settings
from ccontrol_common.config.path_conf import BASE_PATH, GEN_AGENT_RPC_PATH, GEN_PATH
from ccontrol_common.core.http_client import do_get
from ccontrol_common.core.log import log
from ccontrol_common.services.ccontrol_service import need_login
from datamodel_code_generator import InputFileType, generate
from pydantic import BaseModel, Field, ValidationError

from ccontrol_test_engine.model.device import get_device_info
from ccontrol_test_engine.service.event.generator.type_converter import (
    convert_type_to_python,
)


# 常量定义
INPUT_MODEL_FILE_NAME = "input_schema.py"
OUTPUT_MODEL_FILE_NAME = "output_schema.py"


@dataclass
class ModelInfo:
    """模型信息"""

    name: str
    fields: Dict[str, Dict[str, Any]]
    nested_types: Set[str]


class AgentEventModel(BaseModel):
    """Agent 事件模型"""

    id: str
    method: str
    agent_Id: str = Field(..., alias="agentId")
    description: Optional[str]
    input_schema: Optional[dict] = Field(..., alias="inputSchema")
    output_schema: Optional[dict] = Field(..., alias="outputSchema")


class SchemaGenerator:
    """Schema 代码生成器"""

    @staticmethod
    def generate_schema_model(schema: Optional[dict], event_path: Path, file_name: str) -> Optional[str]:
        """生成 Schema 模型代码"""
        if not schema:
            return None

        model_path = Path.joinpath(event_path, file_name)
        generate(
            input_=json.dumps(schema),
            output=model_path,
            input_file_type=InputFileType.JsonSchema,
            field_include_all_keys=True,
            use_schema_description=True,
            use_field_description=True,
            use_title_as_name=True,
        )
        return get_model_name(str(model_path))


class RPCGenerator:
    """RPC 代码生成器"""

    @staticmethod
    def generate_caller_file(
        agent_event: AgentEventModel,
        event_path: Path,
        input_model_name: Optional[str],
        output_model_name: Optional[str],
    ):
        """生成 RPC 调用文件"""
        caller_path = Path.joinpath(event_path, "__init__.py")
        module = ".".join(event_path.relative_to(BASE_PATH).parts)

        imports = RPCGenerator._generate_imports(module, input_model_name, output_model_name)
        func_def = RPCGenerator._generate_function_definition(agent_event, input_model_name, output_model_name)

        caller_content = f"""
{chr(10).join(imports)}


{func_def}
""".lstrip()

        with open(caller_path, "w", encoding="utf-8") as f:
            f.write(caller_content)

    @staticmethod
    def _generate_imports(module: str, input_model_name: Optional[str], output_model_name: Optional[str]) -> List[str]:
        """生成导入语句"""
        imports = []
        if input_model_name:
            imports.append(f"from {module}.input_schema import {input_model_name} as InputModel")
        if output_model_name:
            imports.append(f"from {module}.output_schema import {output_model_name} as OutputModel")
        return imports

    @staticmethod
    def _generate_function_definition(
        agent_event: AgentEventModel,
        input_model_name: Optional[str],
        output_model_name: Optional[str],
    ) -> str:
        """生成函数定义"""
        # 添加 Optional 导入
        imports = ["from typing import Optional"]

        # 构建函数签名
        func_signature = "def call("
        if input_model_name:
            func_signature += "data: InputModel"
            func_signature += ", "
        func_signature += "*, rpc_timeout: Optional[float] = None"
        func_signature += ") -> "
        func_signature += "OutputModel" if output_model_name else "None"
        func_signature += ":"

        # 构建文档字符串，添加 rpc_timeout 参数说明
        docstring = f'    """{agent_event.description or ""}'
        if docstring.strip() != '    """':
            docstring += "\n\n"
        docstring += '    Args:\n        rpc_timeout: 可选的 RPC 调用超时时间（秒），默认为 None 表示使用系统默认超时\n    """'

        # 构建函数体，传递 timeout 参数
        func_body = """
    from ccontrol_test_engine import GlobalStore
    driver = GlobalStore().get_driver()
    resp = driver.agent_rpc_thread.call("{}", {}, timeout=rpc_timeout)
    return {}""".format(
            agent_event.method,
            "data.model_dump()" if input_model_name else "{}",
            "OutputModel(**resp)" if output_model_name else "None",
        )

        # 提前计算导入语句，避免在 f-string 中使用转义字符
        imports_text = "\n".join(imports)

        # 使用正确的 f-string 语法
        return f"{imports_text}\n\n{func_signature}\n{docstring}\n{func_body}"


class AgentRPCAggregator:
    """RPC 聚合器"""

    def __init__(self, gen_path: Path):
        self.gen_path = gen_path

    def aggregate(self):
        """聚合所有 RPC 到一个文件中"""
        content_imports, content_defs = self._generate_content()
        if not content_defs:
            content_defs.append("    pass")

        template = self._generate_template(content_imports, content_defs)
        self._write_file(template)

    def _generate_content(self) -> Tuple[List[str], List[str]]:
        """生成聚合内容"""
        module = ".".join(self.gen_path.relative_to(BASE_PATH).parts)
        content_imports = []
        content_defs = []

        for rpc_dir in os.listdir(self.gen_path):
            if not self._is_valid_rpc_dir(rpc_dir):
                continue

            self._process_rpc_dir(rpc_dir, module, content_imports, content_defs)

        return self._finalize_content(content_imports, content_defs)

    def _is_valid_rpc_dir(self, rpc_dir: str) -> bool:
        """检查是否为有效的 RPC 目录"""
        rpc_dir_path = os.path.join(self.gen_path, rpc_dir)
        return os.path.isdir(rpc_dir_path)

    def _process_rpc_dir(
        self,
        rpc_dir: str,
        module: str,
        content_imports: List[str],
        content_defs: List[str],
    ):
        """处理单个 RPC 目录"""
        # 导入调用函数
        content_imports.append(f"from {module}.{rpc_dir} import call as _{rpc_dir}")

        # 导入输入模型（如果存在）
        input_model_name = self._get_input_model_name(rpc_dir)
        if input_model_name:
            content_imports.append(f"from {module}.{rpc_dir} import InputModel as InputModel_{rpc_dir}")
            # 导入输入模型中的嵌套类型
            input_file_path = os.path.join(self.gen_path, rpc_dir, INPUT_MODEL_FILE_NAME)
            if os.path.exists(input_file_path):
                _, nested_types = self._get_model_fields(input_file_path)
                for nested_type in nested_types:
                    content_imports.append(f"from {module}.{rpc_dir}.input_schema import {nested_type}")

        # 导入输出模型（如果存在）
        output_file_path = os.path.join(self.gen_path, rpc_dir, OUTPUT_MODEL_FILE_NAME)
        if os.path.exists(output_file_path):
            content_imports.append(f"from {module}.{rpc_dir} import OutputModel as OutputModel_{rpc_dir}")
            # 导入输出模型中的嵌套类型
            _, nested_types = self._get_model_fields(output_file_path)
            for nested_type in nested_types:
                content_imports.append(f"from {module}.{rpc_dir}.output_schema import {nested_type}")

        # 获取方法定义和嵌套类型
        method_def, _ = self._generate_rpc_method(rpc_dir, input_model_name)
        content_defs.append(method_def)

    def _get_input_model_name(self, rpc_dir: str) -> Optional[str]:
        """获取输入模型名称"""
        rpc_dir_path = self.gen_path / rpc_dir
        if INPUT_MODEL_FILE_NAME in os.listdir(rpc_dir_path):
            input_file_path = rpc_dir_path / INPUT_MODEL_FILE_NAME
            return get_model_name(str(input_file_path))
        return None

    def _generate_rpc_method(self, rpc_dir: str, input_model_name: Optional[str]) -> Tuple[str, Set[str]]:
        """生成 RPC 方法和需要导入的类型"""
        # 获取输出模型名称
        output_file_path = os.path.join(self.gen_path, rpc_dir, OUTPUT_MODEL_FILE_NAME)
        has_output = os.path.exists(output_file_path)
        return_type = f"OutputModel_{rpc_dir}" if has_output else "None"

        if not input_model_name:
            # 无参数的情况
            method_def = f"""
    @staticmethod
    def {rpc_dir}(*, rpc_timeout: Optional[float] = None) -> {return_type}:
        \"\"\"
        无参数方法
        
        Args:
            rpc_timeout: 可选的 RPC 调用超时时间（秒），默认为 None 表示使用系统默认超时
        \"\"\"
        return _{rpc_dir}(rpc_timeout=rpc_timeout)"""
            return method_def, set()

        # 获取输入模型的字段信息
        input_file_path = os.path.join(self.gen_path, rpc_dir, INPUT_MODEL_FILE_NAME)
        fields, nested_types = self._get_model_fields(input_file_path)

        # 生成方法签名和参数列表
        params = []
        param_docs = []
        for field_name, field_info in fields.items():
            type_hint = field_info["type"]
            description = field_info["description"] if field_info["description"] else f"类型: {field_info['type']}"

            # 将示例添加到参数文档中
            if field_info["examples"]:
                examples_str = ", ".join(_safe_str(ex) for ex in field_info["examples"])
                description = f"{description} (参考值: {examples_str})"

            param_docs.append(f"            {field_name}: {description}")

            # 生成参数定义
            if field_info["default"] is ...:
                params.append(f"{field_name}: {type_hint}")
            else:
                default_value = f'"{field_info["default"]}"' if isinstance(field_info["default"], str) else str(field_info["default"])
                params.append(f"{field_name}: {type_hint} = {default_value}")

        # 生成完整的方法定义
        param_docs_str = "\n".join(param_docs)
        method_def = f"""
    @staticmethod
    def {rpc_dir}(*, {', '.join(params)}, rpc_timeout: Optional[float] = None) -> {return_type}:
        \"\"\"
        RPC 方法

        Args:
{param_docs_str}
            rpc_timeout: 可选的 RPC 调用超时时间（秒），默认为 None 表示使用系统默认超时

        Returns:
            {return_type}: RPC 调用结果
        \"\"\"
        data = InputModel_{rpc_dir}({', '.join(f'{name}={name}' for name in fields.keys())})
        return _{rpc_dir}(data=data, rpc_timeout=rpc_timeout)"""

        return method_def, nested_types

    def _get_model_fields(self, file_path: str) -> Tuple[dict, Set[str]]:
        """获取模型的字段信息和需要导入的类型"""
        if not file_path or not os.path.exists(file_path):
            return {}, set()

        # 导入生成的模型
        spec = importlib.util.spec_from_file_location("temp_module", file_path)
        if not spec:
            return {}, set()

        spec = spec if isinstance(spec, ModuleSpec) else None
        if not spec or not spec.loader:
            return {}, set()

        module = importlib.util.module_from_spec(spec)
        sys.modules["temp_module"] = module

        spec.loader.exec_module(module)

        # 获取模型类
        model_name = get_model_name(file_path)
        if not model_name or not hasattr(module, model_name):
            return {}, set()

        model_class = getattr(module, model_name)
        model_schema = model_class.model_json_schema()

        # 获取字段的类型注解和文档
        type_annotations = {}
        nested_types = set()

        for field_name, field in model_class.model_fields.items():
            field_type = field.annotation
            python_type, field_nested_types = convert_type_to_python(field_type)
            type_annotations[field_name] = python_type
            nested_types.update(field_nested_types)

        # 提取字段信息
        fields = {}
        for field_name, field_info in model_schema.get("properties", {}).items():
            field_desc = field_info.get("description", "")
            field_default = field_info.get("default", ...)
            field_examples = field_info.get("examples", [])
            fields[field_name] = {
                "type": type_annotations.get(field_name, "Any"),
                "description": field_desc,
                "default": field_default,
                "examples": field_examples,
            }

        return fields, nested_types

    def _generate_template(self, content_imports: List[str], content_defs: List[str]) -> str:
        """生成聚合模板"""
        return """
{}


class AgentRPC:
    \"\"\"
    Agent RPC 类，提供所有 RPC 方法的调用接口
    \"\"\"
{}
    """.strip().format("\n".join(content_imports), "\n".join(content_defs))

    def _finalize_content(self, content_imports: List[str], content_defs: List[str]) -> Tuple[List[str], List[str]]:
        """最终处理聚合内容"""
        # 添加必要的导入
        imports_needed = set()
        for def_content in content_defs:
            if "Optional[" in def_content:
                imports_needed.add("from typing import Optional")
            if "Literal[" in def_content:
                imports_needed.add("from typing import Literal")
            if any(x in def_content for x in ["list[", "dict[", "tuple[", "set["]):
                imports_needed.add("from typing import Any")

        content_imports = sorted(list(imports_needed)) + sorted(list(set(content_imports)))
        return content_imports, content_defs

    def _write_file(self, content: str):
        """写入聚合文件"""
        agg_file_path = Path.joinpath(self.gen_path, "__init__.py")
        with open(agg_file_path, "w", encoding="utf-8") as f:
            f.write(content)


class AgentRPCGenerator:
    """Agent RPC 生成器主类"""

    def __init__(self, device_id: Optional[int] = None):
        self.device_id = device_id
        self.gen_path = get_gen_agent_rpc_path(device_id)

    def generate(self):
        """生成 Agent RPC 代码"""
        device_info = self._get_device_info()
        event_list = self._fetch_agent_events(device_info.agent_id)

        self._prepare_gen_path()

        for event in event_list:
            self._generate_event(event)

        self._aggregate_rpc()

    def _get_device_info(self):
        """获取设备信息"""
        target_device_id = self.device_id or settings.DEVICE_ID
        device_info = get_device_info(target_device_id)
        if not device_info:
            raise AssertionError("device_info is None")
        return device_info

    def _fetch_agent_events(self, agent_id: str) -> List[AgentEventModel]:
        """获取 Agent 事件列表"""
        query = {"where": {"agentId": agent_id}}
        resp = do_get(
            settings.ZEUS_DOMAIN + API_EVENT_FIND_MANY,
            params={"q": json.dumps(query)},
        )
        event_list = resp.json().get("data", [])
        return self._validate_events(event_list)

    def _validate_events(self, event_list: List[dict]) -> List[AgentEventModel]:
        """验证事件列表"""
        validated_events = []
        for event in event_list:
            try:
                validated_event = AgentEventModel(**event)
                validated_events.append(validated_event)
            except ValidationError as e:
                log.error(f"ValidationError for event {event.get('id')}: {e}")
        return validated_events

    def _prepare_gen_path(self):
        """准备生成目录"""
        if os.path.exists(self.gen_path):
            shutil.rmtree(self.gen_path)
        os.makedirs(self.gen_path)

    def _generate_event(self, event: AgentEventModel):
        """生成单个事件的代码"""
        event_path = Path.joinpath(self.gen_path, event.method)
        os.makedirs(event_path, exist_ok=True)

        schema_gen = SchemaGenerator()
        input_model_name = schema_gen.generate_schema_model(event.input_schema, event_path, INPUT_MODEL_FILE_NAME)
        output_model_name = schema_gen.generate_schema_model(event.output_schema, event_path, OUTPUT_MODEL_FILE_NAME)

        RPCGenerator.generate_caller_file(event, event_path, input_model_name, output_model_name)

    def _aggregate_rpc(self):
        """聚合 RPC 代码"""
        aggregator = AgentRPCAggregator(self.gen_path)
        aggregator.aggregate()


# 工具函数
def get_model_name(file_path: str) -> Optional[str]:
    """获取模型名称"""
    if not file_path or not os.path.exists(file_path):
        log.error(f"file_path not found! file_path: {file_path}")
        return None
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()
    result = re.findall("class ([a-zA-Z0-9_]+)", content)
    return result[-1] if result else None


def get_gen_agent_rpc_path(device_id: Optional[int] = None) -> Path:
    """获取代码生成路径"""
    if device_id:
        return Path.joinpath(GEN_PATH, f"agent_rpc_{device_id}")
    return GEN_AGENT_RPC_PATH


@need_login
def gen_agent_rpc(device_id: Optional[int] = None):
    """
    生成 Agent RPC 代码
    Args:
        device_id: 设备ID，如果提供，则生成路径为 agent_rpc_<device_id>
    """
    generator = AgentRPCGenerator(device_id)
    generator.generate()
    log.success("🎉 gen_agent_rpc success!")


def _safe_str(value: Any) -> str:
    """安全地将值转换为字符串，避免破坏文档字符串"""
    if isinstance(value, str):
        # 使用双引号包裹字符串，避免转义问题
        return '"' + value.replace('"', "'") + '"'
    return str(value)


if __name__ == "__main__":
    gen_agent_rpc()
