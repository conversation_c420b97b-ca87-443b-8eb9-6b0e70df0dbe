from typing import Union, Optional

from adbutils._device import AdbDevice

from ccontrol_ai_service.agent.ai_agent import AIAgent
from ccontrol_common.core.log import log
from ccontrol_test_engine.service.driver.enhance import Core
from ccontrol_test_engine.service.driver.enhance.event import Event


class CControlDriver(Core):
    core: Core
    ai: AIAgent
    event: Event

    def __init__(
        self,
        serial: Union[str, AdbDevice],
        device_id: int,
        agent_id: str,
        task_id: Optional[str] = None,
    ):
        super().__init__(
            serial=serial,
            device_id=device_id,
            agent_id=agent_id,
            task_id=task_id,
        )
        self.core = self
        self.ai = AIAgent(self.core)
        self.event = Event(self.core)


if __name__ == "__main__":
    driver = CControlDriver(
        serial="172.19.96.155:5555",
        device_id=1001,
        agent_id="WFTYbEvhMkwtTXGUNA3DW",
    )
    d = driver.adb_connect()

    image = driver.screenshot()
    if image:
        image.show()
    log.info(f"🚀 image: {image}")

    driver.close()

    print(f"d.info: {d.info}")
    # driver.ai.vqa("是否在系统信息页面？")
    # result = driver.ai.grounding("Settings")
    # result = driver.ai.grounding("Settings", box_color=BoxLabelColorEnum.RED)
    # result = driver.ai.grounding("Settings", is_crop=True)
