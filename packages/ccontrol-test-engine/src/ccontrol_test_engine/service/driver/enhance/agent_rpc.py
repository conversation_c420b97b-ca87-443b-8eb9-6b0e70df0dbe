import asyncio
import json
import threading
from asyncio import Future
from enum import Enum
import time
from typing import Optional, Any
from uuid import uuid4
from json import JSONDecodeError

from livekit import rtc
from pydantic import BaseModel, ValidationError

from ccontrol_common.core.log import log, escape_color_tags
from ccontrol_common.core.context_manager import LogContextManager
from ccontrol_common.config.conf import settings
from ccontrol_common.core.http_client import do_get

CONNECT_TIMEOUT = 10
DEFAULT_TIMEOUT = 10
AGENT_RPC_TOPIC = "agent-rpc"
API_TOKEN = "/api/livekit/token"


class TokenModel(BaseModel):
    accessToken: str
    identity: str


class ActionEnum(str, Enum):
    RPC_REQUEST = "rpc_request"
    RPC_RESPONSE = "rpc_response"


class AgentRPCErrorModel(BaseModel):
    code: int
    message: str


class AgentRPCException(Exception):
    """Exception raised for RPC errors."""

    def __init__(self, error_model: AgentRPCErrorModel):
        super().__init__(error_model.message)
        self.code = error_model.code
        self.error = error_model


class AgentRPCModel(BaseModel):
    id: str
    action: ActionEnum
    method: Optional[str] = None
    data: Optional[dict] = None
    error: Optional[AgentRPCErrorModel] = None


def get_access_token(agent_id: str):
    if not agent_id:
        raise AssertionError("agent_id 不能为空")
    resp = do_get(
        settings.ZEUS_DOMAIN + API_TOKEN,
        params={"roomName": f"agent-{agent_id}"},
    )
    if not resp.is_success:
        raise AssertionError("获取 token 失败")
    resp = TokenModel.model_validate(resp.json())
    return resp.accessToken


class AgentRPCService(threading.Thread):
    """Agent RPC 服务"""

    def __init__(self, agent_id: str):
        super().__init__()
        self.agent_id = agent_id
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.room: Optional[rtc.Room] = None
        self._connected = False
        self._stop_event = threading.Event()
        self.future_map: dict[str, Future] = {}
        self.is_first_publish: bool = True  # 添加首次发布标志
        self.context_manager = LogContextManager()

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected and not self._stop_event.is_set()

    def wait_connected(self):
        """等待连接建立"""
        start_time = time.time()
        while not self._connected:
            if time.time() - start_time > CONNECT_TIMEOUT:
                return False
            time.sleep(0.1)
        return True

    async def wait_publish_ready(self):
        """等待发布就绪"""
        start_time = time.time()
        timeout = 60  # 60秒超时
        while True:
            if self._connected and self.room and hasattr(self.room, "local_participant") and self.room.local_participant:
                break
            if time.time() - start_time > timeout:
                log.error("[AgentRPC] wait_publish_ready timeout")
                break
            await asyncio.sleep(0.1)

        if self.is_first_publish and self.room and self.room.local_participant:
            self.is_first_publish = False
            # Magic特性：ping 一下后面就能发送消息了
            await self.room.local_participant.publish_data("ping", topic=AGENT_RPC_TOPIC)
            # 确定性时延，3 秒后 data channel 必定能正常通信
            await asyncio.sleep(3)

    async def publish(self, data: str):
        """发布数据"""
        await self.wait_publish_ready()
        if not self.room or not self.room.local_participant:
            raise AssertionError("Room not connected")
        log.info(f"[AgentRPC] publish data: {data}")
        await self.room.local_participant.publish_data(data, topic=AGENT_RPC_TOPIC)

    async def cleanup(self):
        """清理资源"""
        if self.room:
            await self.room.disconnect()

    def stop(self):
        """停止服务"""
        if self.loop and self.loop.is_running():
            future = asyncio.run_coroutine_threadsafe(self.cleanup(), self.loop)
            future.result()  # 等待cleanup完成
        self._stop_event.set()
        if self.loop:
            self.loop.call_soon_threadsafe(self.loop.stop)
        self.join()

    def run(self):
        """运行服务"""
        context = self.context_manager.get_thread_context(str(self.ident))

        if context:
            with self.context_manager.context(context):
                self._run_with_context()
        else:
            self._run_with_context()

    def _run_with_context(self):
        """带上下文的运行逻辑"""
        log.info("AgentRPCService 启动")
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        try:
            self.loop.run_until_complete(self.main())
            self.loop.run_forever()
        finally:
            self.loop.close()

    def call(
        self,
        method: str,
        data: Optional[dict] = None,
        timeout: Optional[float] = DEFAULT_TIMEOUT,
    ):
        """
        调用远程方法

        Args:
            method (str): 方法名
            data (dict): 参数
            timeout (int): 超时时间

        Returns:
            dict: 响应结果
        """
        if not self.loop or not self.loop.is_running():
            if self._stop_event.is_set():
                raise RuntimeError("AgentRPCService 已终止")
            raise RuntimeError("AgentRPCService 尚未启动或已停止运行")

        try:
            log.info(f"[AgentRPC] 调用方法: {method}")
            future = asyncio.run_coroutine_threadsafe(self.call_async(method, data, timeout), self.loop)
            result = future.result(timeout)
            log.info(f"[AgentRPC] 调用成功: {result}")
            return result
        except RuntimeError as e:
            if "Event loop is closed" in str(e):
                # 如果事件循环关闭，重新启动服务
                log.warning("Event loop is closed, restarting service...")
                self.run()
                future = asyncio.run_coroutine_threadsafe(self.call_async(method, data, timeout), self.loop)
                return future.result(timeout)
            raise e
        except TimeoutError as e:
            error_msg = f"[AgentRPC] error: {e.__class__.__name__}"
            if str(e):
                error_msg += f", message: {str(e)}"
            log.error(error_msg)
            raise e
        except Exception as e:
            error_msg = f"[AgentRPC] error: {e.__class__.__name__}"
            if str(e):
                error_msg += f", message: {str(e)}"
            log.error(error_msg)
            raise e

    async def call_async(
        self,
        method: str,
        data: Optional[dict] = None,
        timeout: Optional[float] = DEFAULT_TIMEOUT,
    ):
        """Agent RPC"""
        if not self.loop:
            raise RuntimeError("Event loop not initialized")

        future_key = str(uuid4().hex)
        log.opt(colors=True).info(f"<cyan>[AgentRPC][REQUEST][{future_key}][{method}] request data: {escape_color_tags(str(data))}</cyan>")
        future = self.loop.create_future()
        self.future_map[future_key] = future

        # publish data
        req_data = AgentRPCModel(id=future_key, action=ActionEnum.RPC_REQUEST, method=method, data=data).model_dump_json(exclude_none=True)
        await self.publish(req_data)

        # wait for response
        try:
            resp = await asyncio.wait_for(future, timeout)
            self.safely_delete_future(future_key)
            return resp
        except TimeoutError as e:
            error_msg = f"[AgentRPC] 调用失败: {e.__class__.__name__}"
            if str(e):
                error_msg += f": {str(e)}"
            log.error(error_msg)
            self.safely_delete_future(future_key)
            raise e
        except Exception as e:
            error_msg = f"[AgentRPC] 调用失败: {e.__class__.__name__}"
            if str(e):
                error_msg += f": {str(e)}"
            log.error(error_msg)
            self.safely_delete_future(future_key)
            raise e

    def safely_delete_future(self, future_key):
        """
        安全删除future
        避免未删除导致内存泄漏
        """
        try:
            del self.future_map[future_key]
        except KeyError:
            log.warning(f"Not found future, future_key: {future_key}")

    def set_future_result(self, future_key: str, result: Any):
        """设置Future的结果"""
        if future_key in self.future_map:
            future = self.future_map[future_key]
            if not future.done():
                future.set_result(result)

    def set_future_exception(self, future_key: str, error: AgentRPCErrorModel):
        """设置Future的异常"""
        if future_key in self.future_map:
            future = self.future_map[future_key]
            if not future.done():
                future.set_exception(AgentRPCException(error))

    async def main(self) -> None:
        """主要的异步运行逻辑"""
        self.room = rtc.Room(loop=self.loop)

        @self.room.on("participant_connected")
        def on_participant_connected(participant: rtc.RemoteParticipant) -> None:
            log.info(f"[AgentRPC] participant connected: {participant.sid} {participant.identity} {participant.metadata}")

        @self.room.on("participant_disconnected")
        def on_participant_disconnected(participant: rtc.RemoteParticipant):
            log.info(f"[AgentRPC] participant disconnected: {participant.sid} {participant.identity} {participant.metadata}")

        @self.room.on("data_received")
        def on_data_received(data: rtc.DataPacket):
            self.handle_recv_message(data)

        @self.room.on("connection_state_changed")
        def on_connection_state_changed(state: rtc.ConnectionState):
            if state == rtc.ConnectionState.CONN_CONNECTED:
                self._connected = True
                log.info(f"[AgentRPC] connection state changed: {state}, is_connected: {self._connected}")
            else:
                self._connected = False
                log.info(f"[AgentRPC] connection state changed: {state}, is_connected: {self._connected}")

        token = get_access_token(self.agent_id)
        await self.room.connect(settings.LIVEKIT_URL, token)
        log.info(f"[AgentRPC] connected to room {self.room.name}")
        log.info("[AgentRPC] participants: " + ", ".join([json.dumps({"identity": p.identity, "metadata": p.metadata}) for p in self.room.remote_participants.values()]))

    def handle_recv_message(self, data: rtc.DataPacket):
        """处理接收到的消息"""
        if data.topic == AGENT_RPC_TOPIC:
            decode_msg = data.data.decode("utf-8")
            log.info(f"[AgentRPC] handle_recv_message data: {decode_msg}")
            if decode_msg in ("ping", "pong", '"ping"', '"pong"'):
                return

            try:
                resp = AgentRPCModel.model_validate_json(decode_msg)
            except ValidationError:
                try:
                    resp = json.loads(decode_msg)
                except JSONDecodeError as e:
                    log.error(f"[AgentRPC] JSONDecodeError, cannot set future exception: {e}")
                    return

                future_key = resp.get("id")
                if not future_key:
                    log.error(f"[AgentRPC] cannot read future_key, decode_msg: {decode_msg}")
                    return

                self.set_future_exception(future_key, AgentRPCErrorModel(code=500, message="Received message is not valid, decode_msg: %s" % decode_msg))
                return

            if resp.action != ActionEnum.RPC_RESPONSE or resp.id not in self.future_map:
                return

            if resp.error:
                log.opt(colors=True).error(f"<red>[AgentRPC][RESPONSE][{resp.id}] code: {resp.error.code}, message: {escape_color_tags(resp.error.message)}</red>")
                self.set_future_exception(resp.id, resp.error)
                return

            log.opt(colors=True).info(f"<green>[AgentRPC][RESPONSE][{resp.id}] response data: {escape_color_tags(str(resp.data))}</green>")
            self.set_future_result(resp.id, resp.data)

    def set_thread_name(self, name: Optional[str] = None):
        """设置线程名：后续可根据线程名进行日志过滤"""
        if name:
            self.name = name


if __name__ == "__main__":
    from ccontrol_test_engine.mock.global_var import mock_global_var

    # 使用示例
    global_var = mock_global_var(settings.DEVICE_ID)
    agent_service = AgentRPCService(global_var.agent.id)
    agent_service.start()
    agent_service.wait_connected()

    log.info("AgentRPCService 已成功启动并连接")

    try:
        log.info("🚀 call")
        _result = agent_service.call("screenshot", {})
        log.info(f"result: {_result}")
    finally:
        agent_service.stop()
        log.info("程序已正常终止")
