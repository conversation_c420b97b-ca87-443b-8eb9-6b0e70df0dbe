import importlib
import time
from io import BytesIO
from typing import Optional, Union

from ccontrol_common.config.conf import settings
from ccontrol_common.core.http_client import do_get

from adbutils import AdbError
from adbutils._device import AdbDevice
from httpcore import ConnectError
from PIL.Image import Image
from PIL.Image import open as open_image
from uiautomator2 import Device

from ccontrol_common.core.log import log
from ccontrol_test_engine.service.driver.enhance.agent_rpc import AgentRPCService
from ccontrol_test_engine.service.driver.enhance.exceptions import ScreenshotError
from ccontrol_ai_service.interface.device import InteractiveDeviceInterface


class Core(InteractiveDeviceInterface):
    """核心功能类"""

    serial: Union[str, AdbDevice]

    u2_device: Optional[Device] = None

    agent_rpc_thread: AgentRPCService

    def __init__(
        self,
        serial: Union[str, AdbDevice],
        device_id: int,
        agent_id: str,
        task_id: Optional[str] = None,
    ):
        """
        :param serial: serial (str): Android device serial number
        :param device_id: Device ID
        :param agent_id: Agent ID
        :param task_id: Task ID (optional)
        :return Device
        :raise ConnectError
        :example
            connect("*************:5555")  # adb connect ip:port
            connect("emulator-5554")  # adb device serial number
        """
        self.serial = serial
        self.device_id = device_id
        self.agent_rpc = self.dynamic_agent_rpc()
        self.start_agent_rpc_thread(agent_id, task_id)

    def dynamic_agent_rpc(self):
        """
        动态引用 AgentRPC 模块

        根据运行模式动态导入不同的 AgentRPC 模块:
        - service 模式: 导入 gen.agent_rpc_{device_id}
        - user 模式: 导入 gen.agent_rpc

        动态导入的目的是避免在没有生成 AgentRPC 代码时就导入该模块而产生异常

        Returns:
            AgentRPC: AgentRPC 实例
        """
        log.info(f"settings.RUN_MODE: {settings.RUN_MODE}")
        if settings.RUN_MODE == "service":
            agent_module = importlib.import_module(f"gen.agent_rpc_{self.device_id}")
        else:
            agent_module = importlib.import_module("gen.agent_rpc")
        return agent_module.AgentRPC()

    def adb_connect(self, timeout=60):
        """
        使用ADB连接到Android设备

        如果设备尚未连接，此方法将尝试连接。
        如果设备已经连接，它将验证连接。

        :raises ConnectError: 如果连接失败或无法检索设备信息。
        :return: 已连接的Device对象。
        """
        if self.u2_device is None:
            try:
                self.u2_device = Device(self.serial)
                log.success(f"device_info: {self.u2_device.info}")
                log.success("ADB连接成功")
            except AdbError as e:
                log.error(f"Failed to connect to device: {self.serial}, error: {e}")
                raise ConnectError(f"Failed to connect to device: {self.serial}, error: {e}") from e
        else:
            # 二次验证
            try:
                log.info("正在验证ADB连接情况...")
                self.u2_device._wait_for_device()
                try:
                    log.info(f"device_info: {self.u2_device.info}")
                except AdbError:
                    # 可能在重启过程中
                    sleep_time = 15
                    log.warning(f"设备连接异常，{sleep_time}秒后开始重连，重连超时时间为{timeout}秒")
                    time.sleep(sleep_time)
                    self.u2_device._wait_for_device(timeout=timeout)
                    log.info(f"device_info: {self.u2_device.info}")
                log.success("ADB已连接")
            except Exception as e:
                log.exception(f"Failed to get device info: {e}")
                raise ConnectError(f"ADB连接失败: {e}") from e

        return self.u2_device

    def start_agent_rpc_thread(self, agent_id: str, task_id: Optional[str] = None):
        self.agent_rpc_thread = AgentRPCService(agent_id)
        self.agent_rpc_thread.set_thread_name(task_id)
        self.agent_rpc_thread.start()
        self.agent_rpc_thread.wait_connected()

    def close(self):
        self.agent_rpc_thread.stop()

    def get_screenshot_url(self) -> str:
        """获取截图下载链接"""
        # Agent RPC screenshot
        download_url = self.agent_rpc_thread.call("screenshot", {}).get("file_download_url")

        if not download_url or not isinstance(download_url, str):
            raise ScreenshotError("Screenshot failed, download url not found")

        return download_url

    def screenshot_by_agent_rpc(self) -> Image:
        """通过 Agent RPC 获取截图"""
        try:
            # Agent RPC screenshot
            download_url = self.get_screenshot_url()

            # 下载截图
            resp_image = do_get(download_url, log_resp_len=0)
            image_data = BytesIO(resp_image.content)
            image = open_image(image_data)
            return image
        except Exception as e:
            error_msg = f"[AgentRPC] Screenshot by agent RPC failed, error: {e.__class__.__name__}"
            if str(e):
                error_msg += f", message: {str(e)}"
            raise ScreenshotError(error_msg) from e

    def screenshot(self) -> Image:
        """截图"""
        return self.screenshot_by_agent_rpc()

    def sync_global_var(self):
        """同步全局变量"""
        # FIXME: langgraph app.invoke 是在多线程环境下工作，需要做线程变量同步，临时措施
        from ccontrol_test_engine import GlobalStore

        GlobalStore().set_driver(self)

    def click(self, x: int, y: int) -> None:
        """点击指定坐标

        Args:
            x: 横坐标
            y: 纵坐标

        Raises:
            NotImplementedError: 点击操作失败时抛出
        """
        # FIXME: langgraph app.invoke 是在多线程环境下工作，需要做线程变量同步，临时措施
        self.sync_global_var()

        if self.u2_device:
            return self.u2_device.double_click(x, y)

        if hasattr(self.agent_rpc, "hid_mouse_abs_click"):
            return self.agent_rpc.hid_mouse_abs_click(x=x, y=y, key="left", times=2)

        # TODO: IR 模式

        raise NotImplementedError("Click failed, not implemented!")
