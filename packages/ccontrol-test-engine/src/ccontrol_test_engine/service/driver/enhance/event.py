from typing import Optional, cast
import sys
import types
import traceback

from ccontrol_common.core import log
from ccontrol_common.core.errors import PythonExecuteError, SyntaxCheckError
from ccontrol_common.services.ccontrol_service import get_python_event_code
from ccontrol_test_engine.service.task.code_security import (
    check_code_security,
    remove_unsafe_code,
)
from ccontrol_test_engine.service.driver.enhance.core import Core

from pydantic import BaseModel


class PythonExecuteResult(BaseModel):
    """Python代码执行结果"""

    success: bool
    error_message: Optional[str] = None
    filtered_code: Optional[str] = None


class Event:
    """事件处理模块"""

    core: Core

    def __init__(self, core: Core):
        self.core = core

    def _format_error_context(self, code: str, error_line: int) -> str:
        """格式化错误上下文信息"""
        code_lines = code.splitlines()
        total_lines = len(code_lines)

        # 计算显示范围（错误行上下各显示2行）
        start_line = max(1, error_line - 2)
        end_line = min(total_lines, error_line + 2)

        # 构建上下文信息
        context_lines = []
        for i in range(start_line - 1, end_line):
            line_marker = "👉" if i + 1 == error_line else "  "
            context_lines.append(
                f"{line_marker} {str(i + 1).rjust(3)}: {code_lines[i]}"
            )
        return "\n".join(context_lines)

    def execute_python_code(self, code: str) -> PythonExecuteResult:
        """执行Python代码

        Args:
            code: Python代码字符串

        Returns:
            PythonExecuteResult: 执行结果
        """
        # 检查代码安全性
        security_result = check_code_security(code)

        # 如果有完全禁止的错误，直接返回
        if security_result.has_forbidden_errors:
            error_msg = "🚀 代码安全检查失败:\n" + "\n".join(
                security_result.forbidden_errors
            )
            return PythonExecuteResult(success=False, error_message=error_msg)

        # 处理代码安全性
        exec_code = code
        filtered_code = None
        if security_result.has_warnings:
            log.warning(
                "检测到不安全代码，尝试移除:["
                + ", ".join(security_result.warning_errors)
                + "]"
            )
            filtered_code = remove_unsafe_code(code)
            if filtered_code is None:
                error_msg = "🚀 无法处理不安全代码"
                return PythonExecuteResult(success=False, error_message=error_msg)
            exec_code = filtered_code

        try:
            # 先编译代码以捕获语法错误
            try:
                compiled_code = compile(exec_code, "<string>", "exec")
            except SyntaxError as se:
                error_context = self._format_error_context(exec_code, se.lineno or 1)
                raise SyntaxCheckError(
                    f"语法错误: {str(se)}\n代码上下文:\n{error_context}"
                ) from se

            # 执行编译后的代码
            # pylint: disable=exec-used
            exec(compiled_code, {"log": log})

            return PythonExecuteResult(
                success=True,
                filtered_code=filtered_code if security_result.has_warnings else None,
            )

        except Exception as e:
            # 获取完整的错误信息
            exc_type, exc_value, exc_tb = sys.exc_info()
            if exc_tb is None:
                raise PythonExecuteError(
                    error_msg=f"运行代码出错: {str(e)}。⚠️ 无法获取错误追踪信息"
                ) from e

            # 获取最后一个错误帧
            tb = cast(types.TracebackType, exc_tb)
            while tb.tb_next is not None:
                tb = tb.tb_next

            # 获取错误行号和格式化错误信息
            error_line = tb.tb_lineno
            error_context = self._format_error_context(exec_code, error_line)

            # 获取完整的错误追踪信息
            tb_info = "".join(traceback.format_exception(exc_type, exc_value, exc_tb))

            raise PythonExecuteError(
                error_msg=f"运行时错误: {str(e)}\n\n调用栈信息:\n{tb_info}",
                error_line=error_line,
                code_context=error_context,
            ) from e

    def execute_python_event_by_id(self, event_id: str) -> PythonExecuteResult:
        """通过事件ID执行Python代码"""
        code = get_python_event_code(event_id)
        return self.execute_python_code(code)

    def execute_python_event_by_method(self, event_name: str) -> PythonExecuteResult:
        """通过方法名执行Python代码"""
        code = get_python_event_code(method=event_name)
        return self.execute_python_code(code)
