[project]
name = "ccontrol-test-engine"
description = "CControl Test Engine"
authors = [{ name = "<PERSON><PERSON><PERSON><PERSON><PERSON>", email = "lian<PERSON><PERSON><PERSON>@cvte.com" }]
dynamic = ["version"]
dependencies = [
    "fastapi<1.0.0,>=0.111.0",
    "uvicorn<1.0.0,>=0.29.0",
    "datamodel-code-generator<1.0.0,>=0.25.6",
    "uiautomator2<4.0.0,>=3.2.2",
    "livekit<1.0.0,>=0.12.1",
    "ccontrol-common",
    "ccontrol-ai-service",
]
requires-python = "<4,>=3.9"
readme = "README.md"
license = "MIT"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
include = ["ccontrol_test_engine*"]
exclude = ["*.tests*", "*.testing*", "*__pycache__*"]

[tool.setuptools.dynamic]
version = { attr = "ccontrol_test_engine.__version__" }

[tool.ruff]
line-length = 255

[tool.ruff.lint]
select = ["E", "F"]

[build-system]
requires = ["setuptools>=78.1.0"]
build-backend = "setuptools.build_meta"

[dependency-groups]
dev = [
    "pytest>=8.3.4",
]
