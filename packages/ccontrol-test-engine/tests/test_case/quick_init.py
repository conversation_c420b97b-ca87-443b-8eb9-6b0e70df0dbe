import time

from pydantic import BaseModel

from ccontrol_test_engine import GlobalStore, quick_init, log
from gen.agent_rpc import AgentRPC

# 必要：快速初始化
quick_init()

# 预置代码，方便编写用例
global_var = GlobalStore().get_global_var()
driver = GlobalStore().get_driver()
ai = driver.ai
agent_rpc: AgentRPC = driver.agent_rpc


# ==============================================
# =============== 测试用例编写区域 ===============
# ==============================================


# 结构化信息提取
class FactoryInfo(BaseModel):
    board: str
    version: str
    build_time: str
    mac: str
    ip: str
    sn: str


# TODO: 在此处编写自动化测试用例
try:
    driver.adb_connect()

    # 回到主页（初始化测试环境）
    # agent_rpc.remote_by_name(keyName="HOME")
    # time.sleep(1)
    # agent_rpc.remote_by_name(keyName="HOME")

    # # =============== 测试结构化信息提取能力 ===============

    # agent_rpc.remote_by_name(keyName="F2")
    # time.sleep(3)

    # result = ai.query("提取左侧工厂菜单信息", response_format=FactoryInfo)
    # log.info(f"🚀 result: {result}")

    # agent_rpc.remote_by_name(keyName="F2")
    # time.sleep(1)

    # =============== 测试 AI Agent 能力 ===============
    # 回到主页（初始化测试环境）
    agent_rpc.remote_by_name(keyName="HOME")
    time.sleep(1)
    agent_rpc.remote_by_name(keyName="HOME")

    ai.action("打开电视管家")
    time.sleep(5)

    ai.action("进入网络测试")
    time.sleep(5)

    ai.assert_true("当前网络正常？")
except Exception as e:
    # 必要：执行异常时关闭 driver
    driver.close()
    log.exception(f"测试用例执行失败: {e}")
    raise e

# ==============================================
# =============== 测试用例编写区域 ===============
# ==============================================


# 必要：关闭 driver
driver.close()
