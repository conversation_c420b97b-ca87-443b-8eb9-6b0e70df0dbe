"""
示例测试用例
测试内容：进入设置-系统信息，获取Device Name
"""

import time


from ccontrol_common.core.log import log
from ccontrol_test_engine.service.task.model.global_model import GlobalStore

driver = GlobalStore().get_driver()
ai = driver.ai
d = driver.adb_connect()

log.info("Start test case 001")

log.info(f"global_store: {GlobalStore().get_store().__dict__}")
log.info(f"device info: {d.info}")

# 启动设置应用
app_session = d.session("com.cvte.settings")

# 进入系统信息，点击第一下是把焦点放在目标上，第二下是确认进入
app_session(text="Common").click()

# ✨ 使用 Grounding 定位目标
center_point = ai.grounding_find_first_center_point("Common")
assert center_point is not None, "Grounding 定位失败"
d.click(center_point[0], center_point[1])
time.sleep(1)


# ✨ 使用 VQA 做断言
assert ai.vqa("当前是不是Common页面？")

# 滑动到底部
for i in range(3):
    app_session.swipe_ext("up")

# ✨ VQA 断言异常示例
assert ai.vqa("火星人在弹吉他？"), "断言异常：模型不理解这个问题"

# 方式一：找到 text 为 The Device Name 的元素，找到兄弟元素最后一个，获取 Device Name
device_name = (
    app_session(text="The Device Name")
    .sibling(className="android.widget.TextView")[-1]
    .get_text()
)
log.info(f"Device Name: {device_name}")

# 方式二：点击 The Device Name，把焦点框设置在 Device Name 上，获取  Device Name
# app_session(text="The Device Name").click()
# device_name = app_session(focused=True).child()[-1].get_text()
# log.info(f"Device Name: {device_name}")

# 退出设置应用
app_session.close()

log.info("End test case 001")
