from ccontrol_test_engine.service.task.model.global_model import GlobalStore
from ccontrol_common.core.log import log

driver = GlobalStore().get_driver()
d = driver.u2_device
ai = driver.ai

resp = driver.screenshot()
log.info(f"🚀 resp 1: {resp}")

resp = driver.agent_rpc_thread.call("screenshot", {})
log.info(f"🚀 resp 2: {resp}")

agent_rpc = driver.agent_rpc
resp = agent_rpc.do_screenshot({})
log.info(f"🚀 resp 3: {resp}")
