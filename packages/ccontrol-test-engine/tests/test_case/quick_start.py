from ccontrol_common.core.log import log
from ccontrol_test_engine.service.task.model.global_model import GlobalStore
from ccontrol_test_engine.service.task.runner import quick_start


def main():
    driver = GlobalStore().get_driver()
    ai = driver.ai
    agent_rpc = driver.agent_rpc

    # ============== 测试用例 ==============

    d = driver.adb_connect()
    log.info(f"device info: {d.info}")
    log.info(f"ai: {ai}")
    log.info(f"agent_rpc: {agent_rpc}")

    # 启动设置应用
    app_session = d.session("com.cvte.settings")
    log.info(f"app_session: {app_session}")


if __name__ == "__main__":
    # 这种方式运行方式不可以打断点，调试不方便
    quick_start(main)
