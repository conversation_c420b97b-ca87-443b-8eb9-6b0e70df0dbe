import os

from ccontrol_common.config.conf import settings
from ccontrol_test_engine.mock.global_var import mock_global_var
from ccontrol_test_engine.mock.task import mock_task
from ccontrol_test_engine.service.task.runner import TaskRunner

if __name__ == "__main__":
    global_var = mock_global_var(settings.DEVICE_ID)
    test_case_path = os.path.join(
        os.path.dirname(os.path.abspath(__file__)), "test_case", "case_001.py"
    )
    task_data = mock_task(test_case_path)

    # task_data = mock_task_print_hello_world()
    TaskRunner(global_var=global_var, task_data=task_data).run()
