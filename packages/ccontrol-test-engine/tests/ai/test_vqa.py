from typing import Optional
from pydantic import BaseModel
import pytest

from ccontrol_ai_service.common.constans import VQAModelEnum
from ccontrol_common.core.log import log
from ccontrol_test_engine.service.task.model.global_model import GlobalStore
from ccontrol_test_engine.service.task.runner import quick_init


class TVInfo(BaseModel):
    board: str
    build_time: str
    # 特殊情况，实际有换行
    panel: str
    mac: str
    ip: str
    upgrade_name: str
    # 异常情况，实际为空
    sn: Optional[str]
    power_time: str
    # 异常情况，实际为空，根本没有这个条目
    button_status: Optional[str]


@pytest.fixture(scope="module")
def init_driver():
    quick_init()


def test_vqa(init_driver):
    """
    测试VQA
    需要在工厂菜单页面下测试
    """
    driver = GlobalStore().get_driver()
    ai = driver.ai

    result = ai.vqa("当前画面是在工厂菜单页面吗？", model=VQAModelEnum.OPENAI_LATEST)
    log.info(f"🚀 result: {result}")
    assert result is True

    result = ai.vqa("当前画面是在工厂菜单页面吗？", model=VQAModelEnum.GEMINI_LATEST)
    log.info(f"🚀 result: {result}")
    assert result is True


def test_vqa_object(init_driver):
    """
    测试VQA结构化输出能力
    需要在工厂菜单页面下测试
    """
    driver = GlobalStore().get_driver()
    ai = driver.ai

    result = ai.vqa_object(
        "提取左侧工厂菜单的信息",
        response_format=TVInfo,
        model=VQAModelEnum.OPENAI_LATEST,
    )
    log.info(f"🚀 result: {result}")

    result = ai.vqa_object(
        "提取左侧工厂菜单的信息",
        response_format=TVInfo,
        model=VQAModelEnum.GEMINI_LATEST,
    )
    log.info(f"🚀 result: {result}")
