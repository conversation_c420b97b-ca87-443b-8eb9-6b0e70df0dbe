import pytest

from ccontrol_test_engine.service.task.model.global_model import GlobalStore
from ccontrol_test_engine.service.task.runner import quick_init
from ccontrol_common.core.log import log


@pytest.fixture(scope="module")
def init_driver():
    quick_init()
    yield
    driver = GlobalStore().get_driver()
    driver.close()


def test_ocr(init_driver):
    """
    测试OCR
    """
    driver = GlobalStore().get_driver()
    ai = driver.ai

    result = ai.ocr()
    log.info(f"🚀 result: {result}")

    result = ai.ocr_find_first_by_text("Apps")
    log.info(f"🚀 result: {result}")

    result = ai.ocr_find_first_by_regex("Apps")
    log.info(f"🚀 result: {result}")

    # assert result is True
