import pytest
from ccontrol_common.core.log import log
from ccontrol_test_engine.mock.global_var import mock_global_var
from ccontrol_test_engine.mock.task import mock_task_print_hello_world
from ccontrol_test_engine.service.driver.enhance.agent_rpc import AgentRPCService
from ccontrol_test_engine.service.task.runner import TaskRunner

DEVICE_ID = 102


@pytest.fixture(scope="module")
def global_var():
    return mock_global_var(DEVICE_ID)


@pytest.fixture(scope="module")
def task_data():
    return mock_task_print_hello_world()


def test_agent_rpc_execution(global_var):
    """测试AgentRPC服务"""
    agent_service = AgentRPCService(global_var.agent.id)
    agent_service.start()
    agent_service.wait_connected()

    try:
        log.info("🚀 调用截图功能")
        result = agent_service.call("screenshot", {})
        log.info(f"🚀 截图结果: {result}")
        assert result is not None, "截图结果不应为空"
    finally:
        agent_service.stop()
        log.info("AgentRPC服务已停止")


def test_agent_rpc_execution_with_thread_run(global_var, task_data, caplog):
    """
    测试AgentRPC服务
    测试点：检查 threading.Thread.run 启动的 时候，LiveKit Room 能否随主线程退出
    """
    task_runner = TaskRunner(global_var=global_var, task_data=task_data)
    task_runner.run()
    assert "Event loop is closed" not in caplog.text


def test_agent_rpc_execution_with_thread_start(global_var, task_data, caplog):
    """
    测试AgentRPC服务
    测试点：检查 threading.Thread.start 启动的时候，LiveKit Room 能否随主线程退出
    """
    task_runner = TaskRunner(global_var=global_var, task_data=task_data)
    task_runner.start()
    task_runner.join()
    assert "Event loop is closed" not in caplog.text
