import threading
import time

import pytest

from ccontrol_common.core import log
from ccontrol_test_engine import GlobalStore
from ccontrol_test_engine.mock.global_var import mock_global_var

DEVICE_ID_1 = 90
DEVICE_ID_2 = 91


@pytest.fixture(scope="module")
def global_var(device_id):
    return mock_global_var(device_id)


def test_global_model_execution():
    """测试threading.local变量隔离"""

    def echo_threading_local_var(thread_name: str, device_id: int):
        GlobalStore().set_global_var(mock_global_var(device_id))

        log.info(
            f"\n{thread_name},\n\t {GlobalStore().get_global_var().device.id},\n\t {id(GlobalStore().get_global_var())},\n\t {GlobalStore().get_global_var()}\n"
        )
        time.sleep(3)
        log.info(
            f"\n{thread_name},\n\t {GlobalStore().get_global_var().device.id},\n\t {id(GlobalStore().get_global_var())},\n\t {GlobalStore().get_global_var()}\n"
        )

    threading.Thread(
        target=echo_threading_local_var, args=("thread_1", DEVICE_ID_1)
    ).start()
    time.sleep(1)
    threading.Thread(
        target=echo_threading_local_var, args=("thread_2", DEVICE_ID_2)
    ).start()
    time.sleep(4)  # wait thread end
