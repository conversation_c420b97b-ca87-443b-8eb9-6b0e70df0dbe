import pytest
from ccontrol_test_engine.service.task.code_security import (
    check_code_security,
    remove_unsafe_code,
    CodeSecurityConfig,
)
from ccontrol_common.core.errors import (
    ForbiddenImportError,
    RestrictedImportError,
    ForbiddenFunctionError,
    RestrictedFunctionError,
    ForbiddenMethodError,
)

# ===================== 测试数据 =====================

SAFE_CODE = """
def hello():
    print('Hello World')
"""

UNSAFE_IMPORTS = {
    "subprocess": "import subprocess",
    "shutil": "import shutil",
    "socket": "from socket import socket",
}

RESTRICTED_IMPORTS = {
    "os": "import os",
    "sys": "from sys import exit",
    "pathlib": "from pathlib import InvalidPath",
}

UNSAFE_CALLS = {
    "eval": "eval('1+1')",
    "exec": "exec('print(\"hello\")')",
    "__import__": "__import__('os')",
}

RESTRICTED_CALLS = {
    "open_write": "open('test.txt', 'w')",
    "open_write_mode": "open('test.txt', mode='w')",
    "input": "input('Enter your name: ')",
    "globals": "globals()",
}

FORBIDDEN_METHODS = """
from ccontrol_test_engine import quick_init, GlobalStore
quick_init()
driver = GlobalStore().get_driver()
driver.close()
"""

# ===================== 基础测试 =====================


def test_safe_code():
    """测试安全的代码"""
    result = check_code_security(SAFE_CODE)
    assert not result.has_forbidden_errors
    assert not result.has_warnings


# ===================== 导入检查测试 =====================


@pytest.mark.parametrize("module,code", UNSAFE_IMPORTS.items())
def test_forbidden_imports(module, code):
    """测试完全禁止的导入"""
    result = check_code_security(code)
    assert result.has_forbidden_errors
    assert len(result.forbidden_errors) == 1
    assert ForbiddenImportError(module).message in result.forbidden_errors[0]


@pytest.mark.parametrize("module,code", RESTRICTED_IMPORTS.items())
def test_restricted_imports(module, code):
    """测试受限制的导入"""
    result = check_code_security(code)
    assert result.has_forbidden_errors
    assert len(result.forbidden_errors) == 1
    if module == "sys":
        assert ForbiddenFunctionError("exit").message in result.forbidden_errors[0]
    else:
        assert (
            RestrictedImportError(
                module, ", ".join(CodeSecurityConfig.RESTRICTED_IMPORTS[module])
            ).message
            in result.forbidden_errors[0]
        )


@pytest.mark.parametrize(
    "code",
    [
        "from os.path import join, dirname",
        "from sys import path",
        "from pathlib import Path",
    ],
)
def test_allowed_imports(code):
    """测试允许的导入"""
    result = check_code_security(code)
    assert not result.has_forbidden_errors
    assert not result.has_warnings


# ===================== 函数调用测试 =====================


@pytest.mark.parametrize("func,code", UNSAFE_CALLS.items())
def test_forbidden_functions(func, code):
    """测试禁止的函数调用"""
    result = check_code_security(code)
    assert result.has_forbidden_errors
    assert len(result.forbidden_errors) == 1
    assert ForbiddenFunctionError(func).message in result.forbidden_errors[0]


@pytest.mark.parametrize("func,code", RESTRICTED_CALLS.items())
def test_restricted_functions(func, code):
    """测试受限制的函数调用"""
    result = check_code_security(code)
    assert result.has_forbidden_errors
    assert len(result.forbidden_errors) == 1
    if func.startswith("open"):
        assert (
            RestrictedFunctionError("open", "r, rt").message
            in result.forbidden_errors[0]
        )
    else:
        assert (
            ForbiddenFunctionError(func.split("_")[0]).message
            in result.forbidden_errors[0]
        )


@pytest.mark.parametrize(
    "code",
    [
        "with open('test.txt', 'r') as f:\n    data = f.read()",
        "with open('test.txt', mode='rt') as f:\n    data = f.read()",
    ],
)
def test_allowed_functions(code):
    """测试允许的函数调用"""
    result = check_code_security(code)
    assert not result.has_forbidden_errors
    assert not result.has_warnings


# ===================== 方法调用测试 =====================


def test_forbidden_methods():
    """测试禁止的方法调用"""
    result = check_code_security(FORBIDDEN_METHODS)
    assert result.has_warnings  # quick_init 和 driver.close 都是警告级别
    assert len(result.warning_errors) == 2  # quick_init 和 driver.close
    assert any(
        ForbiddenMethodError("quick_init").message in error
        for error in result.warning_errors
    )
    assert any(
        ForbiddenMethodError("driver.close").message in error
        for error in result.warning_errors
    )


@pytest.mark.parametrize(
    "code",
    [
        """
from ccontrol_test_engine import quick_init as qi
from ccontrol_test_engine import GlobalStore as GS
driver = GS().get_driver()
driver.close()
qi()
    """,
    ],
)
def test_forbidden_methods_with_alias(code):
    """测试使用别名的禁止方法调用"""
    result = check_code_security(code)
    assert result.has_warnings  # quick_init 和 driver.close 都是警告级别
    assert len(result.warning_errors) == 2  # quick_init 和 driver.close
    assert any(
        ForbiddenMethodError("quick_init").message in error
        for error in result.warning_errors
    )
    assert any(
        ForbiddenMethodError("driver.close").message in error
        for error in result.warning_errors
    )


# ===================== 代码移除测试 =====================


@pytest.mark.parametrize(
    "test_input,expected_in,expected_not_in",
    [
        (
            """
import os
import sys
import json
from subprocess import run
from datetime import datetime
from os.path import join
from pathlib import Path, PurePath
print('hello')
        """,
            [
                "import json",
                "from datetime import datetime",
                "from os.path import join",
                "from pathlib import Path",
                "print('hello')",
            ],
            ["import os", "import sys", "from subprocess import run", "PurePath"],
        ),
    ],
)
def test_remove_unsafe_imports(test_input, expected_in, expected_not_in):
    """测试移除不安全导入"""
    safe_code = remove_unsafe_code(test_input)
    assert safe_code is not None
    for expected in expected_in:
        assert expected in safe_code
    for not_expected in expected_not_in:
        assert not_expected not in safe_code


@pytest.mark.parametrize(
    "test_input,expected_in,expected_not_in",
    [
        (
            """
result = eval('1+1')
with open('test.txt', 'w') as f:
    data = f.write('hello')
with open('test.txt', 'r') as f:
    data = f.read()
print('hello')
        """,
            ["open('test.txt', 'r')", "print('hello')"],
            ["eval(", "open('test.txt', 'w')"],
        ),
    ],
)
def test_remove_unsafe_calls(test_input, expected_in, expected_not_in):
    """测试移除不安全函数调用"""
    safe_code = remove_unsafe_code(test_input)
    assert safe_code is not None
    for expected in expected_in:
        assert expected in safe_code
    for not_expected in expected_not_in:
        assert not_expected not in safe_code


@pytest.mark.parametrize(
    "test_input,expected_in,expected_not_in",
    [
        (
            """
from ccontrol_test_engine import quick_init, GlobalStore
quick_init()
driver = GlobalStore().get_driver()
driver.close()
print('hello')
        """,
            ["print('hello')"],  # 修改为单引号
            ["quick_init()", "driver.close()"],
        ),
    ],
)
def test_remove_forbidden_methods(test_input, expected_in, expected_not_in):
    """测试移除禁止的方法调用"""
    safe_code = remove_unsafe_code(test_input)
    assert safe_code is not None
    for expected in expected_in:
        assert expected in safe_code
    for not_expected in expected_not_in:
        assert not_expected not in safe_code


# ===================== 边界情况测试 =====================


@pytest.mark.parametrize(
    "code,expected",
    [
        ("", "\n"),
        ("# This is just a comment", "# This is just a comment"),
        ('"""This is a multiline comment"""', '"""This is a multiline comment"""'),
        (
            "def special_func():\n    print('特殊字符：🌟')",
            "def special_func():\n    print('特殊字符：🌟')",
        ),
    ],
)
def test_edge_cases(code, expected):
    """测试边界情况"""
    result = check_code_security(code)
    assert not result.has_forbidden_errors
    assert not result.has_warnings
    result = remove_unsafe_code(code)
    assert result is not None
    assert expected in result


def test_syntax_error():
    """测试语法错误的代码"""
    code = "def test("
    result = check_code_security(code)
    assert result.has_forbidden_errors
    assert len(result.forbidden_errors) == 1
    assert any("代码语法错误" in error for error in result.forbidden_errors)
    assert remove_unsafe_code(code) is None


# ===================== 复杂场景测试 =====================


@pytest.mark.parametrize(
    "code",
    [
        """
import os.path
from os.path import join as path_join
from sys import path as sys_path, platform
from pathlib import Path as PathClass
    """,
    ],
)
def test_nested_imports(code):
    """测试嵌套导入场景"""
    result = check_code_security(code)
    assert not result.has_forbidden_errors
    assert not result.has_warnings


@pytest.mark.parametrize(
    "code",
    [
        """
with open('test.txt', mode='r') as f1, open('test2.txt', 'rt') as f2:
    data1 = f1.read()
    data2 = f2.read()

def complex_func():
    try:
        return open('test3.txt', mode='r').read()
    except:
        return open('test4.txt', 'rt').read()
    """,
    ],
)
def test_complex_function_calls(code):
    """测试复杂的函数调用场景"""
    result = check_code_security(code)
    assert not result.has_forbidden_errors
    assert not result.has_warnings


def test_mixed_scenarios():
    """测试混合场景"""
    code = """
import json
from os.path import join, dirname
from pathlib import Path
from subprocess import run
from sys import path

def mixed_function():
    eval('1+1')
    with open('test.txt', 'r') as f:
        data = f.read()
    with open('test2.txt', 'w') as f:
        f.write('hello')
    """
    result = check_code_security(code)
    assert result.has_forbidden_errors
    assert len(result.forbidden_errors) == 3
    assert any(
        ForbiddenImportError("subprocess").message in error
        for error in result.forbidden_errors
    )
    assert any(
        ForbiddenFunctionError("eval").message in error
        for error in result.forbidden_errors
    )
    assert any(
        RestrictedFunctionError("open", "r, rt").message in error
        for error in result.forbidden_errors
    )

    safe_code = remove_unsafe_code(code)
    assert safe_code is not None
    assert "import json" in safe_code
    assert "from os.path import join, dirname" in safe_code
    assert "from pathlib import Path" in safe_code
    assert "subprocess" not in safe_code
    assert "eval" not in safe_code
    assert "open('test2.txt', 'w')" not in safe_code
    assert "open('test.txt', 'r')" in safe_code
