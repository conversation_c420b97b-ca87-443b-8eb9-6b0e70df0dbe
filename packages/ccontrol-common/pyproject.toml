[project]
name = "ccontrol-common"
description = "CControl Common"
authors = [{ name = "<PERSON><PERSON><PERSON><PERSON><PERSON>", email = "l<PERSON><PERSON><PERSON><PERSON>@cvte.com" }]
dynamic = ["version"]
dependencies = [
    # 兼容 win7, openai-python 1.40.0 版本会引入 jiter，jiter 不支持 win7
    "openai>=1.30.0,<1.40.0; python_version < '3.9'",
    "openai>=1.40.0,<2; python_version >= '3.9'",
    # 兼容 win7, 避免 DLL 异常
    "pydantic>=2.6.0,<=2.7.0; python_version < '3.9'",
    "pydantic>=2.7.1,<3; python_version >= '3.9'",
    # ================================================
    "loguru<1.0.0,>=0.7.2",
    "path<17.0.0,>=16.14.0",
    "pydantic-settings<3.0.0,>=2.2.1",
    "toml<1.0.0,>=0.10.2",
    "requests<3.0.0,>=2.32.3",
    "httpx<1.0.0,>=0.27.2",
    "pillow<11.0.0,>=10.4.0",
    "fastapi>=0.111.0",
]
requires-python = "<4,>=3.8"
readme = "README.md"
license = "MIT"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
include = ["ccontrol_common*"]
exclude = ["*.tests*", "*.testing*", "*.__pycache__*"]

[tool.setuptools.dynamic]
version = { attr = "ccontrol_common.__version__" }

[build-system]
requires = ["setuptools>=78.1.0"]
build-backend = "setuptools.build_meta"

[dependency-groups]
dev = [
    "pytest>=8.3.4",
]
