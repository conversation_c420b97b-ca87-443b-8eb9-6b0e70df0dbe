from io import BytesIO
import pytest
from PIL import Image
from ccontrol_common.utils.image import ImageUtil


@pytest.fixture
def sample_pil_image():
    """创建一个示例PIL图像用于测试"""
    img = Image.new("RGB", (100, 100), color="red")
    return img


@pytest.fixture
def sample_bytes_image(sample_pil_image):
    """创建一个示例bytes格式图像用于测试"""
    img_byte_arr = BytesIO()
    sample_pil_image.save(img_byte_arr, format="PNG")
    return img_byte_arr.getvalue()


@pytest.fixture
def sample_base64_image():
    """创建一个示例base64格式图像字符串用于测试"""
    return "data:image/jpeg;base64,/9j/4AAQSkZJRg=="


class TestImageUtil:
    """ImageUtil工具类的测试用例集"""

    def test_get_type_pil_image(self, sample_pil_image):
        """测试PIL.Image类型输入"""
        assert ImageUtil.get_type(sample_pil_image) == "PIL"

    def test_get_type_bytes(self, sample_bytes_image):
        """测试bytes类型输入"""
        assert ImageUtil.get_type(sample_bytes_image) == "bytes"

    def test_get_type_base64(self, sample_base64_image):
        """测试base64格式字符串输入"""
        assert ImageUtil.get_type(sample_base64_image) == "base64"

    def test_get_type_url(self):
        """测试URL格式字符串输入"""
        url = "https://example.com/image.jpg"
        assert ImageUtil.get_type(url) == "url"

    def test_get_type_path(self):
        """测试文件路径格式字符串输入"""
        path = "/path/to/image.jpg"
        assert ImageUtil.get_type(path) == "path"

    def test_get_type_none(self):
        """测试None值输入"""
        with pytest.raises(ValueError, match="Image cannot be None or empty"):
            ImageUtil.get_type(None)  # type: ignore[arg-type]

    def test_get_type_empty_string(self):
        """测试空字符串输入"""
        with pytest.raises(ValueError, match="Image cannot be None or empty"):
            ImageUtil.get_type("")

    def test_get_type_unsupported(self):
        """测试不支持的类型输入"""
        with pytest.raises(ValueError, match="Unsupported image type"):
            ImageUtil.get_type(123)  # type: ignore[arg-type]
