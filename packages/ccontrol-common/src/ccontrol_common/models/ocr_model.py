from typing import <PERSON><PERSON>

from pydantic import BaseModel, Field


OCR_SERVICE_COORDINATE_COUNT = 8


class OCRPredictModel(BaseModel):
    """OCR检测模型"""

    bounds: Tuple[int, int, int, int] = Field(
        ...,
        description="**Bounding Box Coordinates**: List of 8 coordinates (x1, y1, x2, y2) defining the box around the recognized text",
        examples=[[100, 100, 200, 200]],
    )
    center_point: Tuple[int, int]
    text: str = Field(
        ...,
        description="**Recognized Text**: The actual text content extracted from the image",
    )
    recognition_score: float = Field(
        ...,
        description="**Text Recognition Confidence**: Score (0.0-1.0) indicating the confidence in the accuracy of the recognized text",
    )
    class_score: float = Field(
        ...,
        description="**Classification Confidence**: Score (0.0-1.0) indicating the confidence in the assigned class label",
    )
    class_label: int = Field(
        ...,
        description="**Class Label**: Numerical label identifying the category/class of the recognized text",
    )

    def __init__(self, *args, **kwargs):
        """自动转换框选坐标系和自动计算中心点坐标"""
        center_point = kwargs.get("center_point")
        bounds = kwargs.get("box") if kwargs.get("box") else kwargs.get("bounds")

        # 转换坐标系，将四角坐标转换成斜对角坐标
        if bounds and len(bounds) == OCR_SERVICE_COORDINATE_COUNT:
            x1, y1 = min(bounds[::2]), min(bounds[1::2])
            x2, y2 = max(bounds[::2]), max(bounds[1::2])
            bounds = (x1, y1, x2, y2)
            kwargs["bounds"] = bounds

        # 计算中心点坐标
        if center_point is None and bounds:
            left, top, right, bottom = bounds
            center_point = ((left + right) // 2, (top + bottom) // 2)
            kwargs["center_point"] = center_point

        # 模型实例化
        super().__init__(*args, **kwargs)
