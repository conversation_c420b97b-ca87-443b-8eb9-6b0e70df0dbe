from typing import Optional, Dict
from threading import Lock, local
from contextlib import contextmanager

from ccontrol_common.core.log_context import LogContext


class LogContextManager:
    """日志上下文管理器"""

    _instance = None
    _lock = Lock()
    _thread_local = local()

    def __init__(self):
        """初始化上下文管理器"""
        self._contexts: Dict[str, LogContext] = {}

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance.__init__()
        return cls._instance

    def get_context(self) -> Optional[LogContext]:
        """获取当前线程的日志上下文"""
        return getattr(self._thread_local, "context", None)

    def set_context(self, context: Optional[LogContext]):
        """设置当前线程的日志上下文"""
        self._thread_local.context = context

    @contextmanager
    def context(self, context: LogContext):
        """上下文管理器"""
        previous = self.get_context()
        self.set_context(context)
        try:
            yield
        finally:
            self.set_context(previous)

    def register_thread_context(self, thread_id: str, context: LogContext):
        """注册线程上下文关系"""
        with self._lock:
            self._contexts[thread_id] = context

    def get_thread_context(self, thread_id: Optional[str]) -> Optional[LogContext]:
        """获取指定线程的上下文"""
        if thread_id is None:
            return None
        return self._contexts.get(thread_id)

    def remove_thread_context(self, thread_id: str):
        """移除线程上下文"""
        with self._lock:
            self._contexts.pop(thread_id, None)
