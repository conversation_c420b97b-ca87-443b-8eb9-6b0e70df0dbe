import json
import uuid
import logging
from typing import Optional, Any, Dict
from urllib.parse import urlparse, parse_qs

import httpx
from httpx import Timeout, Request, Response, Headers
from httpx._types import (
    HeaderTypes,
    QueryParamTypes,
    RequestContent,
    RequestData,
    RequestFiles,
    TimeoutTypes,
)
from pydantic import BaseModel

from ccontrol_common.config.api_conf import API_AUTH_SESSION
from ccontrol_common.core import SingletonMeta
from ccontrol_common.core.log import log
from ccontrol_common.config.conf import settings

DEFAULT_LOG_LEN = 256
CONTENT_JSON = "application/json"
CONTENT_HTML = "text/html"
DEFAULT_TIMEOUT = 60.0


def gen_request_id():
    return uuid.uuid4().hex[:8]


def remove_image_url(data):
    """移除图片URL，用于日志记录"""
    if isinstance(data, dict):
        # 如果是字典，遍历其键值对
        keys_to_remove = [key for key in data.keys() if key in ("image_url", "imageUrl", "base64", "imageUrl1", "imageUrl2", "image", "screenshot", "crop")]
        for key in keys_to_remove:
            data[key] = "<image_url>"
        for key in data.keys():
            remove_image_url(data[key])
    elif isinstance(data, list):
        # 如果是列表，遍历其元素
        for item in data:
            remove_image_url(item)
    return data


class HttpClient(metaclass=SingletonMeta):
    """http客户端"""

    _httpx_client: httpx.Client
    _cookies: Dict[str, str]
    _zeus_domain: str

    def __init__(self):
        super().__init__()
        # 设置 httpx logger 级别为 WARNING
        logging.getLogger("httpx").setLevel(logging.WARNING)

        self._cookies = {settings.ZEUS_COOKIE_KEY: settings.ZEUS_COOKIE_VALUE} if settings.ZEUS_COOKIE_VALUE else {}
        self._zeus_domain = urlparse(settings.ZEUS_DOMAIN).netloc
        # httpx.Client将复用底层http连接
        self._httpx_client = httpx.Client(
            event_hooks={
                "request": [self.handle_request],
                "response": [self.handle_response],
            },
            timeout=Timeout(timeout=DEFAULT_TIMEOUT),
        )

    def update_cookies(self, cookies: Dict[str, str]):
        """更新cookies"""
        self._cookies = cookies

    def get_cookies(self) -> Dict[str, str]:
        """获取当前cookies"""
        return self._cookies

    def _format_log_content(self, content: str, max_len: int) -> str:
        """格式化日志内容"""
        if max_len != -1 and len(content) > max_len:
            return f"{content[:max_len]}......"
        return content

    def handle_request(self, request: Request):
        """hook: request前日志打印 and 插入request_id"""
        # 在每次请求时动态设置最新的 cookies
        if request.url.host == self._zeus_domain and self._cookies:
            cookie_str = "; ".join([f"{k}={v}" for k, v in self._cookies.items()])
            request.headers["Cookie"] = cookie_str

        # 合并默认请求头
        if "log_req_len" not in request.headers:
            request.headers["log_req_len"] = str(-1)
        if "log_resp_len" not in request.headers:
            request.headers["log_resp_len"] = str(-1)

        req_id = gen_request_id()
        try:
            log_req_len = int(request.headers.get("log_req_len", -1))
            if log_req_len == 0:  # 如果日志长度为0，直接跳过日志打印
                request.headers["X-Request-ID"] = req_id
                return

            content_type = request.headers.get("content-type", "")

            # 处理不同类型的请求内容
            if content_type.startswith(CONTENT_JSON) and isinstance(request.content, (str, bytes)):
                try:
                    content_str = request.content.decode("utf-8") if isinstance(request.content, bytes) else request.content
                    content_json = json.loads(content_str)
                    content_json = remove_image_url(content_json)
                    log_content = json.dumps(content_json, ensure_ascii=False)
                except (json.JSONDecodeError, UnicodeDecodeError):
                    log_content = "<invalid json content>"
            elif "multipart/form-data" in content_type:
                log_content = "<multipart form data>"
            elif "application/x-www-form-urlencoded" in content_type and request.content:
                try:
                    content_str = request.content.decode("utf-8") if isinstance(request.content, bytes) else str(request.content)
                    form_data = parse_qs(content_str)
                    form_data = remove_image_url(form_data)
                    log_content = json.dumps(form_data)
                except (UnicodeDecodeError, ValueError):
                    log_content = "<invalid form data>"
            else:
                log_content = f"<{content_type} content>"

            final_content = self._format_log_content(log_content, log_req_len)

        except (ValueError, AttributeError) as e:
            log.exception("Error processing request content")
            final_content = f"<error reading content: {str(e)}>"

        log.info(f"[{request.method}][{req_id}][{request.url}]: {final_content}")
        request.headers["X-Request-ID"] = req_id

    def handle_response(self, response: Response):
        """hook: response日志记录 and 请求状态检测"""
        request_id = response.request.headers.get("X-Request-ID")
        if not response.is_success and not response.is_redirect:
            try:
                response.read()
            except Exception as e:
                raise AssertionError(f"Response error, code: {response.status_code}, unable to read content: {str(e)}") from e
            raise AssertionError(f"Response error, code: {response.status_code}, text: {response.text}")

        try:
            response.read()
            log_resp_len = int(response.request.headers.get("log_resp_len", -1))
            if log_resp_len == 0:  # 如果日志长度为0，直接跳过日志打印
                return

            content_type = response.headers.get("content-type", "")

            # 处理不同类型的响应内容
            if content_type.startswith(CONTENT_JSON):
                try:
                    resp_json = json.loads(response.text)
                    resp_content = json.dumps(resp_json, ensure_ascii=False)
                except (json.JSONDecodeError, UnicodeDecodeError):
                    resp_content = "<invalid json content>"
            elif content_type.startswith(CONTENT_HTML):
                resp_content = response.text[:DEFAULT_LOG_LEN]
            else:
                resp_content = response.text

            log_content = self._format_log_content(resp_content, log_resp_len)

        except (ValueError, AttributeError) as e:
            log.exception("Error processing response content")
            log_content = f"<error reading content: {str(e)}>"

        log.info(f"[RESPONSE][{request_id}]: {log_content}")

    def get_client(self):
        """获取http client"""
        return self._httpx_client

    def close(self):
        """关闭http连接"""
        self._httpx_client.close()

    def __del__(self):
        self.close()


httpx_client = HttpClient()


def do_get(
    url: str,
    *,
    params: Optional[QueryParamTypes] = None,
    headers: Optional[HeaderTypes] = None,
    timeout: TimeoutTypes = Timeout(30),
    log_req_len: int = -1,
    log_resp_len: int = -1,
    **kwargs,
):
    """get请求并打印日志"""
    request_headers = Headers(headers) if headers else Headers()
    request_headers["log_req_len"] = str(log_req_len)
    request_headers["log_resp_len"] = str(log_resp_len)
    return httpx_client.get_client().get(
        url=url,
        params=params,
        timeout=timeout,
        headers=request_headers,
        **kwargs,
    )


def do_post(
    url: str,
    *,
    content: Optional[RequestContent] = None,
    data: Optional[RequestData] = None,
    files: Optional[RequestFiles] = None,
    json_data: Optional[Any] = None,
    params: Optional[QueryParamTypes] = None,
    headers: Optional[HeaderTypes] = None,
    timeout: TimeoutTypes = Timeout(30),
    log_req_len: int = -1,
    log_resp_len: int = -1,
    **kwargs,
) -> Response:
    """post请求并打印日志"""
    request_headers = Headers(headers) if headers else Headers()
    request_headers["log_req_len"] = str(log_req_len)
    request_headers["log_resp_len"] = str(log_resp_len)
    return httpx_client.get_client().post(
        url,
        content=content,
        params=params,
        json=json_data,
        files=files,
        data=data,
        timeout=timeout,
        headers=request_headers,
        **kwargs,
    )


def do_patch(
    url: str,
    *,
    content: Optional[RequestContent] = None,
    data: Optional[RequestData] = None,
    files: Optional[RequestFiles] = None,
    json: Optional[Any] = None,
    params: Optional[QueryParamTypes] = None,
    headers: Optional[HeaderTypes] = None,
    timeout: TimeoutTypes = Timeout(30),
    log_req_len: int = -1,
    log_resp_len: int = -1,
    **kwargs,
) -> Response:
    """patch请求并打印日志"""
    request_headers = Headers(headers) if headers else Headers()
    request_headers["log_req_len"] = str(log_req_len)
    request_headers["log_resp_len"] = str(log_resp_len)
    return httpx_client.get_client().patch(
        url=url,
        content=content,
        params=params,
        json=json,
        files=files,
        data=data,
        timeout=timeout,
        headers=request_headers,
        **kwargs,
    )


class RespParse(BaseModel):
    """http响应解析"""

    success: bool = False
    msg: Optional[str] = None
    code: Optional[int] = -1
    data: Optional[dict] = None
    resp_json: Optional[dict] = None


def response_valid(
    resp: Response,
    err_msg: Optional[str] = None,
    check_json: Optional[bool] = True,
) -> RespParse:
    """返回值校验"""
    # step 1: 校验返回值
    if not resp.is_success:
        resp_status = False
        log.error(f"{err_msg}请求失败, resp: {resp.text}")
    else:
        resp_status = True

    # step 2: 获取返回值
    try:
        resp_json = resp.json()
        if not resp_json:
            resp_json = {}
    except Exception as e:
        log.exception(f"parse response json failed, {e}")
        raise e

    code = resp_json.get("code", -1)
    msg = resp_json.get("msg", "")
    data = resp_json.get("data", {})

    # step 3: 根据code判断返回状态
    if check_json:
        if 200 <= code < 400:
            resp_status = True
        else:
            resp_status = False
    return RespParse(success=resp_status, code=code, msg=msg, data=data, resp_json=resp_json)


if __name__ == "__main__":
    do_get(url=settings.ZEUS_DOMAIN + API_AUTH_SESSION)
