#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import logging
import os
import sys
import threading
from datetime import datetime
from functools import partial, wraps
from typing import Any, List, Optional, Union

from loguru import logger

from ccontrol_common.config import path_conf
from ccontrol_common.config.conf import settings
from ccontrol_common.core.context_manager import LogContextManager
from ccontrol_common.core.log_context import LogContext
from ccontrol_common.core.singleton import SingletonMeta

# 绑定值常量
CLIENT_ADDR = "client_addr"
TRACE_ID = "trace_id"
TASK_ID = "task_id"

LOG_FORMAT = "[ <green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> ]" + "[ <cyan>{file.name:>25}</cyan>:<cyan>{function:>27}</cyan>:<cyan>{line:>4}</cyan> ]" + "[ <level>{level:^8}</level> ]" + "{extra[task_id_info]}" + " {extra[prefix]}{message}"

# 重入检测线程本地存储
_logger_reentry_guard = threading.local()


def escape_color_tags(message: str) -> str:
    """转义可能被误认为是颜色标记的字符"""
    return message.replace("<", r"\<")


def _is_logger_reentry() -> bool:
    """检查是否处于日志重入状态"""
    return getattr(_logger_reentry_guard, "in_use", False)


def _set_logger_reentry(value: bool):
    """设置日志重入状态"""
    _logger_reentry_guard.in_use = value


def safe_log_call(logger_method, *args, **kwargs):
    """安全的日志调用，避免重入死锁"""
    if _is_logger_reentry():
        # 如果已经在日志调用中，直接返回，避免重入
        return

    try:
        _set_logger_reentry(True)
        logger_method(*args, **kwargs)
    except Exception as e:
        # 如果是死锁错误，直接忽略
        if "deadlock avoided" in str(e).lower():
            pass
        else:
            # 其他错误可以考虑输出到 stderr
            print(f"Logging error: {e}", file=sys.stderr)
    finally:
        _set_logger_reentry(False)


class InterceptHandler(logging.Handler):
    def emit(self, record: logging.LogRecord) -> None:
        # Get corresponding Loguru level if it exists.
        try:
            level: Union[str, int] = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message.
        frame, depth = logging.currentframe(), 0
        while frame:
            filename = frame.f_code.co_filename
            is_logging = filename == logging.__file__
            is_frozen = "importlib" in filename and "_bootstrap" in filename
            if depth > 0 and not (is_logging or is_frozen):
                break
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


class LoggerProxy:
    """日志代理"""

    def __init__(self):
        self._logger = logger
        self._context_manager = LogContextManager()

    def add_task_handler(self, task_id: str) -> int:
        """为任务添加日志处理器

        Args:
            task_id: 任务ID

        Returns:
            int: 日志处理器ID
        """
        handler_id = Logger().add_task_handler(task_id)
        if not isinstance(handler_id, int):
            raise TypeError(f"Handler ID must be an integer, got {type(handler_id)}")
        return handler_id

    def remove(self, handler_id: int) -> None:
        """移除日志处理器

        Args:
            handler_id: 日志处理器ID
        """
        if not isinstance(handler_id, int):
            raise TypeError(f"Handler ID must be an integer, got {type(handler_id)}")
        self._logger.remove(handler_id)

    def __getattr__(self, name: str) -> Any:
        attr = getattr(self._logger, name)

        # 对于特殊方法（如 opt, bind 等），需要返回新的 LoggerProxy 实例来支持链式调用
        if name in ('opt', 'bind', 'patch', 'level', 'catch', 'contextualize'):
            def chain_wrapper(*args, **kwargs):
                # 调用原始方法获取新的 logger 实例
                new_logger = attr(*args, **kwargs)
                # 创建新的 LoggerProxy 实例，使用新的 logger
                new_proxy = LoggerProxy()
                new_proxy._logger = new_logger
                new_proxy._context_manager = self._context_manager
                return new_proxy
            return chain_wrapper

        if callable(attr):
            @wraps(attr)
            def wrapped(*args, **kwargs):
                context = self._context_manager.get_context()
                if context:
                    # 构建上下文路径
                    context_path = self._build_context_path(context)
                    # 计算树状缩进前缀
                    prefix = self._get_tree_prefix(context)
                    # 如果args是消息内容，添加缩进
                    if args:
                        args = (f"{prefix}{args[0]}", *args[1:])
                    # 绑定上下文信息，包括 task_id
                    bound_logger = self._logger.bind(task_id=context.task_id or "", context_type=context.context_type.value, context_path=context_path, **context.metadata or {})
                    # 使用 opt(depth=1) 跳过包装函数，显示真实的调用位置
                    return safe_log_call(getattr(bound_logger.opt(depth=1), name), *args, **kwargs)
                # 使用 opt(depth=1) 跳过包装函数，显示真实的调用位置
                return safe_log_call(getattr(self._logger.opt(depth=1), name), *args, **kwargs)

            return wrapped
        return attr

    def _build_context_path(self, context: LogContext) -> str:
        """构建上下文路径"""
        parts = []
        current = context
        while current:
            if current.context_id:  # 确保 context_id 不为 None
                parts.append(current.context_id)
            if current.parent_id:  # 只在有父上下文 ID 时继续
                parent_context = self._context_manager.get_thread_context(current.parent_id)
                if parent_context is None:  # 如果找不到父上下文，终止循环
                    break
                current = parent_context
            else:
                break
        return ".".join(reversed(parts))

    def _get_tree_prefix(self, context: LogContext) -> str:
        """生成树状缩进前缀"""
        depth = 0
        current = context
        while current and current.parent_id:
            depth += 1
            parent_context = self._context_manager.get_thread_context(current.parent_id)
            if parent_context is None:
                break
            current = parent_context

        if depth == 0:
            return ""
        if depth == 1:
            return "├── "
        return "├" + "──" * depth + " "


class Logger(metaclass=SingletonMeta):
    @staticmethod
    def patch(record):
        # 额外信息后补空格
        extra = record["extra"]

        # client地址
        if extra[CLIENT_ADDR]:
            extra[CLIENT_ADDR] = f"from: {extra[CLIENT_ADDR]}"

            # 鲸云trace_id记录
            if extra[TRACE_ID]:
                extra[CLIENT_ADDR] += f"({extra[TRACE_ID]})"

            extra[CLIENT_ADDR] += " - "

        # task_id 信息处理
        task_id_info = ""
        if extra.get(TASK_ID):
            task_id_info = f"[ TASK:{extra[TASK_ID]} ]"
        extra["task_id_info"] = task_id_info

        return record

    @staticmethod
    def intercept_logging():
        """将标准库logging重定向到loguru"""
        # 移除所有已有的handlers
        logging.root.handlers = []
        # 设置日志级别为DEBUG，与loguru保持一致
        logging.root.setLevel(settings.LOG_LEVEL)
        # 添加InterceptHandler
        logging.root.addHandler(InterceptHandler())

    @staticmethod
    def init():
        logger.configure(
            extra={CLIENT_ADDR: "", TRACE_ID: "", TASK_ID: "", "node_info": "", "prefix": "", "task_id_info": ""},
            patcher=Logger.patch,
        )
        logger.remove()
        logger.add(sys.stderr, format=LOG_FORMAT, level=settings.LOG_LEVEL)
        # 添加对标准库logging的拦截
        Logger.intercept_logging()

    @staticmethod
    def log():
        Logger.init()
        if not os.path.exists(path_conf.LOG_PATH):
            os.mkdir(path_conf.LOG_PATH)

        # 获取当前时间戳，用于生成唯一的日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")

        # 使用时间戳创建唯一的日志文件名
        # 格式: FastAPI_{timestamp}.log
        log_filename = f"FastAPI_{timestamp}.log"
        log_file = os.path.join(path_conf.LOG_PATH, log_filename)

        # loguru日志配置
        logger.add(
            sink=log_file,
            encoding="utf-8",
            level=settings.LOG_LEVEL,  # 使用配置文件中的日志级别
            rotation="50 MB",  # 按文件大小轮转，避免单个日志文件过大
            retention="30 days",  # 定时自动清理文件
            compression="zip",  # 压缩旧日志文件以节省空间
            enqueue=True,  # 异步安全
            backtrace=True,  # 错误跟踪
            diagnose=True,
            format=LOG_FORMAT,
        )

        return LoggerProxy()

    @staticmethod
    def log_filter(record, target: str = "") -> bool:
        """优先通过线程名过滤"""
        return target == record["thread"].name or record["extra"]["node_info"].startswith(target)

    @staticmethod
    def add_task_handler(task_id: str) -> int:
        """创建handler：
        handler会过滤出bind(task=task_id)的日志
        handler会过滤出TaskRunner.name = task_id的日志

        Args:
            task_id: 任务ID

        Returns:
            int: 日志处理器ID
        """
        # step 1: 创建日志存放位置
        if not os.path.exists(path_conf.LOG_PATH):
            os.makedirs(path_conf.LOG_PATH)
        # step 2: 创建日志文件
        log_task_file = os.path.join(path_conf.LOG_PATH, f"{task_id}.log")
        # step 3: 创建handler
        logger_handler = logger.add(
            sink=log_task_file,
            encoding="utf-8",
            level=settings.LOG_LEVEL,
            retention="30 days",  # 定时自动清理文件
            enqueue=True,  # 异步安全
            backtrace=True,  # 错误跟踪
            diagnose=True,
            filter=partial(Logger.log_filter, target=task_id),
            format=LOG_FORMAT,
        )

        if not isinstance(logger_handler, int):
            raise TypeError(f"Logger handler must be an integer, got {type(logger_handler)}")
        return logger_handler


class LogTree:
    def __init__(
        self,
        name: str,
        parent: Optional["LogTree"] = None,
        description: Optional[str] = None,
        task_id: Optional[str] = None,
        _depth_offset: int = 1,  # 内部参数，用于调整调用栈深度
        _auto_log: bool = True,  # 是否自动记录日志
    ):
        self.name = name
        self.parent = parent
        self.description = description
        self.task_id = task_id or (parent.task_id if parent else None)
        self.children: List[LogTree] = []
        self._depth_offset = _depth_offset
        self._auto_log = _auto_log

        # Get the full task path and initialize logger with tree prefix
        task_path = self.get_task_path()
        prefix = self._get_tree_prefix()

        # 绑定 task_id 和其他信息
        bind_data = {"node_info": task_path, "prefix": prefix}
        if self.task_id:
            bind_data["task_id"] = self.task_id

        self.logger = logger.bind(**bind_data)

        # 延迟日志记录，避免在构造函数中直接调用 logger
        if self._auto_log:
            # 使用安全的日志调用
            safe_log_call(self.logger.opt(depth=_depth_offset).info, f"[{task_path}]")

        if self.parent:
            self.parent.children.append(self)

    def _get_tree_prefix(self) -> str:
        """Generate tree-like prefix based on node depth"""
        if not self.parent:
            return ""

        depth = 0
        current = self.parent
        while current:
            depth += 1
            current = current.parent

        if depth == 1:
            return "├── "
        return "├" + "──" * depth + " "

    def get_task_path(self) -> str:
        """Get the full path of the task from root"""
        if not self.parent:
            return self.name
        return f"{self.parent.get_task_path()}.{self.name}"

    def create_child(self, name: str, description: Optional[str] = None) -> "LogTree":
        """Create and return a new child task"""
        return LogTree(name=name, parent=self, description=description, task_id=self.task_id, _depth_offset=2)

    def log_message(self, level: str, message: str, **kwargs):
        """安全的日志消息记录方法"""
        logger_method = getattr(self.logger, level.lower())
        safe_log_call(logger_method, message, **kwargs)


log = Logger().log()
