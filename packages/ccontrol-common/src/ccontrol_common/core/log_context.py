from typing import Optional, Dict
from enum import Enum

from pydantic import BaseModel, Field


class LogContextType(str, Enum):
    """日志上下文类型"""

    TASK = "task"
    SUITE = "suite"
    CASE = "case"
    EVENT = "event"
    AGENT = "agent"


class LogContext(BaseModel):
    """日志上下文"""

    context_id: str  # 上下文ID
    context_type: LogContextType  # 上下文类型
    parent_id: Optional[str] = None  # 父上下文ID
    task_id: Optional[str] = None  # 所属任务ID
    metadata: Dict[str, str] = Field(default_factory=dict)  # 元数据

    model_config = {
        "arbitrary_types_allowed": True,  # 允许任意类型
    }
