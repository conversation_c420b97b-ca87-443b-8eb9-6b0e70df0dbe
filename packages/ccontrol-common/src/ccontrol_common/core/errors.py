#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
from enum import Enum
from http import HTTPStatus
from typing import Any, Optional

from pydantic import BaseModel, Field

if sys.version_info >= (3, 9):
    from typing import Annotated
else:
    from typing_extensions import Annotated


class ErrorModel(BaseModel):
    """统一错误响应模型"""

    code: Annotated[int, Field(description="错误码")]
    message: Annotated[str, Field(description="错误消息")]
    data: Optional[Any] = Field(default=None, description="错误详情数据")


class BaseError(Exception):
    """基础错误类"""

    def __init__(self, code: int, message: str, data: Optional[Any] = None):
        self.error = ErrorModel(code=code, message=message, data=data)
        super().__init__(message)


class RequestError(BaseError):
    """400 错误"""

    def __init__(self, message: str = HTTPStatus.BAD_REQUEST.phrase, data: Optional[Any] = None):
        super().__init__(code=HTTPStatus.BAD_REQUEST, message=message, data=data)


class ParameterError(BaseError):
    """参数错误"""

    def __init__(self, message: str = "Invalid parameters", data: Optional[Any] = None):
        super().__init__(code=HTTPStatus.BAD_REQUEST, message=message, data=data)


class AuthorizationError(BaseError):
    """401 错误"""

    def __init__(self, message: str = HTTPStatus.UNAUTHORIZED.phrase, data: Optional[Any] = None):
        super().__init__(code=HTTPStatus.UNAUTHORIZED, message=message, data=data)


class ForbiddenError(BaseError):
    """403 错误"""

    def __init__(self, message: str = HTTPStatus.FORBIDDEN.phrase, data: Optional[Any] = None):
        super().__init__(code=HTTPStatus.FORBIDDEN, message=message, data=data)


class NotFoundError(BaseError):
    """404 错误"""

    def __init__(self, message: str = HTTPStatus.NOT_FOUND.phrase, data: Optional[Any] = None):
        super().__init__(code=HTTPStatus.NOT_FOUND, message=message, data=data)


class ServerError(BaseError):
    """500 错误"""

    def __init__(
        self,
        message: str = HTTPStatus.INTERNAL_SERVER_ERROR.phrase,
        data: Optional[Any] = None,
    ):
        super().__init__(code=HTTPStatus.INTERNAL_SERVER_ERROR, message=message, data=data)


class GatewayError(BaseError):
    """502 错误"""

    def __init__(self, message: str = HTTPStatus.BAD_GATEWAY.phrase, data: Optional[Any] = None):
        super().__init__(code=HTTPStatus.BAD_GATEWAY, message=message, data=data)


# ===================== 代码安全相关错误 =====================


class CodeSecurityLevel(Enum):
    """代码安全级别"""

    FORBIDDEN = "forbidden"  # 完全禁止
    WARNING = "warning"  # 警告并删除


class CodeSecurityError(BaseError):
    """代码安全检查相关错误"""

    def __init__(self, message: str):
        super().__init__(code=400, message=message)
        self.message = message


class ForbiddenImportError(CodeSecurityError):
    """禁止导入模块错误"""

    def __init__(self, module_name: str):
        message = f"禁止导入模块: {module_name}"
        super().__init__(message)


class RestrictedImportError(CodeSecurityError):
    """受限制导入错误"""

    def __init__(self, module_name: str, allowed_attrs: str):
        message = f"不建议直接导入 {module_name} 模块，请使用具体的功能，如: from {module_name} import {allowed_attrs}"
        super().__init__(message)


class ForbiddenFunctionError(CodeSecurityError):
    """禁止使用函数错误"""

    def __init__(self, func_name: str):
        message = f"禁止使用内置函数: {func_name}"
        super().__init__(message)


class RestrictedFunctionError(CodeSecurityError):
    """受限制函数使用错误"""

    def __init__(self, func_name: str, allowed_modes: str):
        message = f"{func_name}() 函数只允许以下模式: {allowed_modes}"
        super().__init__(message)


class ForbiddenMethodError(CodeSecurityError):
    """禁止调用方法错误"""

    def __init__(self, method_name: str):
        message = f"禁止调用方法: {method_name}"
        super().__init__(message)


class SyntaxCheckError(CodeSecurityError):
    """语法检查错误"""

    def __init__(self, error_msg: str):
        message = f"代码语法错误: {error_msg}"
        super().__init__(message)


class PythonExecuteError(BaseError):
    """Python代码执行错误"""

    def __init__(
        self,
        error_msg: str,
        error_line: Optional[int] = None,
        code_context: Optional[str] = None,
    ):
        message = f"Python代码执行错误: {error_msg}"
        if error_line is not None:
            message += f"\n错误行号: {error_line}"
        if code_context is not None:
            message += f"\n代码上下文:\n{code_context}"
        super().__init__(code=500, message=message)


# ===================== 任务相关错误 =====================


class TaskNotFoundError(NotFoundError):
    """任务不存在错误"""

    def __init__(self, task_id: str, data: Optional[Any] = None):
        super().__init__(message=f"Task {task_id} not found", data=data)


class TaskStateError(RequestError):
    """任务状态错误"""

    def __init__(self, message: str, data: Optional[Any] = None):
        super().__init__(message=message, data=data)


class TaskInitError(ServerError):
    """任务初始化错误"""

    def __init__(self, task_id: str, component: str, data: Optional[Any] = None):
        super().__init__(message=f"Task {task_id} {component} initialization failed", data=data)


class TaskRuntimeError(ServerError):
    """任务运行时错误"""

    def __init__(self, task_id: str, operation: str, data: Optional[Any] = None):
        super().__init__(message=f"Task {task_id} failed during {operation}", data=data)
