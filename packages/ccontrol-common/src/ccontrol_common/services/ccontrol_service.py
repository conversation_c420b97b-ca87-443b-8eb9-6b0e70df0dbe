import functools
from datetime import datetime, timezone
import json
from typing import Literal, Optional

from ccontrol_common.config.api_conf import API_AUTH_SESSION, API_EVENT_FIND_FIRST
from ccontrol_common.config.conf import settings
from ccontrol_common.core.http_client import do_get


def get_session():
    """获取 session"""
    if not settings.ZEUS_COOKIE_VALUE:
        raise AssertionError("请先设置环境变量：ZEUS_COOKIE_VALUE")
    resp = do_get(settings.ZEUS_DOMAIN + API_AUTH_SESSION, log_resp_len=0)
    resp_json = resp.json()

    if not resp_json:
        raise AssertionError("COOKIE已过期，请重新获取COOKIE")

    # 判断 COOKIE 是否过期
    expires = resp_json.get("expires")
    if not expires:
        raise AssertionError("session 不存在过期时间，请联系管理员！")
    expires_time = datetime.fromisoformat(expires.replace("Z", "+00:00"))
    now_time = datetime.now(timezone.utc)
    if expires_time < now_time:
        raise AssertionError("COOKIE已过期，请重新获取COOKIE")

    return resp_json


def is_login() -> bool:
    """获取登录状态"""
    resp = get_session()
    return resp is not None


def valid_login():
    """验证是否登录，如果未登录，则抛出异常"""
    if not is_login():
        raise AssertionError("请先登录")
    return True


def need_login(func):
    """验证登录状态的装饰器"""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 在执行函数之前验证是否登录
        valid_login()
        return func(*args, **kwargs)

    return wrapper


@need_login
def test_login_wrapper():
    return


@need_login
def get_event(
    id: Optional[str] = None,
    *,
    method: Optional[str] = None,
    event_type: Optional[Literal["ai", "agent_rpc", "python"]] = None,
):
    if not id and not method:
        raise AssertionError("id和method不能同时为空")

    where = {}
    if id is not None:
        where["id"] = id
    if method is not None:
        where["method"] = method
    if event_type is not None:
        where["eventType"] = event_type

    resp = do_get(
        settings.ZEUS_DOMAIN + API_EVENT_FIND_FIRST,
        params={"q": json.dumps({"where": where})},
    )
    return resp.json().get("data", None)


@need_login
def get_python_event_code(id: Optional[str] = None, *, method: Optional[str] = None):
    event = get_event(id=id, method=method, event_type="python")
    if not event:
        raise AssertionError("event not found")

    code = event.get("pythonCode", None)
    if not code:
        raise AssertionError("event code is empty")

    return code


if __name__ == "__main__":
    # print(f"get_session: {get_session()}")

    # print(f"is_login : {is_login()}")

    # print(f"valid_login : {valid_login()}")

    # test_login_wrapper()

    print("-> result: ", get_python_event_code("cm4b2fsve0001wmwne1xshj4k"))
    print("-> result: ", get_python_event_code(method="hello_world"))
