import json
from datetime import datetime
from functools import wraps
import os
from typing import Callable, Optional, Union
from uuid import UUID

from ccontrol_common.config.conf import settings
from ccontrol_common.core import log
from ccontrol_common.core.http_client import do_post
from ccontrol_common.services.ccontrol_service import get_session
from ccontrol_common.services.analysis.tv_statistics.model import CreateEfficiencyToolStatistics


def record_to_tv_statistics(efficiency_tool_uuid: Union[UUID, str], channel: str, extra: Optional[Union[str, Callable[..., Optional[str]]]] = None, extra_json: Optional[Union[str, Callable[..., Optional[str]]]] = None):
    """通用效能工具统计装饰器

    Args:
        efficiency_tool_uuid: 效能工具UUID
        channel: 数据来源渠道
        extra: 额外信息，可以是字符串或返回可选字符串的函数
        extra_json: JSON格式的补充信息，需要是合法的JSON字符串或返回可选JSON字符串的函数

    Returns:
        装饰器函数

    Example:
        >>> @record_efficiency_statistics(
        ...     efficiency_tool_uuid=UUID("tool-uuid"),
        ...     channel="web",
        ...     extra_json='{"key": "value"}'
        ... )
        ... def some_function():
        ...     pass
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = datetime.now()
            result = func(*args, **kwargs)

            # 如果分析功能启用，执行统计上报
            if settings.CCONTROL_ANALYSIS_ENABLED:
                try:
                    time_consuming = round((datetime.now() - start_time).total_seconds(), 2)
                    session = get_session()
                    username = session.get("user", {}).get("account")

                    # 处理动态参数，注意返回值可能为 None
                    extra_value = extra(*args, **kwargs) if callable(extra) else extra
                    extra_json_value = extra_json(*args, **kwargs) if callable(extra_json) else extra_json

                    statistics = CreateEfficiencyToolStatistics(efficiency_tool_uuid=efficiency_tool_uuid, username=username, channel=channel, use_time=start_time, time_consuming=time_consuming, times=1, extra=extra_value, extra_json=extra_json_value)

                    do_post(url=settings.TV_STATISTICS_DOMAIN + "/v1/efficiency_tool/statistics/insert", json_data=[statistics.model_dump(mode="json", exclude_none=True)])
                except (ValueError, TypeError, ConnectionError) as e:
                    # 使用具体的异常类型而不是通用的Exception
                    log.error(f"Failed to report efficiency statistics: {str(e)}")

            return result

        return wrapper

    return decorator


def create_tool_statistics_decorator(
    efficiency_tool_uuid: Union[UUID, str],
    channel: str,
):
    """创建特定工具的统计装饰器

    这个函数用于创建一个预设了 UUID 和 channel 的装饰器

    Args:
        efficiency_tool_uuid: 效能工具UUID
        channel: 数据来源渠道，例如：CControl-Test-Engine、自动做 bin

    Returns:
        一个新的装饰器函数，该函数只接收 extra 和 extra_json 参数

    Example:
        >>> record_my_tool_statistics = create_tool_statistics_decorator(
        ...     efficiency_tool_uuid=UUID("my-tool-uuid"),
        ...     channel="my-tool"
        ... )
        >>>
        >>> @record_my_tool_statistics()
        ... def my_tool_function():
        ...     pass
        >>>
        >>> @record_my_tool_statistics(extra="some info", extra_json='{"key": "value"}')
        ... def another_function():
        ...     pass
        >>>
        >>> # 使用函数动态获取参数
        >>> @record_my_tool_statistics(
        ...     extra=lambda *args, **kwargs: f"处理了 {kwargs.get('item_id')}" if 'item_id' in kwargs else None,
        ...     extra_json=lambda *args, **kwargs: json.dumps({"result": args[0]}) if args else None
        ... )
        ... def process_function(result=None, item_id=None):
        ...     return result
    """

    def tool_statistics_decorator(extra: Optional[Union[str, Callable[..., Optional[str]]]] = None, extra_json: Optional[Union[str, Callable[..., Optional[str]]]] = None):
        return record_to_tv_statistics(efficiency_tool_uuid=efficiency_tool_uuid, channel=channel, extra=extra, extra_json=extra_json)

    return tool_statistics_decorator


if __name__ == "__main__":
    tool_statistics = create_tool_statistics_decorator("7e3371d4f4ac46bf8f0560f7e60d4ee9", "CControl-Test-Engine")
    print(tool_statistics)

    # 静态参数示例
    @tool_statistics(extra="test", extra_json=json.dumps({"test": "value"}))
    def test():
        pass

    # 动态参数示例 - 可以返回 None
    @tool_statistics(
        # 如果环境变量不存在则返回 None
        extra=lambda *args, **kwargs: os.environ.get("PWD"),
    )
    def test_dynamic(value=None, config=None):
        return value

    test()
    test_dynamic("测试值", config={"option": "value"})
    # 测试返回 None 的情况
    test_dynamic(None)
