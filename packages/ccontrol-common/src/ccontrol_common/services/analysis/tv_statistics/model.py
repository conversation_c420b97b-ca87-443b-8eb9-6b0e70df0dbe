from datetime import datetime
from typing import Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field


class CreateEfficiencyToolStatistics(BaseModel):
    """效能工具使用统计数据模型

    This model represents statistics for efficiency tool usage, including user information,
    timing details, and additional metadata.
    """

    efficiency_tool_uuid: Union[UUID, str] = Field(..., title="Efficiency Tool Uuid", description="效能工具的唯一标识符")

    username: str = Field(..., title="使用者", description="用户域账号")

    channel: str = Field(..., title="渠道名", description="用来标识数据来源")

    use_time: Optional[datetime] = Field(None, title="使用时间点", description="真实使用时间点")

    times: Optional[int] = Field(
        None,
        title="使用次数",
        description="工具被使用的次数",
        ge=0,  # 使用次数必须大于等于0
    )

    time_consuming: Optional[float] = Field(
        None,
        title="耗时",
        description="单位：秒，保留两位小数",
        ge=0.0,  # 耗时必须大于等于0
    )

    extra: Optional[str] = Field(None, title="额外信息", description="其他补充信息")

    extra_json: Optional[str] = Field(None, title="额外信息(JSON)", description="JSON格式的补充信息，需要是合法的JSON字符串")

    class Config:
        """Pydantic model configuration"""

        json_schema_extra = {
            "example": {
                "efficiency_tool_uuid": "0ea19b7431d14a54991522784c40d9c7",
                "username": "john.doe",
                "channel": "web",
                "use_time": "2024-03-20T10:30:00Z",
                "times": 1,
                "time_consuming": 5.50,
                "extra": "Additional information",
                "extra_json": '{"key": "value"}',
            }
        }


if __name__ == "__main__":
    print(CreateEfficiencyToolStatistics.model_json_schema())

    tool = CreateEfficiencyToolStatistics(efficiency_tool_uuid="test", username="test", channel="test", use_time=datetime.now(), times=1, time_consuming=5.50, extra="test", extra_json='{"key": "value"}')
    print(" ~ tool:", tool)
