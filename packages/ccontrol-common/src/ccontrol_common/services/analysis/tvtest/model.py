from enum import IntEnum
from typing import Dict, Any, Optional

from pydantic import BaseModel, Field
from pydantic.alias_generators import to_camel


class AutoTestResultEnum(IntEnum):
    """自动化测试结果枚举"""

    NOT_AUTO = 0  # 非自动化
    SUCCESS = 1  # 成功
    FAILED = 2  # 失败
    RUNNING = 3  # 进行中


class AutoTestResultBo(BaseModel):
    """自动化测试结果模型"""

    auto_test_from: str = Field(description="自动化测试来源: UIAT")
    auto_test_result: AutoTestResultEnum = Field(description="自动化测试结果 0:非自动化 1：成功 2：失败 3：进行中")
    ext_info: Dict[str, Any] = Field(default_factory=dict, description="扩展信息")
    jira_code: Optional[str] = Field(default=None, description="jira编号")
    test_user: Optional[str] = Field(default=None, description="测试人")

    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
    }

    def model_dump_json(self, **kwargs) -> str:
        """重写 model_dump_json 方法，默认使用 by_alias=True"""
        kwargs.setdefault("by_alias", True)
        return super().model_dump_json(**kwargs)
