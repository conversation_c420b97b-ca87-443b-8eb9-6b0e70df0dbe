import json
from typing import Dict, Any, Optional, Callable
from functools import wraps

from ccontrol_common.config.conf import settings
from ccontrol_common.core import log
from ccontrol_common.core.http_client import do_post
from ccontrol_common.services.ccontrol_service import get_session
from ccontrol_common.services.analysis.tvtest.model import (
    AutoTestResultBo,
    AutoTestResultEnum,
)


def _report_status(
    status: AutoTestResultEnum,
    jira_code: Optional[str] = None,
    ext_info: Optional[Dict[str, Any]] = None,
) -> None:
    """Report test execution status to TV test system.

    Args:
        status: Test execution status
        jira_code: JIRA issue code
        ext_info: Additional information
    """
    # 如果分析功能被禁用，直接返回
    if not settings.CCONTROL_ANALYSIS_ENABLED:
        return

    try:
        # Get user information from session
        session = get_session()
        user = session.get("user", {})
        test_user = user.get("account")

        req_body = AutoTestResultBo(
            auto_test_from="CControl",
            auto_test_result=status,
            jira_code=jira_code,
            test_user=test_user,
            ext_info=ext_info or {},
        )
        req_json = json.loads(req_body.model_dump_json())
        resp = do_post(
            url=settings.TV_TEST_DOMAIN + "/openapi/v1/activity/record-jira-auto-test-result",
            json_data=req_json,
        )
        # 任一条件不满足则打印异常日志记录
        if not all(
            [
                resp.status_code == 200,
                resp.json().get("status") == "0",
                resp.json().get("data"),
            ]
        ):
            log.error("❌ Failed to report test status to TV test system, please check the parameters.")
    except Exception as e:
        log.error(f"Failed to report test status: {e}")


def record_to_tvtest(jira_code: Optional[str] = None):
    """Decorator to record test execution status.

    Args:
        jira_code: JIRA issue code

    Returns:
        Decorator function
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Report running status before execution
            _report_status(
                AutoTestResultEnum.RUNNING,
                jira_code=jira_code,
            )

            try:
                # Execute the test function
                result = func(*args, **kwargs)
                # Report success status after execution
                _report_status(
                    AutoTestResultEnum.SUCCESS,
                    jira_code=jira_code,
                )
                return result
            except Exception as e:
                # Report failed status if exception occurs
                _report_status(
                    AutoTestResultEnum.FAILED,
                    jira_code=jira_code,
                    ext_info={"error": str(e)},
                )
                raise  # Re-raise the exception to maintain original behavior

        return wrapper

    return decorator


if __name__ == "__main__":

    @record_to_tvtest(jira_code="TEST-001")
    def execute_test_case():
        """执行测试用例"""
        print("执行测试用例")

    execute_test_case()
