#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os.path
import os
from functools import lru_cache
from typing import Literal, Optional

from pydantic_settings import BaseSettings, SettingsConfigDict

from ccontrol_common.config.path_conf import WORKSPACE_PATH


class Settings(BaseSettings):
    """配置类"""

    # 基础配置
    model_config = SettingsConfigDict(
        env_file=[
            os.path.join(WORKSPACE_PATH, ".env"),
            os.path.join(os.getcwd(), ".env"),
        ],
        extra="allow",
    )

    # 日志等级
    LOG_LEVEL: str = "INFO"
    # 开启 LiveKit 的 RTC 调试日志
    LOG_LIVEKIT_RTC: Optional[Literal["true", "1"]] = None

    # LiveKit
    LIVEKIT_URL: str = "https://ccontrol-play.cvte.com"

    # Zeus Server
    ZEUS_DOMAIN: str = "https://ccontrol.cvte.com"
    ZEUS_COOKIE_KEY: str = "__Secure-authjs.session-token"

    # User Config
    ZEUS_COOKIE_VALUE: str = ""
    DEVICE_ID: int = -1

    # 区分测试引擎服务和用户模式
    RUN_MODE: Literal["user", "service"] = "user"

    # Analysis
    # 分析功能开关
    CCONTROL_ANALYSIS_ENABLED: bool = False

    # TV 统计数据上报地址
    TV_STATISTICS_DOMAIN: str = "https://tv-statistics.gz.cvte.cn"

    # TV Test
    # 正式环境： https://tvtest.gz.cvte.cn/baseservice
    # 测试环境： https://tvtestfat.gz.cvte.cn/baseservice
    TV_TEST_DOMAIN: str = "https://tvtest.gz.cvte.cn/baseservice"

    def model_post_init(self, *_, **__) -> None:
        """在初始化完成后执行的操作"""
        if self.LOG_LIVEKIT_RTC:
            os.environ["LIVEKIT_RTC_DEBUG"] = self.LOG_LIVEKIT_RTC


@lru_cache()
def get_settings():
    """读取配置优化写法"""
    return Settings()


settings = get_settings()

if __name__ == "__main__":
    print(settings.model_dump_json())
