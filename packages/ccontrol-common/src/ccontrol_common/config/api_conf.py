# Zeus API
API_AUTH_SESSION = "/api/auth/session"
API_AUTH_CREDENTIALS = "/api/auth/callback/credentials"
API_EVENT_FIND_FIRST = "/api/model/cControlEvent/findFirst"
API_EVENT_FIND_MANY = "/api/model/cControlEvent/findMany"

# 任务API
API_TASK_COMPLETE = "/api/tasks/complete"

# 对象存储API
API_PRESIGNED_UPLOAD = "/api/file/presigned/upload"
API_PRESIGNED_OVERRIDE_UPLOAD = "/api/file/presigned/overrideUpload"
API_PRESIGNED_DOWNLOAD = "/api/file/presigned/download"

# 文件状态更新API
API_FILE_STATUS_UPDATE = "/api/model/cControlFile/update"
