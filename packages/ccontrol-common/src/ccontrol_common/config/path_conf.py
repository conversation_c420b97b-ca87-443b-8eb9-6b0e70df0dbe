#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
from pathlib import Path

import toml

# 获取项目根目录
# 或使用绝对路径，指到项目根目录为止，例如 windows：BasePath = D:\Project\ccontrol-test-engine
WORKSPACE_PATH = Path(__file__).resolve().parent.parent.parent.parent.parent.parent
CWD_PATH = Path.cwd()


def get_user_mode():
    """
    获取用户模式

    Returns:
        bool: True 为用户模式，False 为开发模式
    """
    project_toml_path = Path.joinpath(WORKSPACE_PATH, "pyproject.toml")
    if os.path.exists(project_toml_path):
        with open(project_toml_path, "r", encoding="utf-8") as f:
            project_toml = toml.load(f)
            project_name = project_toml.get("project", {}).get("name")
            if project_name is None:
                raise AssertionError("Project name is not found in pyproject.toml")
            if "ccontrol-test-engine" in project_name:
                return False
    return True


USER_MODE = get_user_mode()

# 基础路径
BASE_PATH = CWD_PATH if USER_MODE else WORKSPACE_PATH

# 日志文件路径
LOG_PATH = Path.joinpath(BASE_PATH, "logs")

# 代码生成路径
GEN_PATH = Path.joinpath(BASE_PATH, "gen")
GEN_AGENT_RPC_PATH = Path.joinpath(GEN_PATH, "agent_rpc")
if not os.path.exists(GEN_AGENT_RPC_PATH):
    os.makedirs(GEN_AGENT_RPC_PATH)
if not os.path.isdir(GEN_AGENT_RPC_PATH):
    raise AssertionError("GenAgentRPCPath is not a directory")


if __name__ == "__main__":
    print(f"getcwd: {os.getcwd()}")
    print(f"USER_MODE: {USER_MODE}")
    print(f"WORKSPACE_PATH: {WORKSPACE_PATH}")
    print(f"CWD_PATH: {CWD_PATH}")
    print(f"BASE_PATH: {BASE_PATH}")
    print(f"GEN_AGENT_RPC_PATH: {GEN_AGENT_RPC_PATH}")
