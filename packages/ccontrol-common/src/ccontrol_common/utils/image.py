import base64
from io import BytesIO
from ccontrol_common.core.http_client import do_get
from typing import Union, Tuple, Literal
from urllib.parse import urlparse

from PIL.Image import Image
from PIL.Image import open as open_image
from ccontrol_common.core.log import log


# 定义支持的输出格式类型
OutputFormat = Literal["PIL", "bytes", "base64"]
# 定义图像源类型
ImageSourceType = Literal["PIL", "bytes", "base64", "url", "path"]


class ImageUtil:
    @staticmethod
    def get_type(image: Union[Image, bytes, str]) -> ImageSourceType:
        """获取图像数据的类型

        Args:
            image: 输入图像，支持 PIL.Image、bytes、base64字符串、URL 或文件路径

        Returns:
            返回图像数据的类型：'PIL'、'bytes'、'base64'、'url' 或 'path'

        Raises:
            ValueError: 当输入类型无效时
        """
        if not image:
            raise ValueError("Image cannot be None or empty")

        if isinstance(image, Image):
            return "PIL"

        if isinstance(image, bytes):
            return "bytes"

        if isinstance(image, str):
            # 检查是否为 base64
            if image.startswith("data:image"):
                return "base64"

            # 检查是否为 URL
            parsed = urlparse(image)
            if parsed.scheme and parsed.netloc:
                return "url"

            # 默认作为文件路径
            return "path"

        raise ValueError(f"Unsupported image type: {type(image)}")

    @staticmethod
    def verify_image(image: Image) -> Tuple[bool, str]:
        """验证图像是否完整

        Returns:
            Tuple[bool, str]: (是否完整, 错误信息)
        """
        try:
            # 尝试加载图像数据
            image.load()
            return True, ""
        except (IOError, OSError) as e:
            return False, str(e)

    @staticmethod
    def convert(
        image: Union[Image, bytes, str],
        output_format: OutputFormat = "PIL",
        *,
        encoding_format: str = "JPEG",
    ) -> Union[Image, bytes, str]:
        """通用的图像格式转换方法

        Args:
            image: 输入图像，支持以下类型：
                - PIL.Image: 图像对象
                - bytes: 图像二进制数据
                - str: base64字符串、文件路径或URL
            output_format: 输出格式，支持：
                - 'PIL': 返回 PIL.Image 对象
                - 'bytes': 返回图像二进制数据
                - 'base64': 返回 base64 字符串
            encoding_format: 当转换为 bytes 或 base64 时使用的图像编码格式，默认为 'JPEG'

        Returns:
            根据 output_format 参数返回相应格式的图像数据

        Raises:
            ValueError: 当输入图像无效或格式转换失败时
        """
        try:
            # 第一步：统一转换为 PIL.Image
            pil_image = ImageUtil._to_pil(image)

            # 第二步：根据目标格式进行转换
            if output_format == "PIL":
                return pil_image
            elif output_format == "bytes":
                return ImageUtil._to_bytes(pil_image, encoding_format)
            elif output_format == "base64":
                return ImageUtil._to_base64(pil_image, encoding_format)
            else:
                raise ValueError(f"Unsupported output format: {output_format}")
        except Exception as e:
            log.error(f"图像转换失败: {str(e)}")
            raise ValueError(f"Image conversion failed: {str(e)}") from e

    @staticmethod
    def _to_pil(image: Union[Image, bytes, str]) -> Image:
        """将输入转换为 PIL.Image 对象"""
        if isinstance(image, Image):
            return image
        if isinstance(image, bytes):
            return open_image(BytesIO(image))
        if isinstance(image, str):
            # 尝试解析为 URL
            parsed = urlparse(image)
            if parsed.scheme and parsed.netloc:
                response = do_get(image, timeout=30)
                response.raise_for_status()
                return open_image(BytesIO(response.content))

            # 尝试解析为 base64
            if image.startswith("data:image"):
                # 移除 data URI scheme
                base64_data = image.split(",")[1]
                image_data = base64.b64decode(base64_data)
                return open_image(BytesIO(image_data))

            # 尝试作为文件路径打开
            try:
                return open_image(image)
            except Exception as e:
                raise ValueError(f"Invalid image string format: {str(e)}") from e

        raise ValueError("Unsupported image type or format")

    @staticmethod
    def _to_bytes(image: Image, encoding_format: str = "JPEG") -> bytes:
        """将 PIL.Image 转换为字节数据"""
        if image.mode == "RGBA" and encoding_format.upper() == "JPEG":
            image = image.convert("RGB")

        buffered = BytesIO()
        save_options = ImageUtil._get_save_options(encoding_format)
        image.save(buffered, format=encoding_format.upper(), **save_options)
        return buffered.getvalue()

    @staticmethod
    def _to_base64(image: Image, encoding_format: str = "JPEG") -> str:
        """将 PIL.Image 转换为 base64 字符串"""
        image_bytes = ImageUtil._to_bytes(image, encoding_format)
        base64_data = base64.b64encode(image_bytes).decode()
        return f"data:image/{encoding_format.lower()};base64,{base64_data}"

    @staticmethod
    def _get_save_options(encoding_format: str) -> dict:
        """获取图像保存选项"""
        save_options = {}
        if encoding_format.upper() == "JPEG":
            save_options.update({"quality": 85, "optimize": True})
        elif encoding_format.upper() == "PNG":
            save_options["optimize"] = True
        return save_options

    # 为了向后兼容，保留但标记为过时
    @staticmethod
    def convert_to_image(image: Union[Image, bytes, str, None]) -> Image:
        """将图片转换为 PIL 图片"""
        if image is None:
            raise ValueError("Image cannot be None")
        return ImageUtil.convert(image, output_format="PIL")  # type: ignore

    @staticmethod
    def convert_to_base64(image: Union[Image, bytes, str, None], encoding_format: str = "JPEG") -> str:
        """PIL图片转base64"""
        return ImageUtil.convert(image, output_format="base64", encoding_format=encoding_format)  # type: ignore
