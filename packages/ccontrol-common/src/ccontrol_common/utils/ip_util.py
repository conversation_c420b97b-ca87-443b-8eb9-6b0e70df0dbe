import socket

from ccontrol_common.core import log


def get_local_ip():
    try:
        # 创建一个 socket 对象
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # 连接一个外部服务器（不需要真实连接）
        s.connect(("*******", 80))
        # 获取本地 IP
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception as e:
        log.exception(f"{e}")
        return "127.0.0.1"


if __name__ == "__main__":
    # 使用方法
    local_ip = get_local_ip()
    print(f"本机IP地址: {local_ip}")
