"""
子线程类

- 对线程的一层封装
- 可用于立即终止线程
"""

import ctypes
import inspect
import threading
from typing import Optional, Callable


class StoppableThread(threading.Thread):
    """子线程类"""

    def __init__(self, fun: Callable, thread_name: Optional[str] = None):
        threading.Thread.__init__(self)
        if thread_name:
            self.name = thread_name
        self.__fun = fun

    def run(self):
        """启动线程"""
        self.__fun()

    def stop_thread(self):
        """终止线程"""
        stop_thread(self)


def async_raise(tid, exc_type):
    """raises the exception, performs cleanup if needed"""
    tid = ctypes.c_long(tid)

    if not inspect.isclass(exc_type):
        exc_type = type(exc_type)

    return ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exc_type))


def stop_thread(thread: threading.Thread):
    """停止线程"""
    res = async_raise(thread.ident, SystemExit)

    if res == 0:
        raise ValueError("invalid thread id")
    elif res > 1:
        async_raise(thread.ident, None)
    elif res < 1:
        raise SystemError("PyThreadState_setAsyncExc failed")
