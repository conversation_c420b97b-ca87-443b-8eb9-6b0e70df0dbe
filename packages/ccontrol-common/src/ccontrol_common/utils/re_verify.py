#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re


def search_string(pattern, text) -> bool:
    """
    全字段正则匹配

    :param pattern:
    :param text:
    :return:
    """
    result = re.search(pattern, text)
    if result:
        return True
    else:
        return False


def match_string(pattern, text) -> bool:
    """
    从字段开头正则匹配

    :param pattern:
    :param text:
    :return:
    """
    result = re.match(pattern, text)
    if result:
        return True
    else:
        return False


def is_mobile(text: str) -> bool:
    """
    检查手机号码

    :param text:
    :return:
    """
    return match_string(r"^1[3-9]\d{9}$", text)


def is_wechat(text: str) -> bool:
    """
    检查微信号

    :param text:
    :return:
    """
    return match_string(r"^[a-zA-Z]([-_a-zA-Z0-9]{5,19})+$", text)


def is_qq(text: str) -> bool:
    """
    检查QQ号

    :param text:
    :return:
    """
    return match_string(r"^[1-9][0-9]{4,10}$", text)


def match_markdown_code_block(text: str) -> bool:
    """
    检查是否为markdown代码块
    :param text:
    :return:
    """
    return match_string(r"```[\s\S]*?```", text)


def extract_markdown_code_block_content(content: str):
    """
    提取markdown代码块
    :param content: 原始内容
    :return: 代码块内容
    """
    pattern = r"```[a-z]*\n([\s\S]*?)\n```"
    match = re.search(pattern, content)

    if match:
        content = match.group(1)

    return content


if __name__ == "__main__":
    print(extract_markdown_code_block_content("  ```python\nprint('hello')\n```"))
    print(extract_markdown_code_block_content("```python\nprint('hello')\n```"))
