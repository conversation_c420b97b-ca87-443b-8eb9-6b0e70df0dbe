from urllib.parse import urlparse, parse_qs
from typing import Optional


def get_query_param(url: str, key: str) -> Optional[str]:
    """
    从URL中提取指定query参数的value

    Args:
        url: 要解析的URL字符串
        key: 要提取的query参数key

    Returns:
        参数对应的value值，如果不存在则返回None
    """

    try:
        parsed = urlparse(url)
        query_params = parse_qs(parsed.query)
        return query_params.get(key, [None])[0]
    except Exception:
        return None
