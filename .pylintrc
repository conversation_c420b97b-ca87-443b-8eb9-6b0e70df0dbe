[MASTER]
# 递归查找 Python 模块
recursive=yes

# 忽略的目录（与 .gitignore 保持一致）
ignore=CVS,__pycache__,.git,logs,gen,experimental,.env,.venv,env,venv,ENV,dist,build,*.egg-info,docs/_build/,.pytest_cache/,.mypy_cache/,.idea/,.vscode/,.tox/,.coverage/,htmlcov/

# 忽略的文件
ignore-patterns=.*\.pyc$,.*\.pyo$,.*\.pyd$,.*\.so$,.*\.egg$,.*\.egg-info$

[MESSAGES CONTROL]
# 禁用一些不必要的警告
disable=C0111,  # missing-docstring
        C0103,  # invalid-name
        C0303,  # trailing-whitespace
        W0511,  # fixme
        R0903,  # too-few-public-methods
        R0913,  # too-many-arguments
        R0914,  # too-many-locals
        W0621,  # redefined-outer-name - 这个规则会与 pytest fixtures 冲突
        W0613,  # unused-argument - pytest fixtures 可能不会直接使用参数
        W0612,  # unused-variable - 有时测试中的变量看起来未使用
        E1101   # no-member - 针对 pytest.mark 等动态属性

[FORMAT]
# 最大行长度
max-line-length=120

[BASIC]
# 好的变量名长度
good-names=i,j,k,ex,Run,_,id,pk,x,y

[REPORTS]
# 输出格式
output-format=text
reports=no
score=no 
