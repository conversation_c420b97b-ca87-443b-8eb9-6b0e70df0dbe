repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.2
    hooks:
      - id: ruff
        types_or: [python, pyi, jupyter]
        args: [--fix]
      - id: ruff-format
        types_or: [python, pyi, jupyter]
  - repo: local
    hooks:
      - id: uv-lock
        name: UV lock dependencies
        entry: bash -c 'uv pip freeze --exclude-editable > requirements.txt'
        language: system
        pass_filenames: false
        stages: [commit]
      - id: uv-sync
        name: UV sync dependencies
        entry: uv pip sync requirements.txt
        language: system
        pass_filenames: false
      # - id: pylint
      #   name: pylint
      #   entry: pylint --disable=C,R,W,F --enable=E packages
      #   language: system
      #   pass_filenames: false
      #   types: [python]
