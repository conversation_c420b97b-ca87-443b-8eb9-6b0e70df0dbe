import subprocess
from enum import Enum
from http import HTTPStatus
from typing import List, Optional, Any
from pathlib import Path
import re
import tomli
import typer
import configparser
from rich.console import Console
from rich.table import Table
from rich.progress import track
from rich.prompt import Prompt, Confirm
from pydantic import BaseModel, Field

# 定义统一的版本号模式
VERSION_NUM_PATTERN = r"\d+\.\d+\.\d+"
VERSION_PRERELEASE_PATTERN = r"(?:-(alpha|beta|rc)(?:\.\d+)?)?"
VERSION_PATTERN = re.compile(f"^{VERSION_NUM_PATTERN}{VERSION_PRERELEASE_PATTERN}$")


class ErrorModel(BaseModel):
    """统一错误响应模型"""

    code: int = Field(description="错误码")
    message: str = Field(description="错误消息")
    data: Optional[Any] = Field(default=None, description="错误详情数据")


class BaseError(Exception):
    """基础错误类"""

    def __init__(self, code: int, message: str, data: Optional[Any] = None):
        self.error = ErrorModel(code=code, message=message, data=data)
        super().__init__(message)


class PyPIError(BaseError):
    """PyPI 相关错误基类"""

    def __init__(self, message: str, data: Optional[Any] = None):
        super().__init__(code=HTTPStatus.BAD_REQUEST, message=message, data=data)


class VersionError(PyPIError):
    """版本号格式错误"""

    def __init__(self, version: str):
        super().__init__(
            message=f"无效的版本号格式 '{version}'。必须是以下格式之一:\n- 正式版: X.Y.Z (例如: 1.0.0)\n- 快照版: X.Y.Z-alpha.N 或 X.Y.Z-beta.N 或 X.Y.Z-rc.N (例如: 1.0.0-alpha.1)\n注意: 快照版本使用连字符'-'而不是点'.'来分隔版本号和预发布标识符"
        )


class PackageNotFoundError(PyPIError):
    """包不存在错误"""

    def __init__(self, package: str):
        super().__init__(message=f"未找到包 '{package}'")


class PublishError(PyPIError):
    """发布错误"""

    def __init__(self, package: str, error: str):
        super().__init__(message=f"发布包 '{package}' 失败: {error}")


class PyPIEnv(str, Enum):
    """PyPI 环境"""

    CVTE_SNAPSHOT = "cvte-snapshot"
    CVTE_RELEASE = "cvte-release"


class PackageInfo(BaseModel):
    """包信息"""

    name: str = Field(description="包名")
    path: Path = Field(description="包路径")
    version: str = Field(description="版本号")


class PublishResult(BaseModel):
    """发布结果"""

    success: bool = Field(description="是否成功")
    package: str = Field(description="包名")
    version: str = Field(description="版本号")
    error: Optional[str] = Field(default=None, description="错误信息")


class PyPICredentials(BaseModel):
    """PyPI 凭据模型"""

    username: Optional[str] = Field(default=None, description="用户名")
    password: Optional[str] = Field(default=None, description="密码")
    token: Optional[str] = Field(default=None, description="令牌")


app = typer.Typer(name="pypi", help="CControl PyPI 包管理工具", add_completion=True)

console = Console()


def version_callback(value: bool):
    """版本号回调"""
    if value:
        manager = PyPIManager(PyPIEnv.CVTE_SNAPSHOT)
        version = manager.get_version()
        console.print(f"当前版本: [green]{version}[/green]")
        raise typer.Exit()


@app.callback(invoke_without_command=True)
def main(
    ctx: typer.Context,
    version: bool = typer.Option(
        False,
        "--version",
        "-V",
        help="显示当前版本",
        callback=version_callback,
        is_eager=True,
    ),
):
    """CControl PyPI 包管理工具"""
    if ctx.invoked_subcommand is None and not version:
        interactive()


@app.command()
def list(env: PyPIEnv = typer.Option(PyPIEnv.CVTE_SNAPSHOT, "--env", "-e", help="发布环境")):
    """列出所有可用的包"""
    manager = PyPIManager(env)
    packages = manager.get_packages()

    table = Table(title="可用的包")
    table.add_column("包名", style="cyan")
    table.add_column("版本", style="magenta")
    table.add_column("路径", style="green")

    for package in packages:
        table.add_row(package.name, package.version, str(package.path))

    console.print(table)


@app.command()
def bump(
    new_version: str = typer.Argument(..., help="新版本号"),
    yes: bool = typer.Option(False, "--yes", "-y", help="跳过确认"),
):
    """更新所有包的版本号"""
    manager = PyPIManager(PyPIEnv.CVTE_SNAPSHOT)
    try:
        current_version = manager.get_version()
        console.print(f"[blue]当前版本: {current_version}[/blue]")

        if not yes and not typer.confirm(f"确定要将版本从 {current_version} 更新为 {new_version} ?"):
            raise typer.Abort()

        manager.update_all_versions(new_version)
        console.print(f"[green]已更新所有包版本到 {new_version}[/green]")
    except VersionError as e:
        console.print(f"[red]错误: {e.error.message}[/red]")
        raise typer.Exit(1)


@app.command()
def publish(
    env: PyPIEnv = typer.Option(PyPIEnv.CVTE_SNAPSHOT, "--env", "-e", help="发布环境"),
    package: str = typer.Option(None, "--package", "-p", help="指定要发布的包名，不指定则发布所有包"),
    yes: bool = typer.Option(False, "--yes", "-y", help="跳过确认"),
):
    """发布包到PyPI"""
    manager = PyPIManager(env)
    version = manager.get_version()
    packages = manager.get_packages()

    if package:
        packages = [p for p in packages if p.name == package]
        if not packages:
            console.print(f"[red]错误: 未找到包 {package}[/red]")
            raise typer.Exit(1)

    if not yes:
        msg = f"确定要发布{'所有' if not package else package}包 (版本 {version}) 到 {env} ?"
        if not typer.confirm(msg):
            raise typer.Abort()

    results = []
    for pkg in track(packages, description="发布进度"):
        result = manager.publish_package(pkg)
        results.append(result)

    # 显示结果
    table = Table(title="发布结果")
    table.add_column("包名", style="cyan")
    table.add_column("版本", style="magenta")
    table.add_column("状态", style="green")
    table.add_column("错误信息", style="red")

    for result in results:
        table.add_row(
            result.package,
            result.version,
            "✓" if result.success else "✗",
            result.error or "",
        )

    console.print(table)

    # 如果有失败的包，返回错误码
    if any(not r.success for r in results):
        raise typer.Exit(1)


@app.command()
def build(package: str = typer.Option(None, "--package", "-p", help="指定要构建的包名，不指定则构建所有包")):
    """构建包"""
    manager = PyPIManager(PyPIEnv.CVTE_SNAPSHOT)
    packages = manager.get_packages()

    if package:
        packages = [p for p in packages if p.name == package]
        if not packages:
            console.print(f"[red]错误: 未找到包 {package}[/red]")
            raise typer.Exit(1)

    for pkg in track(packages, description="构建进度"):
        try:
            manager.build_package(pkg)
            console.print(f"[green]构建成功: {pkg.name}[/green]")
        except subprocess.CalledProcessError as e:
            console.print(f"[red]构建失败: {pkg.name}[/red]")
            console.print(f"[red]错误: {e}[/red]")
            raise typer.Exit(1)


@app.command()
def interactive():
    """交互式模式"""
    console.print("\n[bold cyan]欢迎使用 CControl PyPI 包管理工具[/bold cyan] 🚀\n")

    # 环境选择
    console.print("[yellow]1. 请选择发布环境:[/yellow]")
    console.print("   [dim]- cvte-snapshot: 快照版本环境[/dim]")
    console.print("   [dim]- cvte-release: 正式版本环境[/dim]")
    env = Prompt.ask("发布环境", choices=["cvte-snapshot", "cvte-release"], default="cvte-snapshot")
    env = PyPIEnv(env)

    manager = PyPIManager(env)
    packages = manager.get_packages()

    # 显示可用的包
    console.print("\n[yellow]2. 当前可用的包:[/yellow]")
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("序号", style="cyan", justify="center")
    table.add_column("包名", style="green")
    table.add_column("当前版本", style="blue")
    table.add_column("路径", style="dim")

    for i, package in enumerate(packages, 1):
        table.add_row(
            str(i),
            package.name,
            package.version,
            str(package.path.relative_to(manager.root)),
        )

    console.print(table)

    # 选择操作
    console.print("\n[yellow]3. 请选择要执行的操作:[/yellow]")
    console.print("   [dim]1. 更新版本 - 更新所有包的版本号[/dim]")
    console.print("   [dim]2. 构建 - 构建选定的包[/dim]")
    console.print("   [dim]3. 发布 - 发布包到 PyPI[/dim]")
    console.print("   [dim]4. 退出程序[/dim]")

    actions = ["更新版本", "构建", "发布", "退出"]
    action_index = int(Prompt.ask("操作选择", choices=["1", "2", "3", "4"], default="1"))
    action = actions[action_index - 1]

    if action == "退出":
        console.print("\n[cyan]感谢使用，再见！👋[/cyan]")
        raise typer.Exit()

    # 选择包
    console.print("\n[yellow]4. 选择要处理的包:[/yellow]")
    if Confirm.ask("是否处理所有包?", default=True):
        selected_packages = packages
    else:
        while True:
            package_index = int(
                Prompt.ask(
                    "请输入包的序号",
                    choices=[str(i) for i in range(1, len(packages) + 1)],
                    default="1",
                )
            )
            if 1 <= package_index <= len(packages):
                break
            console.print("[red]❌ 无效的序号，请重新输入[/red]")
        selected_packages = [packages[package_index - 1]]

    # 执行操作
    try:
        if action == "构建":
            console.print("\n[yellow]开始构建包...[/yellow]")
            for pkg in track(selected_packages, description="构建进度"):
                manager.build_package(pkg)
                console.print(f"[green]✓ 构建成功: {pkg.name}[/green]")

        elif action == "发布":
            console.print("\n[yellow]开始发布包...[/yellow]")
            results = []
            for pkg in track(selected_packages, description="发布进度"):
                result = manager.publish_package(pkg)
                results.append(result)

            # 显示结果
            table = Table(title="发布结果", show_header=True, header_style="bold magenta")
            table.add_column("包名", style="cyan")
            table.add_column("版本", style="blue")
            table.add_column("状态", style="green")
            table.add_column("错误信息", style="red")

            for result in results:
                table.add_row(
                    result.package,
                    result.version,
                    "✓" if result.success else "✗",
                    result.error or "",
                )

            console.print(table)

        elif action == "更新版本":
            console.print("\n[yellow]更新版本操作说明:[/yellow]")
            current_version = manager.get_version()
            suggested_version = get_suggested_version(current_version)
            console.print(f"[blue]当前版本: {current_version}[/blue]")
            console.print("   [dim]- 正式版格式: X.Y.Z (例如: 1.0.0)[/dim]")
            console.print("   [dim]- 快照版格式: X.Y.Z-alpha.N 或 X.Y.Z-beta.N 或 X.Y.Z-rc.N (例如: 1.0.0-alpha.1)[/dim]")

            while True:
                new_version = Prompt.ask("请输入新版本号", default=suggested_version)
                try:
                    if VERSION_PATTERN.match(new_version):
                        break
                    console.print("[red]❌ 版本号格式错误[/red]")
                except VersionError:
                    console.print("[red]❌ 版本号格式错误[/red]")

            manager.update_all_versions(new_version)
            console.print(f"[green]✓ 已更新所有包版本到 {new_version}[/green]")

        console.print("\n[bold green]操作完成！🎉[/bold green]")

    except (subprocess.CalledProcessError, VersionError) as e:
        console.print(f"\n[red]❌ 错误: {str(e)}[/red]")
        raise typer.Exit(1)


def get_suggested_version(current_version: str) -> str:
    """根据当前版本生成建议的下一个版本号"""
    # 检查是否是预发布版本 (alpha|beta|rc)
    pre_release_match = re.match(r"^(\d+\.\d+\.\d+)-(alpha|beta|rc)(\.(\d+))?$", current_version)
    if pre_release_match:
        base_version, pre_type, _, pre_num = pre_release_match.groups()
        if pre_num is None:
            return f"{base_version}-{pre_type}.1"
        return f"{base_version}-{pre_type}.{int(pre_num) + 1}"

    # 正式版本，增加最后一位
    version_parts = current_version.split(".")
    version_parts[-1] = str(int(version_parts[-1]) + 1)
    return ".".join(version_parts)


class PyPIManager:
    """PyPI 包管理器"""

    def __init__(self, env: PyPIEnv):
        self.env = env
        self.root = Path(__file__).parent.parent
        self.packages_dir = self.root / "packages"
        self.apps_dir = self.root / "apps"

    def validate_version(self, version: str) -> bool:
        """验证版本号格式"""
        if not VERSION_PATTERN.match(version):
            raise VersionError(version)
        return True

    def get_version(self) -> str:
        """获取当前版本号"""
        with open(self.root / "pyproject.toml", "rb") as f:
            data = tomli.load(f)
        return data["project"]["version"]

    def update_root_version(self, new_version: str):
        """更新根目录版本号"""
        pyproject = self.root / "pyproject.toml"
        with open(pyproject, "r", encoding="utf-8") as f:
            content = f.read()
        content = re.sub(r'version\s*=\s*["\'].*["\']', f'version = "{new_version}"', content)
        with open(pyproject, "w", encoding="utf-8") as f:
            f.write(content)

    def update_package_version(self, package: PackageInfo):
        """更新单个包的版本号"""
        # 更新 __init__.py
        init_file = package.path / "src" / package.name.replace("-", "_") / "__init__.py"
        if not init_file.exists():
            init_file.parent.mkdir(parents=True, exist_ok=True)
            init_file.write_text(f'__version__ = "{package.version}"\n')
        else:
            content = init_file.read_text()
            if "__version__" in content:
                content = re.sub(
                    r'__version__\s*=\s*["\'].*["\']',
                    f'__version__ = "{package.version}"',
                    content,
                )
            else:
                content = f'__version__ = "{package.version}"\n' + content
            init_file.write_text(content)

        # 更新 pyproject.toml
        pyproject = package.path / "pyproject.toml"
        if pyproject.exists():
            with open(pyproject, "r", encoding="utf-8") as f:
                content = f.read()
            content = re.sub(r'version\s*=\s*["\'].*["\']', f'version = "{package.version}"', content)
            with open(pyproject, "w", encoding="utf-8") as f:
                f.write(content)

        # 更新 README.md 中的版本号
        readme = package.path / "README.md"
        if readme.exists():
            with open(readme, "r", encoding="utf-8") as f:
                content = f.read()
            # 更新 badge 中的版本号
            content = re.sub(
                f"v{VERSION_NUM_PATTERN}{VERSION_PRERELEASE_PATTERN}",
                f"v{package.version}",
                content,
            )
            # 更新其他可能的版本号引用
            content = re.sub(
                f"version\\s*[=:]\\s*[\"']?{VERSION_NUM_PATTERN}{VERSION_PRERELEASE_PATTERN}[\"']?",
                f'version = "{package.version}"',
                content,
            )
            with open(readme, "w", encoding="utf-8") as f:
                f.write(content)

    def update_all_versions(self, new_version: str):
        """更新所有包的版本号"""
        self.validate_version(new_version)

        # 更新根目录的文件
        self.update_root_version(new_version)

        # 更新根目录的 README.md
        readme = self.root / "README.md"
        if readme.exists():
            with open(readme, "r", encoding="utf-8") as f:
                content = f.read()
            # 更新 badge 中的版本号
            content = re.sub(
                f"v{VERSION_NUM_PATTERN}{VERSION_PRERELEASE_PATTERN}",
                f"v{new_version}",
                content,
            )
            # 更新其他可能的版本号引用
            content = re.sub(
                f"version\\s*[=:]\\s*[\"']?{VERSION_NUM_PATTERN}{VERSION_PRERELEASE_PATTERN}[\"']?",
                f'version = "{new_version}"',
                content,
            )
            with open(readme, "w", encoding="utf-8") as f:
                f.write(content)

        # 更新所有包的版本号
        for package in self.get_packages():
            package.version = new_version
            self.update_package_version(package)

    def get_pypi_credentials(self) -> PyPICredentials:
        """
        从 ~/.pypirc 或 ./.pypirc 文件中读取指定 index 的用户名和密码

        1. 优先读取项目目录下的 .pypirc
        2. 如果不存在，则读取用户主目录下的 ~/.pypirc
        3. 在配置文件中寻找与当前环境匹配的凭据配置

        返回:
            PyPICredentials: 包含用户名、密码和令牌的凭据对象
        """
        config = configparser.ConfigParser()
        credentials = PyPICredentials()

        # 检查项目目录下的 .pypirc
        local_pypirc = self.root / ".pypirc"
        if local_pypirc.exists():
            config.read(local_pypirc)
            creds = self._extract_credentials_from_config(config)
            if creds.username or creds.password or creds.token:
                return creds

        # 检查用户主目录下的 ~/.pypirc
        home_pypirc = Path.home() / ".pypirc"
        if home_pypirc.exists():
            config = configparser.ConfigParser()  # 创建新的配置解析器以避免合并配置
            config.read(home_pypirc)
            creds = self._extract_credentials_from_config(config)
            if creds.username or creds.password or creds.token:
                return creds

        return credentials

    def _extract_credentials_from_config(self, config: configparser.ConfigParser) -> PyPICredentials:
        """从配置解析器中提取与当前环境匹配的凭据"""
        credentials = PyPICredentials()

        # 首先检查环境名称对应的部分
        if self.env.value in config:
            section = config[self.env.value]
            if "username" in section:
                credentials.username = section["username"]
            if "password" in section:
                credentials.password = section["password"]
            if "token" in section:
                credentials.token = section["token"]
            if credentials.username or credentials.password or credentials.token:
                return credentials

        # 检查 distutils 部分下是否有 index-servers 配置
        if "distutils" in config and "index-servers" in config["distutils"]:
            servers = [s.strip() for s in config["distutils"]["index-servers"].split("\n") if s.strip()]

            # 查找匹配当前环境的服务器
            for server in servers:
                if server == self.env.value and server in config:
                    section = config[server]
                    if "username" in section:
                        credentials.username = section["username"]
                    if "password" in section:
                        credentials.password = section["password"]
                    if "token" in section:
                        credentials.token = section["token"]
                    if credentials.username or credentials.password or credentials.token:
                        return credentials

        # 最后检查通用仓库部分
        repo_urls = {
            PyPIEnv.CVTE_SNAPSHOT: "https://pypi.cvte.com/repository/cvte-snapshot/",
            PyPIEnv.CVTE_RELEASE: "https://pypi.cvte.com/repository/cvte-release/",
        }

        for section_name in config.sections():
            if "repository" in config[section_name] and config[section_name]["repository"] == repo_urls.get(self.env):
                section = config[section_name]
                if "username" in section:
                    credentials.username = section["username"]
                if "password" in section:
                    credentials.password = section["password"]
                if "token" in section:
                    credentials.token = section["token"]
                if credentials.username or credentials.password or credentials.token:
                    return credentials

        return credentials

    def build_package(self, package: PackageInfo):
        """构建单个包"""
        # 清理旧的构建文件
        subprocess.run(["rm", "-rf", "dist"], cwd=package.path, check=True)
        subprocess.run(["rm", "-rf", "*.egg-info"], cwd=package.path, check=True)
        # 只构建 wheel 格式，这是 PyPI 推荐的格式
        output_dir = package.path / "dist"
        subprocess.run(
            ["uv", "build", "--wheel", "-o", str(output_dir)],
            cwd=package.path,
            check=True,
        )

    def publish_package(self, package: PackageInfo) -> PublishResult:
        """发布单个包到PyPI"""
        try:
            print(f"\n构建 {package.name}...")
            self.build_package(package)
            print(f"发布 {package.name}...")
            # 检查 dist 目录是否存在 wheel 文件
            dist_dir = package.path / "dist"
            wheel_files = [file for file in dist_dir.glob("*.whl")]
            if not dist_dir.exists() or not wheel_files:
                return PublishResult(
                    success=False,
                    package=package.name,
                    version=package.version,
                    error="未找到 wheel 文件，构建可能失败",
                )

            # 获取凭据信息
            credentials = self.get_pypi_credentials()

            # 构建发布命令
            publish_cmd = ["uv", "publish", "--index", self.env.value]

            # 优先使用用户名和密码
            if credentials.username and credentials.password:
                publish_cmd.extend(["--username", credentials.username])
                publish_cmd.extend(["--password", credentials.password])
            # 如果没有用户名和密码但有 token，则使用 token
            elif credentials.token:
                publish_cmd.extend(["--token", credentials.token])

            # 添加 wheel 文件路径
            wheel_paths = [str(f) for f in wheel_files]
            publish_cmd.extend(wheel_paths)

            # 执行发布命令
            subprocess.run(
                publish_cmd,
                cwd=package.path,
                check=True,
            )
            return PublishResult(success=True, package=package.name, version=package.version)
        except subprocess.CalledProcessError as e:
            return PublishResult(
                success=False,
                package=package.name,
                version=package.version,
                error=str(e),
            )

    def get_packages(self) -> List[PackageInfo]:
        """按依赖顺序返回包信息"""
        packages = [
            self.packages_dir / "ccontrol-common",
            self.packages_dir / "ccontrol-test-engine",
            self.packages_dir / "ccontrol-ai-service",
        ]

        version = self.get_version()
        return [PackageInfo(name=path.name, path=path, version=version) for path in packages]


if __name__ == "__main__":
    app()
