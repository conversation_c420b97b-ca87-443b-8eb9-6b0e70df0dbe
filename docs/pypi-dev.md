# PyPI 发布指南

## 发布流程

### 1. 配置 PyPI 源

复制 `.pypirc.template` 到 `~/.pypirc` 并编辑：

```ini
[distutils]
index-servers =
    snapshot
    release

[snapshot]
repository = https://your-snapshot-pypi-url/simple
username = __token__
password = ${SNAPSHOT_PYPI_TOKEN}

[release]
repository = https://your-release-pypi-url/simple
username = __token__
password = ${RELEASE_PYPI_TOKEN}
```

### 2. 设置环境变量

```bash
export SNAPSHOT_PYPI_TOKEN="your-snapshot-token"
export RELEASE_PYPI_TOKEN="your-release-token"
```

### 3. 发布包

#### 交互式模式（推荐）

使用交互式模式可以更方便地进行包管理：

```bash
python scripts/pypi.py interactive
```

交互式模式提供以下功能：
1. 选择发布环境（snapshot/release）
2. 查看当前可用的包及其版本
3. 选择操作：
   - 构建 - 构建选定的包
   - 发布 - 发布包到 PyPI
   - 更新版本 - 更新所有包的版本号
4. 选择处理单个包或所有包

#### 命令行模式

如果需要在脚本中使用，也可以使用命令行模式：

构建包：
```bash
# 构建所有包
python scripts/pypi.py build

# 构建单个包
python scripts/pypi.py build --package ccontrol-common
```

发布包：
```bash
# 发布所有包到 snapshot
python scripts/pypi.py publish --env snapshot

# 发布单个包到 release
python scripts/pypi.py publish --env release --package ccontrol-common
```

更新版本：
```bash
# 更新所有包的版本号
python scripts/pypi.py bump 0.1.14
```

## 注意事项

1. 包发布顺序（按依赖关系）：
   - ccontrol-common
   - ccontrol-test-engine
   - ccontrol-ai-service

2. 版本号格式：
   - 正式版本：`0.1.13`
   - 开发版本：
     * Alpha: `0.1.13-alpha.1`
     * Beta: `0.1.13-beta.1`
     * RC: `0.1.13-rc.1`

3. 版本更新说明：
   - 更新版本号时会自动更新以下文件：
     * 根目录 pyproject.toml
     * 根目录 README.md
     * 各包目录下的：
       - pyproject.toml
       - src/<package_name>/__init__.py
       - README.md
