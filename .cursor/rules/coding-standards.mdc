---
description:
globs:
alwaysApply: false
---
# 编码规范和最佳实践

## 核心原则

1. **严格遵循 SOLID 原则**和行业最佳实践
2. **应用第一性原理思维**解决复杂问题
3. **确保代码可读性和可维护性**
4. **为关键逻辑添加有意义的注释**（解释为什么，而不是做什么）
5. **合理组织代码结构并优化性能**
6. **确保安全性和适当的错误处理**

## 错误处理规范

### 使用统一的错误类型

所有错误处理必须使用 [ccontrol-common.core.errors](mdc:packages/ccontrol-common/src/ccontrol_common/core/errors.py) 中定义的错误类型：

```python
from ccontrol_common.core.errors import (
    RequestError,      # 400 错误
    ParameterError,    # 参数错误
    AuthorizationError, # 401 错误
    ForbiddenError,    # 403 错误
    NotFoundError,     # 404 错误
    ServerError,       # 500 错误
    TaskNotFoundError, # 任务不存在
    TaskStateError,    # 任务状态错误
)
```

### HTTP 状态码规范

- 严格遵循 HTTP 状态码标准规范
- 使用 `http.HTTPStatus` 枚举而不是硬编码数字
- 错误响应必须包含 `code`、`message` 和可选的 `data` 字段

## 数据模型规范

### 使用 Pydantic 定义模型

所有数据模型必须使用 Pydantic 进行定义：

```python
from pydantic import BaseModel, Field
from typing import Optional, List

class TaskModel(BaseModel):
    """任务模型"""
    
    id: str = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    status: str = Field(..., description="任务状态")
    created_at: Optional[datetime] = Field(None, description="创建时间")
```

## 测试规范

### 单元测试

- **所有单元测试必须使用 pytest**
- 测试文件命名：`test_*.py` 或 `*_test.py`
- 测试函数命名：`test_功能描述`
- 使用 fixtures 管理测试数据和依赖

```python
import pytest
from ccontrol_test_engine.service.task import TaskService

@pytest.fixture
def task_service():
    return TaskService()

def test_create_task_success(task_service):
    """测试成功创建任务"""
    # 测试逻辑
    pass
```

## 包管理规范

### UV Workspace

- 使用 UV 作为 Python 包管理器
- 启用 UV workspace 进行多包管理
- 依赖定义在各包的 `pyproject.toml` 中
- 使用 `uv sync --dev` 安装开发依赖

### 版本管理

- 所有包共用根目录 [pyproject.toml](mdc:pyproject.toml) 中的版本号
- 版本号格式：`主版本.次版本.修订版本[-预发布标识.预发布版本]`

## 代码质量工具

### Ruff 配置

- 行长度限制：255 字符（在 [pyproject.toml](mdc:pyproject.toml) 中配置）
- 启用 E（错误）和 F（致命错误）检查
- 运行 `ruff check` 进行代码检查
- 运行 `ruff format` 进行代码格式化

### Pre-commit 钩子

- 使用 pre-commit 确保代码质量
- 自动运行 ruff、pytest 等工具
- 提交前自动格式化代码
