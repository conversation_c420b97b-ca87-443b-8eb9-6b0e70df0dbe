---
description:
globs:
alwaysApply: false
---
# 测试引擎架构指南

## 核心架构组件

### 测试引擎核心 (ccontrol-test-engine)

位于 [packages/ccontrol-test-engine/](mdc:packages/ccontrol-test-engine/) 的核心测试引擎包含以下主要模块：

#### 驱动服务 (Driver Service)

- **路径**: [service/driver/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/driver/)
- **功能**: 管理测试设备驱动和连接
- **增强功能**: [enhance/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/driver/enhance/)
  - **AI 增强**: [ai/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/driver/enhance/ai/)
  - **智能代理**: [agents/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/driver/enhance/ai/agents/)

#### 任务管理 (Task Management)

- **路径**: [service/task/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/task/)
- **功能**: 测试任务的创建、执行、监控和管理
- **模型**: [model/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/task/model/)

#### 事件系统 (Event System)

- **路径**: [service/event/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/event/)
- **功能**: 处理测试过程中的各种事件
- **组件**:
  - **事件生成器**: [generator/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/event/generator/)
  - **事件模型**: [model/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/event/model/)
  - **系统事件**: [system_event/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/event/system_event/)

#### 测试用例上传

- **路径**: [service/test_case_upload/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/test_case_upload/)
- **功能**: 处理测试用例的上传和管理

## AI 服务集成

### AI 服务包 (ccontrol-ai-service)

位于 [packages/ccontrol-ai-service/](mdc:packages/ccontrol-ai-service/) 的 AI 服务提供智能化测试能力：

#### 智能代理 (Agents)

- **路径**: [agent/](mdc:packages/ccontrol-ai-service/src/ccontrol_ai_service/agent/)
- **功能**: 提供 AI 驱动的测试自动化和决策能力

#### AI 服务

- **路径**: [service/](mdc:packages/ccontrol-ai-service/src/ccontrol_ai_service/service/)
- **功能**: 核心 AI 服务逻辑和接口

#### 通用组件

- **路径**: [common/](mdc:packages/ccontrol-ai-service/src/ccontrol_ai_service/common/)
- **功能**: AI 服务的通用工具和配置

#### 接口定义

- **路径**: [interface/](mdc:packages/ccontrol-ai-service/src/ccontrol_ai_service/interface/)
- **功能**: AI 服务的外部接口定义

## 数据模型架构

### 服务层模型

位于 [apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/model/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/model/)：

- **任务模型**: [task/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/model/task/)
- **Cookie 管理**: [cookies/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/model/cookies/)
- **对象存储**: [object_storage/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/model/object_storage/)

### 核心引擎模型

位于 [packages/ccontrol-test-engine/src/ccontrol_test_engine/model/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/model/)：

- 定义测试引擎的核心数据结构
- 包含任务、事件、驱动等相关模型

## 配置管理

### 服务配置

- **主配置**: [config/config.py](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/config/config.py)
- **日志配置**: [config/uvicorn_log_config.json](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/config/uvicorn_log_config.json)

### 引擎配置

- **核心配置**: [config/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/config/)
- **通用配置**: [config/](mdc:packages/ccontrol-common/src/ccontrol_common/config/)

## Mock 和测试支持

### Mock 数据

- **服务 Mock**: [mock/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/mock/)
- **引擎 Mock**: [mock/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/mock/)
- **AI 服务 Mock**: [mock/](mdc:packages/ccontrol-ai-service/src/ccontrol_ai_service/mock/)

### 测试结构

- **服务测试**: [tests/](mdc:apps/ccontrol-test-engine-service/tests/)
- **引擎测试**: [tests/](mdc:packages/ccontrol-test-engine/tests/)
- **AI 服务测试**: [tests/](mdc:packages/ccontrol-ai-service/tests/)
- **通用库测试**: [tests/](mdc:packages/ccontrol-common/tests/)

## 日志和监控

### 日志管理

- **服务日志**: [logs/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/logs/)
- **引擎日志**: [logs/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/mock/logs/)
- **通用日志**: [core/logs/](mdc:packages/ccontrol-common/src/ccontrol_common/core/logs/)

### 代码生成

- **服务生成**: [gen/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/gen/)
- **引擎生成**: [gen/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/mock/gen/)
- **通用生成**: [core/gen/](mdc:packages/ccontrol-common/src/ccontrol_common/core/gen/)
