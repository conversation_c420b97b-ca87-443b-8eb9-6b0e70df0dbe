# Python Bundler Guidelines

## Core Principles

The Python bundler in [python_bundler.py](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/test_case_upload/python_bundler.py) uses an **exclusion-based approach** for determining local modules:

1. **Exclude modules in exclusion list** - Check `_is_excluded_module()`
2. **Exclude standard library modules** - Check `_is_standard_library_module()`  
3. **Exclude third-party modules** - Check `_is_third_party_module()`
4. **Include remaining .py files** - These are considered local modules

## Key Methods

### `is_local_module(module_name, module_path)`
- **DO NOT** use hardcoded path keywords or module prefixes
- **DO** rely on the exclusion-based logic
- **DO** ensure it works across different project structures

### `collect_imports(file_path, source_code)`
- **DO** use `_is_standard_library_module()` for dynamic standard library detection
- **DO NOT** rely only on hardcoded `builtin_modules` list
- **DO** properly categorize imports into system_imports, from_imports, excluded_imports

### `_is_standard_library_module(module_name)`
- **DO** support Python 3.9+ compatibility
- **DO** use multiple fallback methods for robustness
- **DO** handle namespace packages correctly

## Testing Requirements

When modifying bundler logic:

1. **Test with real modules** from different categories:
   - Standard library: `os`, `sys`, `email.mime.text`, `smtplib`
   - Third-party: `requests`, `pydantic`, `numpy`
   - Local modules: project-specific modules
   - Excluded modules: `ccontrol_*`, `gen.*`

2. **Verify import collection** works correctly:
   - All necessary imports are included in generated bundle
   - No undefined name errors in final code
   - Standard library imports are preserved
   - Local module imports are removed after bundling

3. **Test cross-project scenarios**:
   - Modules in different sys.path directories
   - Complex project structures
   - Virtual environments

## Common Issues to Avoid

1. **Hardcoding project-specific paths or module names**
   ```python
   # BAD
   if 'testsuites' in path or 'ccontrol' in module_name:
   
   # GOOD  
   if self._is_standard_library_module(module_name):
   ```

2. **Missing import dependencies**
   - Always verify email-related imports are included
   - Check that all used objects have corresponding imports

3. **Python version compatibility**
   - Test fallback logic for Python 3.9
   - Handle missing `sys.stdlib_module_names` gracefully

## Bundle Validation Checklist

Before considering bundling complete:

- [ ] All local module source code is included
- [ ] Local module import statements are removed  
- [ ] Standard library imports are preserved
- [ ] No undefined name errors in linter
- [ ] Email-related imports work correctly
- [ ] Generated code passes syntax validation
description:
globs:
alwaysApply: false
---
