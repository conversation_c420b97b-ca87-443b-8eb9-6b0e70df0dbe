---
description:
globs:
alwaysApply: false
---
# API 开发指南

## FastAPI 服务架构

### 主要入口点

- **服务启动**: [main.py](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/main.py)
- **应用注册**: [core/registers/agg_registers.py](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/core/registers/agg_registers.py)
- **配置管理**: [config/config.py](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/config/config.py)

### API 路由结构

```
apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/api/
└── v1/
    ├── __init__.py
    ├── task/          # 任务相关 API
    ├── driver/        # 驱动相关 API
    ├── event/         # 事件相关 API
    └── health/        # 健康检查 API
```

## API 开发规范

### 路由定义

```python
from fastapi import APIRouter, Depends, HTTPStatus
from ccontrol_common.core.errors import TaskNotFoundError, ParameterError
from ccontrol_test_engine_service.model.task import TaskCreateRequest, TaskResponse

router = APIRouter(prefix="/api/v1/tasks", tags=["任务管理"])

@router.post("/", response_model=TaskResponse, status_code=HTTPStatus.CREATED)
async def create_task(
    request: TaskCreateRequest,
    task_service: TaskService = Depends(get_task_service)
) -> TaskResponse:
    """创建新任务"""
    try:
        task = await task_service.create_task(request)
        return TaskResponse.from_task(task)
    except ValueError as e:
        raise ParameterError(str(e))
```

### 请求/响应模型

使用 Pydantic 模型定义 API 的请求和响应结构：

```python
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class TaskCreateRequest(BaseModel):
    """创建任务请求"""
    
    name: str = Field(..., description="任务名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="任务描述")
    config: dict = Field(default_factory=dict, description="任务配置")

class TaskResponse(BaseModel):
    """任务响应"""
    
    id: str = Field(..., description="任务ID")
    name: str = Field(..., description="任务名称")
    status: str = Field(..., description="任务状态")
    created_at: datetime = Field(..., description="创建时间")
    
    @classmethod
    def from_task(cls, task: Task) -> "TaskResponse":
        """从任务对象创建响应"""
        return cls(
            id=task.id,
            name=task.name,
            status=task.status.value,
            created_at=task.created_at
        )
```

### 错误处理

```python
from fastapi import HTTPException
from ccontrol_common.core.errors import BaseError

@router.get("/{task_id}")
async def get_task(task_id: str) -> TaskResponse:
    """获取任务详情"""
    try:
        task = await task_service.get_task(task_id)
        return TaskResponse.from_task(task)
    except TaskNotFoundError as e:
        raise HTTPException(
            status_code=e.error.code,
            detail=e.error.dict()
        )
```

### 依赖注入

```python
from fastapi import Depends
from ccontrol_test_engine.service.task import TaskService

def get_task_service() -> TaskService:
    """获取任务服务实例"""
    return TaskService()

@router.post("/")
async def create_task(
    request: TaskCreateRequest,
    task_service: TaskService = Depends(get_task_service)
):
    """创建任务"""
    pass
```

## 静态文件和文档

### Swagger UI

- 静态文件位置: [static/swagger-ui/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/static/swagger-ui/)
- API 文档访问: `http://localhost:8000/docs`
- ReDoc 访问: `http://localhost:8000/redoc`

### 配置示例

```python
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles

app = FastAPI(
    title="CControl Test Engine API",
    description="测试引擎 API 服务",
    version="0.3.24",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")
```

## 中间件和安全

### 日志中间件

```python
import time
from fastapi import Request
from ccontrol_common.core.log import log

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    log.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.4f}s"
    )
    return response
```

### CORS 配置

```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```
