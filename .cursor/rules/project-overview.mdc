---
description:
globs:
alwaysApply: false
---
# CControl Test Engine Monorepo Overview

## 项目架构

这是一个基于 UV workspace 的 Python 单体仓库，包含测试引擎相关的多个包：

### 核心组件

- **apps/ccontrol-test-engine-service/**: 主要的 FastAPI 服务，提供 REST API
  - 入口文件：[main.py](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/main.py)
  - API 路由：[api/v1/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/api/v1/)
  - 核心逻辑：[core/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/core/)

- **packages/ccontrol-common/**: 通用库，包含共享的错误处理、日志、配置等
  - 错误定义：[core/errors.py](mdc:packages/ccontrol-common/src/ccontrol_common/core/errors.py)
  - 日志配置：[core/log.py](mdc:packages/ccontrol-common/src/ccontrol_common/core/log.py)

- **packages/ccontrol-test-engine/**: 测试引擎核心逻辑
  - 驱动服务：[service/driver/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/driver/)
  - 任务管理：[service/task/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/task/)
  - 事件系统：[service/event/](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/event/)

- **packages/ccontrol-ai-service/**: AI 相关服务
  - 智能代理：[agent/](mdc:packages/ccontrol-ai-service/src/ccontrol_ai_service/agent/)
  - AI 服务：[service/](mdc:packages/ccontrol-ai-service/src/ccontrol_ai_service/service/)

### 配置文件

- [pyproject.toml](mdc:pyproject.toml): 项目配置和依赖管理
- [uv.lock](mdc:uv.lock): 锁定的依赖版本
- [README.md](mdc:README.md): 项目说明和开发指南

### 开发工具

- 使用 UV 作为包管理器
- 使用 Ruff 进行代码格式化和 lint
- 使用 pytest 进行单元测试
- 使用 Pydantic 进行数据模型定义
