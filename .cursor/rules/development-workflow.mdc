---
description:
globs:
alwaysApply: false
---
# 开发工作流程指南

## 环境设置

### 初始化开发环境

```bash
# 1. 创建虚拟环境
uv venv

# 2. 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows

# 3. 安装依赖
uv sync --dev

# 4. 安装 pre-commit 钩子
pre-commit install
```

### 项目配置文件

- **主配置**: [pyproject.toml](mdc:pyproject.toml) - 项目元数据和依赖
- **锁定文件**: [uv.lock](mdc:uv.lock) - 精确的依赖版本
- **开发指南**: [README.md](mdc:README.md) - 项目说明和快速开始

## 开发命令

### 代码质量检查

```bash
# 运行 Ruff 检查
ruff check .

# 自动修复可修复的问题
ruff check --fix .

# 格式化代码
ruff format .

# 运行 pylint 检查
pylint packages/ apps/
```

### 测试执行

```bash
# 运行所有测试
pytest

# 运行特定包的测试
pytest packages/ccontrol-common/tests/
pytest packages/ccontrol-test-engine/tests/
pytest packages/ccontrol-ai-service/tests/
pytest apps/ccontrol-test-engine-service/tests/

# 运行测试并生成覆盖率报告
pytest --cov=ccontrol_common --cov=ccontrol_test_engine --cov=ccontrol_ai_service

# 运行特定测试文件
pytest tests/test_specific_module.py

# 运行特定测试函数
pytest tests/test_module.py::test_function_name
```

### 服务启动

```bash
# 启动测试引擎服务
cd apps/ccontrol-test-engine-service
python -m ccontrol_test_engine_service.main

# 或使用 uvicorn 直接启动
uvicorn ccontrol_test_engine_service.main:app --reload --host 0.0.0.0 --port 8000
```

## Git 工作流程

### 分支管理

```bash
# 创建新功能分支
git checkout -b feature/new-feature-name

# 创建修复分支
git checkout -b fix/bug-description

# 切换到主分支
git checkout master

# 合并分支
git merge feature/new-feature-name
```

### 提交规范

```bash
# 提交格式：<type>(<scope>): <description>
git commit -m "feat(task): add task creation API"
git commit -m "fix(driver): resolve connection timeout issue"
git commit -m "docs(api): update API documentation"
git commit -m "test(task): add unit tests for task service"
git commit -m "refactor(core): improve error handling"
```

### 常用 Git 命令

```bash
# 查看状态
git status

# 查看提交历史（非交互式）
git --no-pager log -n 20 --pretty=format:"%h %ad %s%n%b" --date=short

# 查看文件差异
git diff

# 暂存更改
git add .

# 推送到远程
git push origin feature/branch-name
```

## 包管理和发布

### 依赖管理

```bash
# 添加新依赖
uv add package-name

# 添加开发依赖
uv add --dev package-name

# 更新依赖
uv sync

# 查看依赖树
uv tree
```

### 版本管理

所有包共用根目录 [pyproject.toml](mdc:pyproject.toml) 中的版本号：

```bash
# 更新版本号（手动编辑 pyproject.toml）
# version = "0.3.25"

# 构建包
uv build

# 发布到测试环境
python scripts/publish.py publish-all snapshot

# 发布到生产环境
python scripts/publish.py publish-single release ccontrol-common
```

## 调试和故障排除

### 日志查看

```bash
# 查看服务日志
tail -f apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/logs/app.log

# 查看特定级别的日志
grep "ERROR" apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/logs/app.log
```

### 常见问题解决

1. **依赖冲突**:
   ```bash
   uv sync --refresh
   ```

2. **测试失败**:
   ```bash
   pytest -v --tb=short
   ```

3. **代码格式问题**:
   ```bash
   ruff format .
   ruff check --fix .
   ```

4. **导入错误**:
   - 检查 `PYTHONPATH` 设置
   - 确认包已正确安装：`uv sync`

## API 开发和测试

### 本地 API 测试

```bash
# 启动服务后访问
# API 文档: http://localhost:8000/docs
# ReDoc: http://localhost:8000/redoc

# 使用 curl 测试 API
curl -X GET "http://localhost:8000/api/v1/health"
curl -X POST "http://localhost:8000/api/v1/tasks" \
  -H "Content-Type: application/json" \
  -d '{"name": "test-task", "description": "Test task"}'
```

### 数据库操作

```bash
# 如果使用数据库迁移
# alembic upgrade head
# alembic revision --autogenerate -m "description"
```

## 性能监控

### 性能分析

```bash
# 使用 cProfile 分析性能
python -m cProfile -o profile.stats your_script.py

# 查看性能报告
python -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(10)"
```

### 内存使用监控

```bash
# 使用 memory_profiler
pip install memory-profiler
python -m memory_profiler your_script.py
```
