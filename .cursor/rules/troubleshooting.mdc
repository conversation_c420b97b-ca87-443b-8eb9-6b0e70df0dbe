---
description:
globs:
alwaysApply: false
---
# 故障排除和调试指南

## 常见问题和解决方案

### 环境和依赖问题

#### 1. UV 依赖安装失败

**问题**: `uv sync` 失败或依赖冲突

**解决方案**:
```bash
# 清理缓存并重新安装
uv cache clean
uv sync --refresh

# 如果仍有问题，删除虚拟环境重新创建
rm -rf .venv
uv venv
source .venv/bin/activate
uv sync --dev
```

#### 2. 导入模块错误

**问题**: `ModuleNotFoundError` 或 `ImportError`

**解决方案**:
```bash
# 确认包已安装
uv sync

# 检查 Python 路径
python -c "import sys; print('\n'.join(sys.path))"

# 确认在正确的虚拟环境中
which python
```

**检查文件**:
- [pyproject.toml](mdc:pyproject.toml) - 确认依赖配置正确
- [uv.lock](mdc:uv.lock) - 检查锁定的版本

#### 3. 包版本冲突

**问题**: 不同包要求不兼容的依赖版本

**解决方案**:
```bash
# 查看依赖树找出冲突
uv tree

# 更新到兼容版本
uv add package-name==compatible-version
```

### 服务启动问题

#### 1. FastAPI 服务无法启动

**问题**: 服务启动失败或端口被占用

**检查文件**: [main.py](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/main.py)

**解决方案**:
```bash
# 检查端口占用
lsof -i :8000
netstat -tulpn | grep :8000

# 杀死占用端口的进程
kill -9 <PID>

# 使用不同端口启动
uvicorn ccontrol_test_engine_service.main:app --port 8001
```

#### 2. 配置文件错误

**问题**: 配置加载失败

**检查文件**: 
- [config/config.py](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/config/config.py)
- [config/uvicorn_log_config.json](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/config/uvicorn_log_config.json)

**解决方案**:
```bash
# 验证配置文件语法
python -c "from ccontrol_test_engine_service.config.config import settings; print(settings)"

# 检查环境变量
env | grep CCONTROL
```

### 测试相关问题

#### 1. pytest 测试失败

**问题**: 测试运行失败或找不到测试

**解决方案**:
```bash
# 详细输出查看错误
pytest -v --tb=long

# 运行特定测试
pytest tests/test_specific.py::test_function -v

# 检查测试发现
pytest --collect-only
```

#### 2. 测试覆盖率问题

**问题**: 覆盖率报告不准确

**解决方案**:
```bash
# 生成详细覆盖率报告
pytest --cov=ccontrol_common --cov=ccontrol_test_engine --cov-report=html

# 查看未覆盖的代码
pytest --cov=ccontrol_common --cov-report=term-missing
```

### 代码质量问题

#### 1. Ruff 检查失败

**问题**: 代码格式或 lint 错误

**解决方案**:
```bash
# 查看具体错误
ruff check . --show-source

# 自动修复可修复的问题
ruff check --fix .

# 格式化代码
ruff format .
```

**配置文件**: [pyproject.toml](mdc:pyproject.toml) 中的 `[tool.ruff]` 部分

#### 2. 类型检查错误

**问题**: mypy 或类型相关错误

**解决方案**:
```bash
# 运行类型检查
mypy packages/ apps/

# 忽略特定错误（临时）
# type: ignore
```

### API 和网络问题

#### 1. API 请求失败

**问题**: HTTP 请求返回错误状态码

**检查文件**: [core/errors.py](mdc:packages/ccontrol-common/src/ccontrol_common/core/errors.py)

**调试步骤**:
```bash
# 检查 API 文档
curl http://localhost:8000/docs

# 测试健康检查端点
curl http://localhost:8000/health

# 查看详细错误信息
curl -v http://localhost:8000/api/v1/endpoint
```

#### 2. 数据库连接问题

**问题**: 数据库连接失败

**解决方案**:
```bash
# 检查数据库服务状态
systemctl status mysql  # 或 postgresql

# 测试连接
mysql -h localhost -u user -p database_name

# 检查连接字符串配置
```

### 日志和监控问题

#### 1. 日志文件问题

**问题**: 日志不生成或格式错误

**检查目录**:
- [logs/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/logs/)
- [core/logs/](mdc:packages/ccontrol-common/src/ccontrol_common/core/logs/)

**解决方案**:
```bash
# 检查日志目录权限
ls -la logs/

# 创建日志目录
mkdir -p logs

# 检查日志配置
python -c "from ccontrol_common.core.log import log; log.info('test')"
```

#### 2. 性能问题

**问题**: 服务响应慢或内存使用过高

**调试工具**:
```bash
# 性能分析
python -m cProfile -o profile.stats your_script.py

# 内存使用监控
pip install memory-profiler
python -m memory_profiler your_script.py

# 系统资源监控
top
htop
```

### AI 服务问题

#### 1. AI 代理初始化失败

**问题**: AI 服务无法启动

**检查目录**: [agent/](mdc:packages/ccontrol-ai-service/src/ccontrol_ai_service/agent/)

**解决方案**:
```bash
# 检查 AI 服务依赖
pip list | grep -E "(torch|transformers|openai)"

# 验证 AI 服务配置
python -c "from ccontrol_ai_service.agent import Agent; print('AI service OK')"
```

#### 2. Mock 数据问题

**问题**: Mock 服务返回错误数据

**检查目录**:
- [mock/](mdc:apps/ccontrol-test-engine-service/src/ccontrol_test_engine_service/mock/)
- [mock/](mdc:packages/ccontrol-ai-service/src/ccontrol_ai_service/mock/)

**解决方案**:
```bash
# 验证 Mock 数据格式
python -c "import json; json.load(open('mock/data.json'))"

# 重新生成 Mock 数据
python scripts/generate_mock_data.py
```

## 调试技巧

### 1. 使用 Python 调试器

```python
import pdb; pdb.set_trace()  # 设置断点

# 或使用 ipdb（更友好的界面）
import ipdb; ipdb.set_trace()
```

### 2. 日志调试

```python
from ccontrol_common.core.log import log

log.debug("调试信息")
log.info("一般信息")
log.warning("警告信息")
log.error("错误信息")
log.critical("严重错误")
```

### 3. 环境变量调试

```bash
# 设置调试模式
export DEBUG=true
export LOG_LEVEL=DEBUG

# 查看所有环境变量
printenv | grep CCONTROL
```

### 4. 网络调试

```bash
# 检查网络连接
ping hostname
telnet hostname port
nc -zv hostname port

# 查看网络接口
netstat -i
ip addr show
```

## 获取帮助

### 1. 查看文档

- 项目文档: [README.md](mdc:README.md)
- API 文档: http://localhost:8000/docs
- 代码注释和 docstring

### 2. 社区资源

```bash
# 查看包信息
uv show package-name

# 查看 Python 帮助
python -c "help(module_name)"
```

### 3. 错误报告

创建错误报告时包含以下信息：
- 错误消息和堆栈跟踪
- 重现步骤
- 环境信息（Python 版本、操作系统等）
- 相关配置文件内容
