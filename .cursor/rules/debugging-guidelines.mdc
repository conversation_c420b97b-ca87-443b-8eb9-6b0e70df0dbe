# Debugging Guidelines for Python Bundler

## Common Issues and Solutions

### 1. Missing Import Errors in Generated Bundle

**Symptoms**: `NameError: name 'X' is not defined` in [combined.py](mdc:combined.py)

**Debug Steps**:
1. Check if import is collected in `collect_imports()`:
   ```python
   bundler.system_imports  # For standard library
   bundler.from_imports    # For from imports
   bundler.excluded_imports # For excluded modules
   ```

2. Verify module classification:
   ```python
   bundler._is_standard_library_module('module_name')
   bundler._is_excluded_module('module_name') 
   bundler._is_third_party_module('/path/to/module')
   ```

3. Test import collection manually:
   ```python
   bundler.collect_imports(file_path, source_code)
   ```

### 2. Local Module Not Bundled

**Symptoms**: Local module functions missing from bundle

**Debug Steps**:
1. Check module path resolution:
   ```python
   bundler.resolve_module_path('module.name')
   ```

2. Verify local module detection:
   ```python
   bundler.is_local_module('module.name', '/path/to/module.py')
   ```

3. Check if module is in processed files:
   ```python
   bundler.processed_files.keys()
   bundler.imported_modules
   ```

### 3. Standard Library Module Incorrectly Excluded

**Symptoms**: Standard library imports missing from bundle

**Debug Steps**:
1. Test standard library detection:
   ```python
   bundler._check_stdlib_module('module_name')
   ```

2. Check Python version compatibility:
   ```python
   hasattr(sys, 'stdlib_module_names')  # Python 3.10+
   ```

3. Verify fallback logic for Python 3.9

### 4. Cross-Project Module Issues

**Symptoms**: Modules from other projects not found

**Debug Steps**:
1. Check `sys.path` configuration in `__main__` section
2. Verify project root settings:
   ```python
   bundler.project_root
   ```

3. Test module path resolution across projects

## Debugging Tools

### Enable Verbose Logging
Add debug prints in key methods:
- `is_local_module()` - Module classification decisions
- `collect_imports()` - Import categorization  
- `resolve_module_path()` - Path resolution results

### Quick Test Script Template
```python
import sys
sys.path.insert(0, 'packages/ccontrol-test-engine/src')

from ccontrol_test_engine.service.test_case_upload.python_bundler import PythonBundler

bundler = PythonBundler()

# Test specific module
module_name = 'your.module.name'
module_path = bundler.resolve_module_path(module_name)
is_local = bundler.is_local_module(module_name, module_path)

print(f'Module: {module_name}')
print(f'Path: {module_path}') 
print(f'Is Local: {is_local}')
```

## Validation Checklist

After fixing bundler issues:

1. **Run full bundle generation**
2. **Check generated file syntax**: `python -m py_compile combined.py`
3. **Verify all imports resolve**: Check for undefined names
4. **Test with different module types**: Standard lib, third-party, local
5. **Validate Python 3.9 compatibility**

## Performance Considerations

- Use caching in `_is_standard_library_module()` to avoid repeated checks
- Minimize file system operations in path resolution
- Consider module dependency ordering for optimal bundling
description:
globs:
alwaysApply: false
---
