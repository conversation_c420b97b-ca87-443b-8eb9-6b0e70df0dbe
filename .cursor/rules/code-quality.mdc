# Code Quality and Best Practices

## Python Coding Standards

### Language and Documentation
- **Use Simplified Chinese** when communicating with users
- **Use English** for code blocks, comments, and technical terms
- **Keep technical terms** in their original English form
- **Add meaningful comments** for key logic (explain why, not what)

### SOLID Principles
- **Single Responsibility**: Each class/method should have one reason to change
- **Open/Closed**: Open for extension, closed for modification
- **Liskov Substitution**: Subtypes must be substitutable for base types
- **Interface Segregation**: Clients shouldn't depend on unused interfaces
- **Dependency Inversion**: Depend on abstractions, not concretions

### Error Handling
```python
# GOOD: Specific exception handling
try:
    result = risky_operation()
except SpecificError as e:
    log.error(f"Operation failed: {e}")
    return None

# BAD: Catching all exceptions
try:
    result = risky_operation()
except:
    pass
```

### Type Hints and Validation
```python
# GOOD: Clear type hints
def process_data(items: List[Dict[str, Any]]) -> Optional[ProcessedData]:
    """Process data items and return result."""
    if not items:
        return None
    # ... implementation

# GOOD: Pydantic models for data validation
from pydantic import BaseModel

class UserData(BaseModel):
    name: str
    age: int
    email: str
```

### Testing Requirements
- **All unit tests must use pytest**
- **Test edge cases and error conditions**
- **Use descriptive test names**: `test_should_exclude_standard_library_modules`
- **Mock external dependencies**

### Performance and Security
- **Optimize for readability first**, then performance
- **Use caching** for expensive operations (like standard library checks)
- **Validate input data** before processing
- **Handle file paths securely** - avoid path traversal

### Git and Version Control
- **Use non-interactive Git commands** in scripts:
  ```bash
  git --no-pager log -n 20 --pretty=format:"%h %ad %s%n%b" --date=short
  ```
- **Commit messages should be descriptive**
- **Test changes before committing**

## Project-Specific Guidelines

### Package Management
- **Use `uv` as Python package manager**
- **Enable uv workspace** for multi-package projects
- **Pin dependency versions** in production

### Error Types
- **Use common error types** from `ccontrol-common.core.errors` first
- **HTTP status codes must follow standard specifications**
- **Create custom exceptions** only when necessary

### Module Organization
```python
# GOOD: Clear module structure
from ccontrol_test_engine import GlobalStore, log
from ccontrol_test_engine.service.driver import CControlDriver

# BAD: Wildcard imports
from ccontrol_test_engine import *
```

### Logging
```python
# GOOD: Structured logging with context
log.info(f"Processing file: {file_path}")
log.error(f"Failed to process {module_name}: {error}")

# BAD: Generic logging
print("Something happened")
```
description:
globs:
alwaysApply: false
---
