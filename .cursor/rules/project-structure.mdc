# CControl Test Engine Project Structure

## Main Components

### Core Service
- **Entry Point**: [python_bundler.py](mdc:packages/ccontrol-test-engine/src/ccontrol_test_engine/service/test_case_upload/python_bundler.py) - Python code bundling service
- **Generated Output**: [combined.py](mdc:combined.py) - Bundled test case file

### Project Layout
```
ccontrol-test-engine/
├── packages/ccontrol-test-engine/src/ccontrol_test_engine/
│   └── service/test_case_upload/python_bundler.py
└── combined.py (generated)

ccontrol-test-case/ (external dependency)
└── testsuites/aosp/aosp_system/
    ├── 系统测试1.py (entry file)
    └── yyn_Keywords.py (local module)
```

## Key Dependencies

### Internal Modules
- `ccontrol_test_engine.*` - Core testing framework
- `gen.agent_rpc` - Agent communication

### External Test Cases
- Located in `/Users/<USER>/Project/ccontrol/ccontrol-test-case/`
- Contains test suites and utility modules
- Added to `sys.path` during bundling

## Package Management
- **Package Manager**: `uv` with workspace enabled
- **Python Version**: 3.9+ compatibility required
- **Testing Framework**: pytest for unit tests

## Code Standards
- Follow SOLID principles and industry best practices
- Use English for code blocks and comments
- Keep technical terms in original English form
- Ensure error handling and proper logging
description:
globs:
alwaysApply: false
---
